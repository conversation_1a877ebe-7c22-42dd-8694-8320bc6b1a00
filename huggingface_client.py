"""
Cliente para a Hugging Face Inference API.
"""
import os
import base64
import requests
from config import DEBUG, HUGGINGFACE_API_KEY
from llm_client_base import LLMClientBase

class HuggingFaceClient(LLMClientBase):
    """
    Cliente para a Hugging Face Inference API, que oferece acesso a modelos multimodais.
    """

    # Lista de modelos Hugging Face que suportam visão (modelos verificados e funcionais)
    VISION_MODELS = [
        {
            "id": "llava-hf/llava-1.5-7b-hf",
            "name": "LLaVA 1.5 7B",
            "description": "Modelo multimodal LLaVA com suporte a visão",
            "supports_vision": True,
            "context_length": 4096,
            "category": "vision"
        },
        {
            "id": "microsoft/DialoGPT-medium",
            "name": "DialoGPT Medium",
            "description": "Modelo conversacional da Microsoft (fallback)",
            "supports_vision": False,
            "context_length": 1024,
            "category": "text"
        },
        {
            "id": "microsoft/DialoGPT-small",
            "name": "DialoGPT Small",
            "description": "Modelo conversacional leve da Microsoft (fallback)",
            "supports_vision": False,
            "context_length": 1024,
            "category": "text"
        }
    ]

    def __init__(self, model_id="llava-hf/llava-1.5-7b-hf"):
        """
        Inicializa o cliente Hugging Face.

        Args:
            model_id: ID do modelo Hugging Face a ser usado
        """
        self.api_key = HUGGINGFACE_API_KEY
        self.model = model_id
        self.base_url = "https://api-inference.huggingface.co/models"

        # Verifica se a API key está configurada
        self.is_available = self.api_key is not None and self.api_key != ""

        if not self.is_available and DEBUG:
            print("Aviso: API key da Hugging Face não configurada. Configure HUGGINGFACE_API_KEY no arquivo .env")

    def encode_image_to_base64(self, image_path):
        """
        Codifica uma imagem para base64.

        Args:
            image_path: Caminho para a imagem

        Returns:
            String base64 da imagem
        """
        with open(image_path, "rb") as image_file:
            return base64.b64encode(image_file.read()).decode('utf-8')

    def process_image(self, image_path, prompt=None):
        """
        Processa uma imagem com a Hugging Face Inference API.

        Args:
            image_path: Caminho para a imagem
            prompt: Prompt opcional para enviar junto com a imagem

        Returns:
            Resposta da Hugging Face
        """
        if not self.is_available:
            return "Erro: API da Hugging Face não está disponível. Verifique se a API key está configurada."

        if not os.path.exists(image_path):
            return f"Erro: Arquivo de imagem não encontrado em {image_path}"

        try:
            # Prepara o prompt padrão se não for fornecido
            if not prompt:
                prompt = """
                Analise a imagem que contém uma questão de prova ou teste.

                FORMATO OBRIGATÓRIO DA RESPOSTA:
                Comece SEMPRE com "Resposta correta: [LETRA]) [TEXTO DA ALTERNATIVA]"
                Exemplo: "Resposta correta: A) A força gravitacional"

                Depois da resposta, você pode adicionar uma breve explicação.

                IMPORTANTE:
                - A imagem contém uma questão real que precisa ser respondida agora
                - Responda SEMPRE em português (PT-BR)
                - Seja direto e objetivo, priorizando a resposta correta
                - O tempo é crítico, então foque primeiro na alternativa correta

                Se você não conseguir ver a imagem ou a imagem não estiver clara, informe isso na sua resposta.
                """

            # Carrega a imagem
            with open(image_path, "rb") as image_file:
                image_data = image_file.read()

            if DEBUG:
                print(f"Enviando imagem para Hugging Face com o modelo {self.model}")

            # Prepara os headers
            headers = {
                "Authorization": f"Bearer {self.api_key}"
            }

            # A API da Hugging Face tem diferentes formatos dependendo do modelo
            # Vamos tentar o formato mais comum primeiro
            url = f"{self.base_url}/{self.model}"

            # Para modelos LLaVA e similares
            if "llava" in self.model.lower():
                # Codifica a imagem para base64
                base64_image = base64.b64encode(image_data).decode("utf-8")

                # Prepara o payload
                payload = {
                    "inputs": {
                        "image": base64_image,
                        "prompt": prompt
                    }
                }

                # Faz a requisição
                response = requests.post(url, headers=headers, json=payload)
            else:
                # Para outros modelos, envia a imagem diretamente
                files = {
                    "image": image_data
                }
                data = {
                    "inputs": prompt
                }

                # Faz a requisição
                response = requests.post(url, headers=headers, files=files, data=data)

            # Verifica se a requisição foi bem-sucedida
            if response.status_code == 200:
                # Extrai a resposta
                result = response.json()

                # A resposta pode vir em diferentes formatos dependendo do modelo
                if isinstance(result, list) and len(result) > 0:
                    if isinstance(result[0], dict) and "generated_text" in result[0]:
                        return result[0]["generated_text"]
                    elif isinstance(result[0], str):
                        return result[0]
                elif isinstance(result, dict):
                    if "generated_text" in result:
                        return result["generated_text"]
                    elif "text" in result:
                        return result["text"]

                # Se não conseguiu extrair de forma estruturada, retorna a resposta bruta
                return str(result)
            else:
                error_msg = f"Erro na API da Hugging Face: {response.status_code} - {response.text}"
                if DEBUG:
                    print(error_msg)

                # Verifica diferentes tipos de erro
                if response.status_code == 404:
                    # Modelo não encontrado - tenta usar um modelo alternativo
                    if DEBUG:
                        print(f"Modelo {self.model} não encontrado (404). Tentando modelo alternativo...")

                    # Tenta usar o primeiro modelo disponível diferente do atual
                    for model in self.VISION_MODELS:
                        if model["id"] != self.model:
                            if DEBUG:
                                print(f"Tentando modelo alternativo: {model['id']}")
                            self.model = model["id"]
                            # Tenta novamente com o novo modelo
                            return self.process_image(image_path, prompt)

                    return f"Erro 404: Modelo não encontrado. Todos os modelos alternativos falharam."

                elif response.status_code == 503:
                    if "loading" in response.text.lower():
                        return "O modelo está sendo carregado. Por favor, tente novamente em alguns segundos."
                    else:
                        return f"Erro 503: Serviço Hugging Face indisponível. O sistema alternará automaticamente para outro provedor."

                elif response.status_code == 429:
                    return f"Erro 429: Limite de taxa excedido. O sistema alternará automaticamente para outro provedor."

                return error_msg

        except Exception as e:
            error_msg = f"Erro ao processar imagem com Hugging Face: {str(e)}"
            if DEBUG:
                print(error_msg)
            return error_msg

    def get_available_models(self):
        """
        Retorna a lista de modelos disponíveis na Hugging Face.

        Returns:
            Lista de dicionários com informações dos modelos
        """
        return self.VISION_MODELS

    def set_model(self, model_id):
        """
        Define o modelo a ser usado.

        Args:
            model_id: ID do modelo

        Returns:
            True se o modelo foi definido com sucesso, False caso contrário
        """
        # Verifica se o modelo está disponível
        available_models = self.get_available_models()
        model_ids = [model["id"] for model in available_models]

        if model_id in model_ids:
            self.model = model_id
            return True
        else:
            # Se o modelo não estiver disponível, usa o primeiro da lista
            if available_models:
                self.model = available_models[0]["id"]
                if DEBUG:
                    print(f"Modelo {model_id} não disponível. Usando {self.model} como alternativa.")
                return True
            return False

    def get_provider_name(self):
        """
        Retorna o nome do provedor de LLM.

        Returns:
            Nome do provedor
        """
        return "Hugging Face"
