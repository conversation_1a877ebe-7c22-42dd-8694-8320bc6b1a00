"""
Módulo para processar e organizar texto extraído de imagens.
Foca em identificar perguntas e alternativas, descartando texto irrelevante.
"""
import re
from config import DEBUG

class TextProcessor:
    def __init__(self):
        # Padrões para identificar alternativas de múltipla escolha
        self.alternative_patterns = [
            r'^\s*\(?([A-Ea-e])\)?\s+(.+)$',  # Formato: A) Texto ou (A) Texto
            r'^\s*\(?([A-Ea-e])\)?\)\s+(.+)$',  # Formato: A)) Texto ou (A)) Texto
            r'^\s*\[([A-Ea-e])\]\s+(.+)$',    # Formato: [A] Texto
            r'^\s*([A-Ea-e])[\.:-]\s+(.+)$',  # Formato: A. Texto ou A: Texto
            r'^\s*([A-Ea-e])\s+(.+)$',        # Formato: A Texto (sem separador)
        ]

        # Padrões para identificar perguntas
        self.question_patterns = [
            r'.*\?$',                         # Termina com ponto de interrogação
            r'^\d+\.\s+(.+)$',                # Começa com número e ponto (ex: "1. Texto")
            r'^Questão\s+\d+',                # Começa com "Questão" seguido de número
            r'^Pergunta\s+\d+',               # Começa com "Pergunta" seguido de número
        ]

        # Palavras-chave que indicam o início de uma questão
        self.question_keywords = [
            'analise', 'assinale', 'avalie', 'calcule', 'classifique', 'compare',
            'complete', 'considere', 'defina', 'demonstre', 'descreva', 'determine',
            'discuta', 'elabore', 'enumere', 'escreva', 'especifique', 'explique',
            'identifique', 'indique', 'interprete', 'justifique', 'liste', 'marque',
            'observe', 'ordene', 'organize', 'qual', 'quais', 'quando', 'quanto',
            'que', 'quem', 'relacione', 'responda', 'selecione', 'verdadeiro', 'falso'
        ]

        # Caracteres a serem removidos do texto para melhorar a qualidade
        self.chars_to_remove = '{}[]()<>|\\^~'

        # Palavras a serem ignoradas (irrelevantes para o contexto)
        self.words_to_ignore = [
            'próxima', 'anterior', 'página', 'voltar', 'avançar', 'menu',
            'sair', 'ajuda', 'copyright', 'todos os direitos', 'desenvolvido por',
            'versão', 'carregando', 'processando', 'aguarde', 'clique aqui'
        ]

    def clean_text(self, text):
        """
        Limpa o texto removendo caracteres problemáticos e normalizando espaços.

        Args:
            text: Texto a ser limpo

        Returns:
            Texto limpo
        """
        if not text:
            return ""

        # Remove caracteres problemáticos
        for char in self.chars_to_remove:
            text = text.replace(char, ' ')

        # Normaliza espaços
        text = re.sub(r'\s+', ' ', text)

        # Remove linhas que contêm palavras a serem ignoradas
        lines = text.split('\n')
        filtered_lines = []

        for line in lines:
            should_ignore = False
            for word in self.words_to_ignore:
                if word.lower() in line.lower():
                    should_ignore = True
                    break

            if not should_ignore and line.strip():
                filtered_lines.append(line)

        return '\n'.join(filtered_lines)

    def fix_encoding_issues(self, text):
        """
        Corrige problemas comuns de codificação em texto extraído por OCR.
        Também remove acentos para melhorar a compatibilidade.

        Args:
            text: Texto com possíveis problemas de codificação

        Returns:
            Texto com codificação corrigida e sem acentos
        """
        if not text:
            return ""

        import unicodedata
        import re

        # Função para remover acentos
        def remove_accents(input_str):
            nfkd_form = unicodedata.normalize('NFKD', input_str)
            return ''.join([c for c in nfkd_form if not unicodedata.combining(c)])

        # Primeiro, substitui caracteres problemáticos comuns
        replacements = {
            '�': '',
            '¿': '?',
            '–': '-',
            '—': '-',
            '"': '"',
            '"': '"',
            ''': "'",
            ''': "'",
            '…': '...',
            '•': '*',
            '·': '.',
            '≤': '<=',
            '≥': '>=',
            '≠': '!=',
            '≈': '~=',
            '∞': 'infinito',
            '÷': '/',
            '×': 'x',
            '∑': 'soma',
            '∏': 'produto',
            '∂': 'derivada parcial',
            '∫': 'integral',
            '∴': 'portanto',
            '∵': 'porque',
            '∩': 'intersecao',
            '∪': 'uniao',
            '⊂': 'contido em',
            '⊃': 'contem',
            '⊆': 'contido ou igual a',
            '⊇': 'contem ou igual a',
            '∈': 'pertence a',
            '∉': 'nao pertence a',
            '∅': 'conjunto vazio',
            '∀': 'para todo',
            '∃': 'existe',
            '¬': 'nao',
            '∧': 'e',
            '∨': 'ou',
            '⇒': 'implica',
            '⇔': 'se e somente se',
            '↔': 'se e somente se',
            '→': 'implica',
            '←': 'e implicado por',
        }

        for bad_char, good_char in replacements.items():
            text = text.replace(bad_char, good_char)

        # Corrige problemas comuns de OCR
        text = text.replace('rn', 'm')  # Corrige 'rn' para 'm'
        text = text.replace('cl', 'd')  # Corrige 'cl' para 'd'
        text = text.replace('I1', 'H')  # Corrige 'I1' para 'H'
        text = text.replace('l1', 'h')  # Corrige 'l1' para 'h'
        text = text.replace('0', 'O')  # Corrige '0' para 'O' em contextos de texto
        text = text.replace('1', 'I')  # Corrige '1' para 'I' em contextos de texto

        # Remove caracteres não imprimíveis
        text = ''.join(c for c in text if c.isprintable() or c in ['\n', '\t', ' '])

        # Remove acentos
        text = remove_accents(text)

        # Substitui sequências de caracteres estranhos por espaços
        text = re.sub(r'[^\w\s\.\,\:\;\?\!\(\)\[\]\{\}\-\_\+\=\*\/\\\'\"\@\#\$\%\&\|]+', ' ', text)

        # Remove espaços múltiplos
        text = re.sub(r'\s+', ' ', text)

        # Corrige "Questao" e variações
        text = re.sub(r'[Qq]uest[a-zA-Z]*o', 'Questao', text)
        text = re.sub(r'[Qq]uest[a-zA-Z]*a', 'Questao', text)

        return text

    def extract_question_and_alternatives(self, text):
        """
        Extrai a pergunta e alternativas do texto.

        Args:
            text: Texto extraído da imagem

        Returns:
            Tupla (pergunta, alternativas, texto_completo_organizado)
        """
        if not text:
            return "", [], ""

        # Limpa e corrige o texto
        text = self.clean_text(text)
        text = self.fix_encoding_issues(text)

        lines = text.split('\n')
        question_lines = []
        alternative_lines = []
        current_alternative = None
        in_question = True

        # Processa cada linha
        for line in lines:
            line = line.strip()
            if not line:
                continue

            # Verifica se é uma alternativa
            is_alternative = False
            for pattern in self.alternative_patterns:
                match = re.match(pattern, line)
                if match:
                    # Encontrou uma alternativa
                    letter = match.group(1).upper()
                    text = match.group(2).strip()

                    # Se já estávamos em uma alternativa, adiciona à lista
                    if current_alternative:
                        alternative_lines.append(current_alternative)

                    # Define a nova alternativa atual
                    current_alternative = (letter, text)
                    is_alternative = True
                    in_question = False
                    break

            if is_alternative:
                continue

            # Se não é uma alternativa e estamos em uma alternativa,
            # adiciona à alternativa atual
            if not is_alternative and current_alternative and not in_question:
                letter, text = current_alternative
                current_alternative = (letter, text + " " + line)
            elif in_question:
                # Verifica se é uma linha de pergunta
                is_question_line = False

                # Verifica se termina com ponto de interrogação
                if line.endswith('?'):
                    is_question_line = True

                # Verifica se contém palavras-chave de pergunta
                if not is_question_line:
                    for keyword in self.question_keywords:
                        if keyword.lower() in line.lower():
                            is_question_line = True
                            break

                # Verifica padrões de pergunta
                if not is_question_line:
                    for pattern in self.question_patterns:
                        if re.match(pattern, line):
                            is_question_line = True
                            break

                if is_question_line:
                    question_lines.append(line)

        # Adiciona a última alternativa, se houver
        if current_alternative:
            alternative_lines.append(current_alternative)

        # Organiza as alternativas em ordem alfabética
        alternatives = []
        for letter, text in sorted(alternative_lines):
            alternatives.append(f"{letter}) {text}")

        # Monta a pergunta
        question = " ".join(question_lines)

        # Monta o texto organizado
        organized_text = question + "\n\n"
        for alt in alternatives:
            organized_text += alt + "\n"

        return question, alternatives, organized_text

    def process_text(self, text):
        """
        Processa o texto extraído, organizando em pergunta e alternativas.

        Args:
            text: Texto extraído da imagem

        Returns:
            Texto processado e organizado
        """
        # Limpa e corrige o texto primeiro
        cleaned_text = self.clean_text(text)
        fixed_text = self.fix_encoding_issues(cleaned_text)

        # Extrai pergunta e alternativas
        question, alternatives, organized_text = self.extract_question_and_alternatives(fixed_text)

        if DEBUG:
            print("\n=== TEXTO PROCESSADO ===")
            print(f"Pergunta: {question}")
            print("Alternativas:")
            for alt in alternatives:
                print(f"  {alt}")

        # Se não encontrou uma estrutura de pergunta/alternativas, retorna o texto limpo
        if not question and not alternatives:
            return fixed_text

        return organized_text
