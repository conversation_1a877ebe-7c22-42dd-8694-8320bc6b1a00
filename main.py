#!/usr/bin/env python3
"""
Python Question Helper - Entry Point Principal

Sistema Inteligente de Assistência para Questões
Desenvolvido por BoZolinO

Este é o ponto de entrada principal do aplicativo.
"""

import sys
import os

# Adiciona o diretório src ao path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def main():
    """Função principal do aplicativo."""
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import QSettings
        from src.core.main import QuestionHelper
        from src.core.config import validate_configuration, DEBUG

        # Valida configuração
        issues = validate_configuration()
        if issues and DEBUG:
            print("⚠️ Problemas de configuração encontrados:")
            for issue in issues:
                print(f"  - {issue}")

        # Inicializa a aplicação QApplication primeiro
        app = QApplication(sys.argv)

        # Configurações
        settings = QSettings("<PERSON><PERSON><PERSON><PERSON>", "Python Question Helper")
        settings.setValue("ads/enabled", False)

        # Cria a instância do QuestionHelper
        helper = QuestionHelper()

        # Modifica o método run para não criar uma nova QApplication
        def modified_run():
            helper.ui.show()
            helper.ui.update_status("Pronto - Selecione um modelo e inicie o monitoramento")
            helper.show_welcome_message()
            return app.exec_()

        helper.run = modified_run

        # Executa a aplicação
        print("Iniciando aplicação Python Question Helper...")
        return helper.run()

    except ImportError as e:
        print(f"❌ Erro de importação: {e}")
        print("💡 Execute: python system_health_check.py para verificar a instalação")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Erro inesperado: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
