"""
Módulo para gerenciar anúncios no Python Question Helper.
"""
from PyQt5.QtCore import QUrl, Qt
from PyQt5.QtWidgets import QWidget, QVBoxLayout, QLabel, QSizePolicy
from PyQt5.QtWebEngineWidgets import (QWebEngineView, QWebEnginePage,
                                     QWebEngineProfile)
from config import DEBUG, ADS_CLIENT_ID, ADS_BANNER_SLOT, ADS_RECTANGLE_SLOT, ADS_MULTIPLEX_SLOT

class AdBanner(QWidget):
    """Widget para exibir anúncios do Google AdSense."""

    def __init__(self, parent=None, ad_type="banner", ad_slot="1234567890", ad_client="ca-pub-1234567890123456"):
        """
        Inicializa o widget de anúncios.

        Args:
            parent: Widget pai
            ad_type: <PERSON><PERSON><PERSON> <PERSON> anúncio (banner, rectangle, etc.)
            ad_slot: ID do slot de anúncio do AdSense
            ad_client: ID do cliente do AdSense
        """
        super().__init__(parent)

        self.ad_slot = ad_slot
        self.ad_client = ad_client
        self.ad_type = ad_type

        # Configurações de tamanho com base no tipo de anúncio (tamanhos otimizados para AdSense)
        self.ad_sizes = {
            "banner": (320, 50),      # Banner padrão do AdSense
            "rectangle": (300, 250),  # Retângulo médio do AdSense
            "leaderboard": (728, 90), # Leaderboard padrão do AdSense
            "skyscraper": (160, 600), # Skyscraper padrão do AdSense
            "small": (234, 60),       # Banner pequeno do AdSense
            "display": (300, 250),    # Display padrão do AdSense
            "multiplex": (300, 250)   # Multiplex (autorelaxed) do AdSense
        }

        # Usa o tamanho padrão se o tipo não for reconhecido
        self.ad_size = self.ad_sizes.get(ad_type, self.ad_sizes["rectangle"])

        # Garante que o widget seja visível
        self.setVisible(True)

        # Estilo minimalista sem bordas ou identificadores
        self.setStyleSheet("""
            background-color: transparent;
        """)

        # Configura o layout
        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(0, 0, 0, 0)  # Remove margens para uma aparência mais integrada
        self.layout.setSpacing(0)

        # Em modo de depuração, mostra um placeholder em vez do anúncio real
        if DEBUG:
            self._setup_debug_placeholder()
        else:
            self._setup_ad_webview()

    def _setup_debug_placeholder(self):
        """Configura um placeholder para o anúncio em modo de depuração."""
        placeholder = QLabel("")
        placeholder.setAlignment(Qt.AlignCenter)
        placeholder.setStyleSheet(f"""
            background-color: transparent;
        """)
        placeholder.setFixedSize(*self.ad_size)
        self.layout.addWidget(placeholder)

    def _setup_ad_webview(self):
        """Configura o WebView para exibir o anúncio do AdSense seguindo as diretrizes oficiais."""
        try:
            # Cria o WebView com configurações otimizadas para anúncios
            self.web_view = QWebEngineView(self)  # Use self como parent para garantir visibilidade

            # Define um tamanho fixo para o WebView
            width, height = self.ad_size
            self.web_view.setFixedSize(width, height)

            # Garante que o WebView seja visível - força visibilidade
            self.web_view.setVisible(True)
            self.web_view.show()  # Força a exibição do WebView

            # Estilo com fundo branco para garantir que o anúncio seja visível
            self.web_view.setStyleSheet("""
                background-color: white;
                border: none;
            """)

            # Força o WebView a renderizar com tamanho correto
            self.web_view.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed)

            # Força a atualização do layout
            self.web_view.updateGeometry()

            # Configurações recomendadas pela documentação do Google para WebView
            from PyQt5.QtWebEngineWidgets import QWebEngineSettings
            settings = self.web_view.settings()

            # Habilita JavaScript (necessário para AdSense)
            settings.setAttribute(QWebEngineSettings.JavascriptEnabled, True)

            # Habilita armazenamento local (necessário para AdSense)
            settings.setAttribute(QWebEngineSettings.LocalStorageEnabled, True)

            # Permite que vídeos HTML sejam reproduzidos automaticamente
            settings.setAttribute(QWebEngineSettings.PlaybackRequiresUserGesture, False)

            # Permite acesso a conteúdo remoto
            settings.setAttribute(QWebEngineSettings.LocalContentCanAccessRemoteUrls, True)

            # Permite que cookies de terceiros sejam aceitos (crucial para AdSense)
            # Configuração direta do perfil para aceitar cookies de terceiros
            profile = self.web_view.page().profile()
            profile.setPersistentCookiesPolicy(QWebEngineProfile.AllowPersistentCookies)

            # Habilita cookies através das configurações
            settings.setAttribute(QWebEngineSettings.LocalStorageEnabled, True)

            # Desativa auditoria XSS para melhor compatibilidade
            settings.setAttribute(QWebEngineSettings.XSSAuditingEnabled, False)

            # Desativa página de erro para melhor experiência do usuário
            settings.setAttribute(QWebEngineSettings.ErrorPageEnabled, False)

            # Configura a página com configurações avançadas
            profile = QWebEngineProfile.defaultProfile()

            # Habilita JavaScript e cookies (necessários para o AdSense)
            settings = profile.settings()
            settings.setAttribute(QWebEngineSettings.JavascriptEnabled, True)
            settings.setAttribute(QWebEngineSettings.LocalStorageEnabled, True)
            settings.setAttribute(QWebEngineSettings.PluginsEnabled, True)
            settings.setAttribute(QWebEngineSettings.JavascriptCanOpenWindows, True)
            settings.setAttribute(QWebEngineSettings.JavascriptCanAccessClipboard, True)
            settings.setAttribute(QWebEngineSettings.AllowRunningInsecureContent, True)
            settings.setAttribute(QWebEngineSettings.AllowGeolocationOnInsecureOrigins, True)

            # Cria uma página com o perfil configurado
            page = QWebEnginePage(profile, self.web_view)
            self.web_view.setPage(page)

            # Configura o User-Agent para simular um navegador desktop
            user_agent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
            page.profile().setHttpUserAgent(user_agent)

            # Configura um timeout para verificar se o anúncio foi carregado
            self.ad_load_timeout = 5000  # 5 segundos

            # Conecta sinais para depuração e tratamento de erros
            page.loadFinished.connect(self._on_load_finished)
            # Nota: loadFailed não está disponível em todas as versões do PyQt5
            # Usamos apenas loadFinished para detectar falhas
            page.javaScriptConsoleMessage = self._handle_console_message

            # Configura um timer para verificar se o anúncio foi carregado
            from PyQt5.QtCore import QTimer
            self.load_timer = QTimer()
            self.load_timer.setSingleShot(True)
            self.load_timer.timeout.connect(self._on_load_timeout)
            self.load_timer.start(self.ad_load_timeout)

            # Carrega o HTML do anúncio
            ad_html = self._generate_ad_html()

            # Define uma URL base para resolver recursos relativos (importante para o AdSense)
            base_url = QUrl("https://www.google.com/")

            # Carrega o HTML com a URL base
            self.web_view.setHtml(ad_html, base_url)

            # Adiciona o WebView ao layout
            self.layout.addWidget(self.web_view)

            # Força o layout a atualizar e redesenhar todos os componentes
            self.layout.update()
            self.layout.activate()
            self.setVisible(True)  # Garante que o widget pai também esteja visível
            self.update()          # Força uma atualização do widget

            # Configura um timer para verificar se o anúncio está visível após alguns segundos
            from PyQt5.QtCore import QTimer
            self.visibility_timer = QTimer()
            self.visibility_timer.setSingleShot(True)
            self.visibility_timer.timeout.connect(self._check_ad_visibility)
            self.visibility_timer.start(3000)  # Verifica após 3 segundos

            print(f"WebView configurado com sucesso para anúncio tipo: {self.ad_type}")

        except Exception as e:
            print(f"Erro ao configurar WebView: {str(e)}")
            self._setup_debug_placeholder()

    # Método _on_load_failed removido pois não é compatível com todas as versões do PyQt5

    def _on_load_timeout(self):
        """Callback quando o timeout de carregamento é atingido."""
        try:
            print("Timeout ao carregar anúncio, mas continuando execução")
            # Verificamos se o anúncio foi carregado mesmo após o timeout
            try:
                if hasattr(self, 'web_view') and self.web_view is not None and hasattr(self.web_view, 'page'):
                    self.web_view.page().runJavaScript(
                        "(function() { try { return typeof adsbygoogle !== 'undefined'; } catch(e) { return false; } })()",
                        self._check_adsense_loaded
                    )
                else:
                    print("WebView não está disponível após timeout")
                    self._check_push_result(True)

                    # Tenta recarregar o anúncio após um breve intervalo
                    from PyQt5.QtCore import QTimer
                    reload_timer = QTimer()
                    reload_timer.setSingleShot(True)
                    reload_timer.timeout.connect(self._reload_ad)
                    reload_timer.start(5000)  # Tenta recarregar após 5 segundos
            except Exception as js_error:
                print(f"Erro ao verificar AdSense após timeout: {str(js_error)}")
                # Mesmo em caso de erro, continuamos a execução
                self._check_push_result(True)
        except Exception as e:
            print(f"Erro no manipulador de timeout: {str(e)}")
            # Mesmo em caso de erro, continuamos a execução
            try:
                self._check_push_result(True)
            except:
                print("Erro crítico no manipulador de timeout")

    def _setup_debug_placeholder(self):
        """Configura um placeholder para depuração quando o WebView falha."""
        # Cria um widget de placeholder invisível
        from PyQt5.QtWidgets import QLabel
        placeholder = QLabel("")
        placeholder.setFixedSize(*self.ad_size)
        placeholder.setAlignment(Qt.AlignCenter)
        placeholder.setStyleSheet("""
            background-color: transparent;
        """)

        # Adiciona o placeholder ao layout
        self.layout.addWidget(placeholder)

    def _on_load_finished(self, success):
        """Callback quando a página termina de carregar."""
        try:
            if success:
                print(f"Anúncio carregado com sucesso: {self.ad_type}")
                # Executa JavaScript para verificar se o AdSense foi carregado
                try:
                    self.web_view.page().runJavaScript(
                        "(function() { try { return typeof adsbygoogle !== 'undefined'; } catch(e) { return false; } })()",
                        self._check_adsense_loaded
                    )
                except Exception as js_error:
                    print(f"Erro ao verificar AdSense após carregamento: {str(js_error)}")
                    # Mesmo em caso de erro, continuamos a execução
                    self._check_push_result(True)
            else:
                print(f"Falha ao carregar anúncio: {self.ad_type}")
                # Mesmo em caso de falha, continuamos a execução
                self._check_push_result(True)
        except Exception as e:
            print(f"Erro no manipulador de carregamento: {str(e)}")
            # Mesmo em caso de erro, continuamos a execução
            try:
                self._check_push_result(True)
            except:
                print("Erro crítico no manipulador de carregamento")

    def _check_adsense_loaded(self, result):
        """Verifica se o AdSense foi carregado."""
        try:
            if result:
                print("AdSense carregado com sucesso")
                # Verificamos se os anúncios foram inicializados corretamente
                try:
                    self.web_view.page().runJavaScript(
                        "(function() { try { return document.querySelector('.adsbygoogle').hasAttribute('data-ad-initialized'); } catch(e) { return false; } })()",
                        self._check_ad_initialized
                    )
                except Exception as js_error:
                    print(f"Erro ao verificar inicialização do anúncio: {str(js_error)}")
                    # Em caso de erro, consideramos como sucesso para não interromper a aplicação
                    self._check_push_result(True)
            else:
                print("AdSense não foi carregado")
                # Mesmo se o AdSense não foi carregado, consideramos como sucesso para não interromper a aplicação
                self._check_push_result(True)
        except Exception as e:
            print(f"Erro ao verificar carregamento do AdSense: {str(e)}")
            # Em caso de erro, consideramos como sucesso para não interromper a aplicação
            self._check_push_result(True)

    def _check_ad_initialized(self, result):
        """Verifica se o anúncio foi inicializado."""
        try:
            if result:
                print("Anúncio inicializado corretamente")
            else:
                print("Anúncio não inicializado, mas continuando execução")

            # Em qualquer caso, consideramos como sucesso para não interromper a aplicação
            self._check_push_result(True)
        except Exception as e:
            print(f"Erro ao verificar inicialização do anúncio: {str(e)}")
            # Em caso de erro, consideramos como sucesso para não interromper a aplicação
            self._check_push_result(True)

    def _check_push_result(self, result):
        """Verifica o resultado da inicialização dos anúncios."""
        try:
            if result:
                print("Anúncios inicializados com sucesso")
            else:
                print("Falha ao inicializar anúncios, mas continuando execução")
        except Exception as e:
            print(f"Erro ao verificar resultado da inicialização dos anúncios: {str(e)}")

    def _check_ad_visibility(self):
        """Verifica se o anúncio está visível e tenta corrigir se não estiver."""
        try:
            if hasattr(self, 'web_view') and self.web_view is not None:
                # Verifica se o WebView está visível
                if not self.web_view.isVisible():
                    print("WebView não está visível, tornando-o visível")
                    self.web_view.setVisible(True)

                # Verifica se o WebView tem o tamanho correto
                current_size = self.web_view.size()
                expected_width, expected_height = self.ad_size

                if current_size.width() != expected_width or current_size.height() != expected_height:
                    print(f"WebView tem tamanho incorreto: {current_size.width()}x{current_size.height()}, corrigindo para {expected_width}x{expected_height}")
                    self.web_view.setFixedSize(expected_width, expected_height)

                # Executa JavaScript para verificar se o anúncio está visível
                self.web_view.page().runJavaScript(
                    """
                    (function() {
                        var adElement = document.querySelector('.adsbygoogle');
                        if (adElement) {
                            var style = window.getComputedStyle(adElement);
                            var isVisible = style.display !== 'none' && style.visibility !== 'hidden';
                            var hasContent = adElement.innerHTML.trim() !== '';
                            var hasSize = style.width !== '0px' && style.height !== '0px';

                            // Força a visibilidade do anúncio
                            adElement.style.display = 'block';
                            adElement.style.visibility = 'visible';
                            adElement.style.width = '""" + str(expected_width) + """px';
                            adElement.style.height = '""" + str(expected_height) + """px';
                            adElement.style.backgroundColor = 'white';

                            // Tenta inicializar novamente se necessário
                            if (!hasContent || !hasSize) {
                                try {
                                    (adsbygoogle = window.adsbygoogle || []).push({});
                                    console.log('Anúncio reinicializado durante verificação de visibilidade');
                                } catch (e) {
                                    console.error('Erro ao reinicializar anúncio:', e);
                                }
                            }

                            return {isVisible: isVisible, hasContent: hasContent, hasSize: hasSize};
                        }
                        return {isVisible: false, hasContent: false, hasSize: false};
                    })()
                    """,
                    self._handle_visibility_check
                )
            else:
                print("WebView não está disponível para verificação de visibilidade")
        except Exception as e:
            print(f"Erro ao verificar visibilidade do anúncio: {str(e)}")

    def _handle_visibility_check(self, result):
        """Manipula o resultado da verificação de visibilidade."""
        try:
            if isinstance(result, dict):
                is_visible = result.get('isVisible', False)
                has_content = result.get('hasContent', False)
                has_size = result.get('hasSize', False)

                if is_visible and has_content and has_size:
                    print("Anúncio está visível e tem conteúdo")
                else:
                    print(f"Anúncio pode não estar visível corretamente: visível={is_visible}, conteúdo={has_content}, tamanho={has_size}")

                    # Se o anúncio não estiver visível, tenta recarregar a página
                    if not is_visible or not has_content or not has_size:
                        print("Tentando recarregar o anúncio...")
                        self._reload_ad()
            else:
                print(f"Resultado inesperado da verificação de visibilidade: {result}")
        except Exception as e:
            print(f"Erro ao manipular verificação de visibilidade: {str(e)}")

    def _reload_ad(self):
        """Recarrega o anúncio para tentar corrigir problemas de visibilidade."""
        try:
            if hasattr(self, 'web_view') and self.web_view is not None:
                # Gera o HTML novamente
                ad_html = self._generate_ad_html()

                # Define uma URL base para resolver recursos relativos
                base_url = QUrl("https://www.google.com/")

                # Recarrega o HTML
                self.web_view.setHtml(ad_html, base_url)
                print("Anúncio recarregado")

                # Força a visibilidade do WebView
                self.web_view.setVisible(True)
                self.web_view.show()

                # Força a atualização do layout
                self.web_view.updateGeometry()
                self.layout.update()
                self.update()

                # Configura um timer para verificar se o anúncio está visível após alguns segundos
                from PyQt5.QtCore import QTimer
                self.visibility_timer = QTimer()
                self.visibility_timer.setSingleShot(True)
                self.visibility_timer.timeout.connect(self._check_ad_visibility)
                self.visibility_timer.start(3000)  # Verifica após 3 segundos
            else:
                print("WebView não está disponível para recarregar o anúncio")
                # Tenta criar um novo WebView
                self._setup_ad_webview()
        except Exception as e:
            print(f"Erro ao recarregar anúncio: {str(e)}")
            # Em caso de erro, tenta criar um novo WebView
            try:
                self._setup_ad_webview()
            except Exception as setup_error:
                print(f"Erro ao recriar WebView: {str(setup_error)}")
                self._setup_debug_placeholder()

    def _handle_console_message(self, level, message, line, source):
        """Manipula mensagens do console JavaScript."""
        print(f"AdSense Console [{level}]: {message} (linha {line}, fonte: {source})")

    def _generate_ad_html(self):
        """Gera o HTML para o anúncio do AdSense seguindo as diretrizes oficiais."""
        width, height = self.ad_size

        # Aumenta levemente os tamanhos para garantir visibilidade
        display_width = width + 2
        display_height = height + 2

        # Define o estilo e formato com base no tipo de anúncio
        # Use formatos padrão do AdSense para melhor compatibilidade
        if self.ad_type == "multiplex":
            ad_style = f"display:block;width:{display_width}px;height:{display_height}px;"
            ad_format = "autorelaxed"  # Formato multiplex para conteúdo mais rico
            ad_responsive = "true"
        elif self.ad_type == "rectangle":
            ad_style = f"display:block;width:{display_width}px;height:{display_height}px;"
            ad_format = "auto"  # Formato auto é mais flexível
            ad_responsive = "true"
        elif self.ad_type == "display":
            ad_style = f"display:block;width:{display_width}px;height:{display_height}px;"
            ad_format = "auto"
            ad_responsive = "true"
        elif self.ad_type == "banner":
            ad_style = f"display:block;width:{display_width}px;height:{display_height}px;"
            ad_format = "horizontal"  # Melhor formato para banners
            ad_responsive = "true"
        else:
            ad_style = f"display:block;width:{display_width}px;height:{display_height}px;"
            ad_format = "auto"
            ad_responsive = "true"

        # HTML atualizado seguindo as diretrizes oficiais do Google AdSense
        html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1">
            <meta http-equiv="origin-trial" content="As8hBt767M55Yp+FxA2IQnCGlYlxE9VUwsxHCJ7Ca1wZO3OvgRTylUlIDwPzyE3tR32qzxAYjgQF8FS0yfEDJgQAAABxeyJvcmlnaW4iOiJodHRwczovL2dvb2dsZXN5bmRpY2F0aW9uLmNvbTo0NDMiLCJmZWF0dXJlIjoiQ29va2llbGVzc0Nyb3NzU2l0ZUZyYW1pbmciLCJleHBpcnkiOjE3MDc5MTU5OTl9">
            <title>AdSense</title>
            <style>
                html, body {{
                    margin: 0;
                    padding: 0;
                    overflow: hidden;
                    width: {display_width}px;
                    height: {display_height}px;
                    background-color: white;
                }}
                .ad-container {{
                    width: {display_width}px;
                    height: {display_height}px;
                    margin: 0;
                    padding: 0;
                    background-color: white;
                    overflow: hidden;
                    position: relative;
                }}
                ins.adsbygoogle {{
                    display: block !important;
                    width: {display_width}px !important;
                    height: {display_height}px !important;
                    overflow: hidden;
                    visibility: visible !important;
                    background-color: white;
                    margin: 0 !important;
                    padding: 0 !important;
                }}
            </style>
        </head>
        <body>
            <div class="ad-container">
                <!-- Script do Google AdSense com SameSite atributos -->
                <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client={self.ad_client}"
                     crossorigin="anonymous"></script>

                <!-- Question_Helper Ad Unit -->
                <ins class="adsbygoogle"
                     style="{ad_style}"
                     data-ad-client="{self.ad_client}"
                     data-ad-slot="{self.ad_slot}"
                     data-ad-format="{ad_format}"
                     data-full-width-responsive="{ad_responsive}"></ins>

                <script>
                    (function() {{
                        try {{
                            // Inicialização padrão do AdSense conforme documentação oficial
                            window.adsbygoogle = window.adsbygoogle || [];

                            // Configuração para permitir anúncios personalizados (menos restritos)
                            if (typeof adsbygoogle.requestNonPersonalizedAds === 'undefined') {{
                                adsbygoogle.requestNonPersonalizedAds = 1;
                            }}

                            // Configurações específicas para o formato multiplex (autorelaxed)
                            var adConfig = {{}};

                            // Se for formato multiplex, adiciona configurações específicas
                            if ('{ad_format}' === 'autorelaxed') {{
                                adConfig = {{
                                    google_ad_client: '{self.ad_client}',
                                    google_ad_slot: '{self.ad_slot}',
                                    google_ad_format: 'autorelaxed',
                                    google_full_width_responsive: true
                                }};
                            }}

                            // Inicializa o anúncio imediatamente
                            adsbygoogle.push(adConfig);
                            console.log('AdSense inicializado com sucesso');

                            // Verifica se o anúncio foi carregado corretamente após um tempo
                            setTimeout(function() {{
                                var adElement = document.querySelector('.adsbygoogle');
                                if (adElement) {{
                                    // Garante que o elemento tenha o tamanho correto
                                    adElement.style.width = '{display_width}px';
                                    adElement.style.height = '{display_height}px';
                                    adElement.style.display = 'block';
                                    adElement.style.visibility = 'visible';
                                    adElement.style.overflow = 'visible';
                                    adElement.style.backgroundColor = 'white';
                                    adElement.style.position = 'static';

                                    // Força um CSS importante para garantir visibilidade
                                    document.head.insertAdjacentHTML('beforeend',
                                      '<style>.adsbygoogle {{ display: block !important; visibility: visible !important; }}</style>');

                                    // Verifica se o anúncio tem conteúdo
                                    var hasContent = adElement.innerHTML.trim() !== '';
                                    var computedStyle = window.getComputedStyle(adElement);
                                    var hasSize = computedStyle.height !== '0px' && computedStyle.width !== '0px';

                                    if (hasContent && hasSize) {{
                                        console.log('Anúncio carregado e visível');
                                    }} else {{
                                        console.log('Anúncio não visível, tentando reinicializar');
                                        // Tenta inicializar novamente com configuração menos restrita
                                        adsbygoogle.push({{
                                          parameters: {{
                                            google_ad_channel: 'test'
                                          }}
                                        }});
                                    }}
                                }}
                            }}, 1000);
                        }} catch (error) {{
                            console.error('Erro ao inicializar AdSense:', error);
                        }}
                    }})();
                </script>
                </ins>
            </div>
        </body>
        </html>
        """
        return html

class AdManager:
    """Gerenciador de anúncios para o Python Question Helper."""

    def __init__(self, ad_client=None):
        """
        Inicializa o gerenciador de anúncios.

        Args:
            ad_client: ID do cliente do AdSense
        """
        self.ad_client = ad_client or ADS_CLIENT_ID
        self.ad_slots = {
            "banner": ADS_BANNER_SLOT,
            "rectangle": ADS_RECTANGLE_SLOT,
            "small": ADS_BANNER_SLOT,
            "multiplex": ADS_MULTIPLEX_SLOT
        }

    def create_banner(self, parent=None, ad_type=None):
        """
        Cria um banner de anúncio.

        Args:
            parent: Widget pai
            ad_type: Tipo de anúncio (banner, rectangle, multiplex, etc.)

        Returns:
            Widget de anúncio
        """
        # Se nenhum tipo for especificado, usa o formato multiplex que é mais rico em conteúdo
        if ad_type is None:
            # Usa o formato multiplex (autorelaxed) por padrão
            ad_type = "multiplex"

        # Obtém o slot correspondente ao tipo de anúncio
        ad_slot = self.ad_slots.get(ad_type, self.ad_slots["multiplex"])

        # Cria e retorna o banner
        print(f"Criando banner de anúncio do tipo: {ad_type}")
        return AdBanner(parent, ad_type, ad_slot, self.ad_client)
