"""
Módulo para processamento de imagens, incluindo detecção e recorte de áreas de questões.
"""
import cv2
import numpy as np
from config import DEBUG

class ImageProcessor:
    def __init__(self):
        pass

    def detect_question_area(self, image):
        """
        Detecta a área da questão na imagem.

        Args:
            image: A imagem capturada

        Returns:
            Uma tupla (x, y, w, h) com as coordenadas da área da questão,
            ou None se não for possível detectar
        """
        try:
            # Converte para escala de cinza
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

            # Aplica blur para reduzir ruído
            blurred = cv2.G<PERSON>sianBlur(gray, (5, 5), 0)

            # Aplica threshold adaptativo
            thresh = cv2.adaptiveThreshold(blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                                         cv2.THRESH_BINARY_INV, 11, 2)

            # Encontra contornos
            contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            # Filtra contornos por área
            min_area = image.shape[0] * image.shape[1] * 0.01  # 1% da área total
            large_contours = [cnt for cnt in contours if cv2.contourArea(cnt) > min_area]

            if not large_contours:
                if DEBUG:
                    print("Nenhum contorno grande encontrado")
                return None

            # Encontra o contorno com a maior área
            largest_contour = max(large_contours, key=cv2.contourArea)

            # Obtém o retângulo delimitador
            x, y, w, h = cv2.boundingRect(largest_contour)

            # Salva a imagem com o contorno para depuração
            if DEBUG:
                debug_img = image.copy()
                cv2.rectangle(debug_img, (x, y), (x+w, y+h), (0, 255, 0), 2)
                cv2.imwrite("debug_question_area.png", debug_img)
                print(f"Área da questão detectada: ({x}, {y}, {w}, {h})")

            return (x, y, w, h)

        except Exception as e:
            if DEBUG:
                print(f"Erro ao detectar área da questão: {e}")
            return None

    def detect_text_regions(self, image):
        """
        Detecta regiões de texto na imagem usando MSER.

        Args:
            image: A imagem capturada

        Returns:
            Uma lista de tuplas (x, y, w, h) com as coordenadas das regiões de texto,
            ou lista vazia se não for possível detectar
        """
        try:
            # Converte para escala de cinza
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

            # Aplica MSER para detectar regiões de texto
            mser = cv2.MSER_create()
            regions, _ = mser.detectRegions(gray)

            # Converte regiões para retângulos
            rects = []
            for region in regions:
                x, y, w, h = cv2.boundingRect(region.reshape(-1, 1, 2))
                # Filtra regiões muito pequenas
                if w > 10 and h > 10 and w < image.shape[1] * 0.9 and h < image.shape[0] * 0.9:
                    rects.append((x, y, w, h))

            # Agrupa retângulos próximos
            grouped_rects = self._group_rectangles(rects)

            # Salva a imagem com os retângulos para depuração
            if DEBUG:
                debug_img = image.copy()
                for (x, y, w, h) in grouped_rects:
                    cv2.rectangle(debug_img, (x, y), (x+w, y+h), (0, 255, 0), 2)
                cv2.imwrite("debug_text_regions.png", debug_img)
                print(f"Regiões de texto detectadas: {len(grouped_rects)}")

            return grouped_rects

        except Exception as e:
            if DEBUG:
                print(f"Erro ao detectar regiões de texto: {e}")
            return []

    def _group_rectangles(self, rects, min_distance=20):
        """
        Agrupa retângulos próximos.

        Args:
            rects: Lista de retângulos (x, y, w, h)
            min_distance: Distância mínima para considerar retângulos como parte do mesmo grupo

        Returns:
            Lista de retângulos agrupados
        """
        if not rects:
            return []

        # Ordena por coordenada y (linha)
        rects.sort(key=lambda r: r[1])

        # Agrupa retângulos por linha
        lines = []
        current_line = [rects[0]]

        for rect in rects[1:]:
            x, y, w, h = rect
            prev_y = current_line[-1][1]

            # Se estiver na mesma linha (ou próximo)
            if abs(y - prev_y) < min_distance:
                current_line.append(rect)
            else:
                # Ordena a linha atual por coordenada x
                current_line.sort(key=lambda r: r[0])
                lines.append(current_line)
                current_line = [rect]

        # Adiciona a última linha
        if current_line:
            current_line.sort(key=lambda r: r[0])
            lines.append(current_line)

        # Cria retângulos para cada linha
        grouped_rects = []
        for line in lines:
            if not line:
                continue

            # Encontra as coordenadas extremas da linha
            min_x = min(r[0] for r in line)
            min_y = min(r[1] for r in line)
            max_x = max(r[0] + r[2] for r in line)
            max_y = max(r[1] + r[3] for r in line)

            # Cria um retângulo para a linha
            grouped_rects.append((min_x, min_y, max_x - min_x, max_y - min_y))

        return grouped_rects

    def crop_to_question(self, image):
        """
        Recorta a imagem para focar na área da questão.

        Args:
            image: A imagem capturada

        Returns:
            A imagem recortada ou a imagem original se não for possível recortar
        """
        # Primeiro tenta detectar a área da questão
        question_area = self.detect_question_area(image)
        if question_area:
            x, y, w, h = question_area
            # Adiciona uma margem
            margin = 20
            x = max(0, x - margin)
            y = max(0, y - margin)
            w = min(image.shape[1] - x, w + 2*margin)
            h = min(image.shape[0] - y, h + 2*margin)

            # Recorta a imagem
            cropped = image[y:y+h, x:x+w]

            if DEBUG:
                cv2.imwrite("cropped_question.png", cropped)
                print(f"Imagem recortada para {cropped.shape}")

            return cropped

        # Se não conseguir detectar a área da questão, tenta detectar regiões de texto
        text_regions = self.detect_text_regions(image)
        if text_regions:
            # Encontra o retângulo que engloba todas as regiões de texto
            min_x = min(r[0] for r in text_regions)
            min_y = min(r[1] for r in text_regions)
            max_x = max(r[0] + r[2] for r in text_regions)
            max_y = max(r[1] + r[3] for r in text_regions)

            # Adiciona uma margem
            margin = 20
            min_x = max(0, min_x - margin)
            min_y = max(0, min_y - margin)
            max_x = min(image.shape[1], max_x + margin)
            max_y = min(image.shape[0], max_y + margin)

            # Recorta a imagem
            cropped = image[min_y:max_y, min_x:max_x]

            if DEBUG:
                cv2.imwrite("cropped_text_regions.png", cropped)
                print(f"Imagem recortada para regiões de texto: {cropped.shape}")

            return cropped

        # Se não conseguir detectar nada, retorna a imagem original
        if DEBUG:
            print("Não foi possível recortar a imagem, retornando original")

        return image

    def enhance_image_for_ocr(self, image):
        """
        Melhora a imagem para OCR com otimizações de desempenho.

        Args:
            image: A imagem a ser melhorada

        Returns:
            A imagem melhorada
        """
        try:
            # Converte para escala de cinza
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

            # Aumenta o contraste - método mais rápido
            enhanced = cv2.equalizeHist(gray)

            # Redimensiona para melhorar o OCR - apenas se necessário
            height, width = enhanced.shape
            min_size = 1200  # Reduzido para processamento mais rápido
            max_size = 2000  # Limite máximo para evitar imagens muito grandes

            if height < min_size or width < min_size:
                # Aumenta imagens pequenas
                scale_factor = min_size / min(height, width)
                enhanced = cv2.resize(enhanced, None, fx=scale_factor, fy=scale_factor,
                                    interpolation=cv2.INTER_LINEAR)  # INTER_LINEAR é mais rápido que CUBIC
            elif height > max_size or width > max_size:
                # Reduz imagens muito grandes
                scale_factor = max_size / max(height, width)
                enhanced = cv2.resize(enhanced, None, fx=scale_factor, fy=scale_factor,
                                    interpolation=cv2.INTER_AREA)  # Melhor para redução

            # Aplica threshold simples - mais rápido que adaptativo
            _, binary = cv2.threshold(enhanced, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

            if DEBUG:
                cv2.imwrite("enhanced_for_ocr.png", binary)
                print("Imagem melhorada para OCR (otimizada)")

            return binary

        except Exception as e:
            if DEBUG:
                print(f"Erro ao melhorar imagem para OCR: {e}")
            return image

# Para testes
if __name__ == "__main__":
    processor = ImageProcessor()

    # Carrega uma imagem de teste
    image = cv2.imread("current_capture.png")
    if image is not None:
        # Testa o recorte da questão
        cropped = processor.crop_to_question(image)

        # Testa a melhoria para OCR
        enhanced = processor.enhance_image_for_ocr(cropped)

        print("Processamento concluído")
    else:
        print("Imagem de teste não encontrada")
