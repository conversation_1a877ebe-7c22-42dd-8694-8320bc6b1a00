"""
<PERSON>ó<PERSON>lo de conversão de fala para texto (Speech-to-Text).
"""
import os
import tempfile
from typing import Optional, Dict, Any
from pathlib import Path

from ..core.config import (
    STT_PROVIDER, STT_LANGUAGE, STT_MODEL_SIZE,
    GOOGLE_SPEECH_API_KEY, AZURE_SPEECH_KEY, AZURE_SPEECH_REGION,
    AWS_ACCESS_KEY, AWS_SECRET_KEY, DEBUG
)


class SpeechToTextConverter:
    """
    Conversor de fala para texto com suporte a múltiplos provedores.
    """
    
    def __init__(self):
        """Inicializa o conversor de fala para texto."""
        self.provider = STT_PROVIDER.lower()
        self.language = STT_LANGUAGE
        self.model_size = STT_MODEL_SIZE
        
        # Inicializar provedor específico
        self._initialize_provider()
        
        if DEBUG:
            print(f"SpeechToTextConverter inicializado com provedor: {self.provider}")
    
    def _initialize_provider(self):
        """Inicializa o provedor de STT específico."""
        try:
            if self.provider == "whisper":
                self._initialize_whisper()
            elif self.provider == "google":
                self._initialize_google()
            elif self.provider == "azure":
                self._initialize_azure()
            elif self.provider == "aws":
                self._initialize_aws()
            else:
                if DEBUG:
                    print(f"Provedor desconhecido: {self.provider}, usando Whisper como padrão")
                self.provider = "whisper"
                self._initialize_whisper()
                
        except Exception as e:
            if DEBUG:
                print(f"Erro ao inicializar provedor {self.provider}: {e}")
    
    def _initialize_whisper(self):
        """Inicializa o Whisper (OpenAI)."""
        try:
            global whisper
            import whisper
            
            # Carrega modelo Whisper
            self.whisper_model = whisper.load_model(self.model_size)
            
            if DEBUG:
                print(f"Whisper modelo '{self.model_size}' carregado com sucesso")
                
        except ImportError:
            if DEBUG:
                print("Whisper não encontrado. Instale com: pip install openai-whisper")
            self.whisper_model = None
        except Exception as e:
            if DEBUG:
                print(f"Erro ao carregar Whisper: {e}")
            self.whisper_model = None
    
    def _initialize_google(self):
        """Inicializa Google Speech-to-Text."""
        try:
            global speech
            from google.cloud import speech
            
            if not GOOGLE_SPEECH_API_KEY:
                if DEBUG:
                    print("Google Speech API key não configurada")
                return
            
            # Configura credenciais
            os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = GOOGLE_SPEECH_API_KEY
            self.google_client = speech.SpeechClient()
            
            if DEBUG:
                print("Google Speech-to-Text inicializado")
                
        except ImportError:
            if DEBUG:
                print("Google Cloud Speech não encontrado. Instale com: pip install google-cloud-speech")
            self.google_client = None
        except Exception as e:
            if DEBUG:
                print(f"Erro ao inicializar Google Speech: {e}")
            self.google_client = None
    
    def _initialize_azure(self):
        """Inicializa Azure Speech Services."""
        try:
            global speechsdk
            import azure.cognitiveservices.speech as speechsdk
            
            if not AZURE_SPEECH_KEY or not AZURE_SPEECH_REGION:
                if DEBUG:
                    print("Azure Speech credenciais não configuradas")
                return
            
            # Configura Azure Speech
            speech_config = speechsdk.SpeechConfig(
                subscription=AZURE_SPEECH_KEY,
                region=AZURE_SPEECH_REGION
            )
            speech_config.speech_recognition_language = self.language
            
            self.azure_config = speech_config
            
            if DEBUG:
                print("Azure Speech Services inicializado")
                
        except ImportError:
            if DEBUG:
                print("Azure Speech SDK não encontrado. Instale com: pip install azure-cognitiveservices-speech")
            self.azure_config = None
        except Exception as e:
            if DEBUG:
                print(f"Erro ao inicializar Azure Speech: {e}")
            self.azure_config = None
    
    def _initialize_aws(self):
        """Inicializa AWS Transcribe."""
        try:
            global boto3
            import boto3
            
            if not AWS_ACCESS_KEY or not AWS_SECRET_KEY:
                if DEBUG:
                    print("AWS credenciais não configuradas")
                return
            
            # Configura cliente AWS
            self.aws_client = boto3.client(
                'transcribe',
                aws_access_key_id=AWS_ACCESS_KEY,
                aws_secret_access_key=AWS_SECRET_KEY
            )
            
            if DEBUG:
                print("AWS Transcribe inicializado")
                
        except ImportError:
            if DEBUG:
                print("Boto3 não encontrado. Instale com: pip install boto3")
            self.aws_client = None
        except Exception as e:
            if DEBUG:
                print(f"Erro ao inicializar AWS Transcribe: {e}")
            self.aws_client = None
    
    def convert_audio_to_text(self, audio_file_path: str) -> Optional[str]:
        """
        Converte arquivo de áudio para texto.
        
        Args:
            audio_file_path: Caminho para o arquivo de áudio
            
        Returns:
            Texto extraído ou None se erro
        """
        try:
            if not os.path.exists(audio_file_path):
                if DEBUG:
                    print(f"Arquivo de áudio não encontrado: {audio_file_path}")
                return None
            
            if DEBUG:
                print(f"Convertendo áudio para texto usando {self.provider}")
            
            # Chama método específico do provedor
            if self.provider == "whisper":
                return self._whisper_transcribe(audio_file_path)
            elif self.provider == "google":
                return self._google_transcribe(audio_file_path)
            elif self.provider == "azure":
                return self._azure_transcribe(audio_file_path)
            elif self.provider == "aws":
                return self._aws_transcribe(audio_file_path)
            else:
                if DEBUG:
                    print(f"Provedor não suportado: {self.provider}")
                return None
                
        except Exception as e:
            if DEBUG:
                print(f"Erro na conversão de áudio para texto: {e}")
            return None
    
    def _whisper_transcribe(self, audio_file_path: str) -> Optional[str]:
        """Transcreve áudio usando Whisper."""
        try:
            if not hasattr(self, 'whisper_model') or self.whisper_model is None:
                if DEBUG:
                    print("Modelo Whisper não disponível")
                return None
            
            # Transcreve áudio
            result = self.whisper_model.transcribe(
                audio_file_path,
                language=self.language.split('-')[0] if '-' in self.language else self.language
            )
            
            text = result.get('text', '').strip()
            
            if DEBUG:
                print(f"Whisper transcription: {text}")
            
            return text if text else None
            
        except Exception as e:
            if DEBUG:
                print(f"Erro na transcrição Whisper: {e}")
            return None
    
    def _google_transcribe(self, audio_file_path: str) -> Optional[str]:
        """Transcreve áudio usando Google Speech-to-Text."""
        try:
            if not hasattr(self, 'google_client') or self.google_client is None:
                if DEBUG:
                    print("Cliente Google Speech não disponível")
                return None
            
            # Lê arquivo de áudio
            with open(audio_file_path, 'rb') as audio_file:
                content = audio_file.read()
            
            # Configura requisição
            audio = speech.RecognitionAudio(content=content)
            config = speech.RecognitionConfig(
                encoding=speech.RecognitionConfig.AudioEncoding.LINEAR16,
                sample_rate_hertz=16000,
                language_code=self.language,
            )
            
            # Faz transcrição
            response = self.google_client.recognize(config=config, audio=audio)
            
            # Extrai texto
            text = ""
            for result in response.results:
                text += result.alternatives[0].transcript + " "
            
            text = text.strip()
            
            if DEBUG:
                print(f"Google transcription: {text}")
            
            return text if text else None
            
        except Exception as e:
            if DEBUG:
                print(f"Erro na transcrição Google: {e}")
            return None
    
    def _azure_transcribe(self, audio_file_path: str) -> Optional[str]:
        """Transcreve áudio usando Azure Speech Services."""
        try:
            if not hasattr(self, 'azure_config') or self.azure_config is None:
                if DEBUG:
                    print("Configuração Azure Speech não disponível")
                return None
            
            # Configura entrada de áudio
            audio_input = speechsdk.AudioConfig(filename=audio_file_path)
            speech_recognizer = speechsdk.SpeechRecognizer(
                speech_config=self.azure_config,
                audio_config=audio_input
            )
            
            # Faz transcrição
            result = speech_recognizer.recognize_once()
            
            if result.reason == speechsdk.ResultReason.RecognizedSpeech:
                text = result.text.strip()
                if DEBUG:
                    print(f"Azure transcription: {text}")
                return text if text else None
            else:
                if DEBUG:
                    print(f"Azure Speech falhou: {result.reason}")
                return None
                
        except Exception as e:
            if DEBUG:
                print(f"Erro na transcrição Azure: {e}")
            return None
    
    def _aws_transcribe(self, audio_file_path: str) -> Optional[str]:
        """Transcreve áudio usando AWS Transcribe."""
        try:
            if not hasattr(self, 'aws_client') or self.aws_client is None:
                if DEBUG:
                    print("Cliente AWS Transcribe não disponível")
                return None
            
            # AWS Transcribe requer upload para S3, implementação simplificada
            # Para uso real, seria necessário configurar S3 bucket
            if DEBUG:
                print("AWS Transcribe requer configuração adicional de S3")
            return None
            
        except Exception as e:
            if DEBUG:
                print(f"Erro na transcrição AWS: {e}")
            return None
    
    def is_available(self) -> bool:
        """
        Verifica se o conversor está disponível.
        
        Returns:
            True se o conversor está disponível
        """
        if self.provider == "whisper":
            return hasattr(self, 'whisper_model') and self.whisper_model is not None
        elif self.provider == "google":
            return hasattr(self, 'google_client') and self.google_client is not None
        elif self.provider == "azure":
            return hasattr(self, 'azure_config') and self.azure_config is not None
        elif self.provider == "aws":
            return hasattr(self, 'aws_client') and self.aws_client is not None
        return False
    
    def get_status(self) -> Dict[str, Any]:
        """
        Retorna status do conversor.
        
        Returns:
            Dicionário com informações de status
        """
        return {
            'provider': self.provider,
            'language': self.language,
            'model_size': self.model_size,
            'available': self.is_available()
        }
