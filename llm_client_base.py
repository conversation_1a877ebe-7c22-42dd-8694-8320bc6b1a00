"""
Classe base para clientes de LLM.
"""
from abc import ABC, abstractmethod

class LLMClientBase(ABC):
    """
    Classe base abstrata para clientes de LLM.
    Todos os clientes de LLM devem implementar esta interface.
    """
    
    @abstractmethod
    def process_image(self, image_path, prompt=None):
        """
        Processa uma imagem com o LLM.
        
        Args:
            image_path: Caminho para a imagem
            prompt: Prompt opcional para enviar junto com a imagem
            
        Returns:
            Resposta do LLM
        """
        pass
    
    @abstractmethod
    def get_available_models(self):
        """
        Retorna a lista de modelos disponíveis para este cliente.
        
        Returns:
            Lista de dicionários com informações dos modelos
        """
        pass
    
    @abstractmethod
    def set_model(self, model_id):
        """
        Define o modelo a ser usado.
        
        Args:
            model_id: ID do modelo
            
        Returns:
            True se o modelo foi definido com sucesso, False caso contrário
        """
        pass
    
    @abstractmethod
    def get_provider_name(self):
        """
        Retorna o nome do provedor de LLM.
        
        Returns:
            Nome do provedor
        """
        pass
