"""
Main entry point for the Python Question Helper application.
"""
import sys
import time
import threading
import cv2
import numpy as np
from datetime import datetime
from PyQt5.QtWidgets import QApplication

from ..processing.screen_capture_simple import ScreenCapture
from ..processing.screen_monitor import ScreenMonitor
from ..processing.ocr_processor import OCRProcessor
from ..llm.llm_client import LLMClient
from ..ui.ui_overlay import OverlayUI
from ..processing.text_processor import TextProcessor
from ..processing.image_processor import ImageProcessor
from ..ui.model_selector_simple_v2 import ModelSelectorSimpleV2Dialog
from ..llm.llm_manager import LLMManager
from ..question_logging.question_logger import QuestionLogger
from .config import DEBUG, LLM_MODEL, LLM_PROVIDER, ADS_ENABLED, ADS_CLIENT_ID, ENABLE_AUDIO_MODULE

# Importação condicional do módulo de áudio
if ENABLE_AUDIO_MODULE:
    try:
        from ..audio import AudioProcessor
        from ..ui.audio_controls import AudioControlsWidget
        AUDIO_AVAILABLE = True
    except ImportError as e:
        if DEBUG:
            print(f"Módulo de áudio não disponível: {e}")
        AUDIO_AVAILABLE = False
else:
    AUDIO_AVAILABLE = False
from PyQt5.QtCore import QSettings

class QuestionHelper:
    def __init__(self):
        self.screen_capture = ScreenCapture()
        self.ocr_processor = OCRProcessor()
        self.text_processor = TextProcessor()
        self.image_processor = ImageProcessor()

        # Carrega as configurações
        self.settings = QSettings("BoZolinO", "Python Question Helper")
        self.detect_any_change = self.settings.value("monitor/detect_any_change", True, type=bool)

        # Carrega o provedor e modelo atual
        current_provider = self.settings.value("llm_provider", LLM_PROVIDER)
        current_model = self.settings.value("llm_model", LLM_MODEL)

        # Carrega as configurações de anúncios
        self.ads_enabled = self.settings.value("ads/enabled", ADS_ENABLED, type=bool)
        self.ads_client_id = self.settings.value("ads/client_id", ADS_CLIENT_ID)

        # Inicializa o gerenciador de LLM
        self.llm_manager = LLMManager(current_provider)
        if current_model:
            self.llm_manager.set_model(current_model)

        # Inicializa o sistema de logging
        self.question_logger = QuestionLogger()

        # Mantém o cliente LLM para compatibilidade com código existente
        self.llm_client = LLMClient()

        # Initialize UI (usa a instância de QApplication existente)
        # Força a desativação dos anúncios
        self.ui = OverlayUI(ads_enabled=False, ads_client_id=None)

        # Simplificado: apenas os botões essenciais
        self.ui.create_auto_monitor_button(self.toggle_auto_monitoring, None)  # Botão de monitoramento
        self.ui.create_model_selector_button(self.open_model_selector)  # Botão para selecionar modelo

        # Initialize processing thread
        self.processing_thread = None
        self.is_processing = False

        # Initialize screen monitor
        self.screen_monitor = ScreenMonitor(callback_on_change=self.on_screen_change)
        self.auto_monitoring = False
        self.current_window_handle = None

        # Variables for handling changes during processing
        self.latest_change_image = None
        self.has_pending_change = False
        self.last_processed_question = None  # Para evitar reprocessar a mesma questão

        # Window handle management
        self.window_connection_lost = False
        self.last_window_check_time = 0

        # Initialize audio module if available
        self.audio_processor = None
        self.audio_controls = None
        if AUDIO_AVAILABLE:
            try:
                self.audio_processor = AudioProcessor(callback_on_text=self.on_audio_text_received)
                self.audio_controls = AudioControlsWidget()
                self.audio_controls.set_audio_processor(self.audio_processor)

                # Connect audio signals
                self.audio_controls.audio_toggle_requested.connect(self.toggle_audio_recording)
                self.audio_controls.audio_text_received.connect(self.on_audio_text_received)
                self.audio_controls.settings_changed.connect(self.on_audio_settings_changed)

                # Add audio controls to UI
                self.ui.add_audio_controls(self.audio_controls)

                if DEBUG:
                    print("Módulo de áudio inicializado com sucesso")
            except Exception as e:
                if DEBUG:
                    print(f"Erro ao inicializar módulo de áudio: {e}")
                self.audio_processor = None
                self.audio_controls = None

    def _extract_confidence_from_response(self, response: str) -> int:
        """
        Extrai o nível de confiança da resposta do LLM.

        Args:
            response: Resposta do LLM

        Returns:
            Nível de confiança (0-100) ou None se não encontrado
        """
        import re

        # Padrões para detectar confiança
        patterns = [
            r'confiança:\s*(\d+)%',
            r'confidence:\s*(\d+)%',
            r'certeza:\s*(\d+)%',
            r'(\d+)%\s*de\s*confiança',
            r'(\d+)%\s*confidence'
        ]

        for pattern in patterns:
            match = re.search(pattern, response, re.IGNORECASE)
            if match:
                confidence = int(match.group(1))
                return min(100, max(0, confidence))  # Garante que está entre 0-100

        # Se não encontrar padrão específico, tenta inferir baseado no conteúdo
        response_lower = response.lower()

        # Indicadores de alta confiança
        if any(indicator in response_lower for indicator in ['certamente', 'definitivamente', 'com certeza', 'claramente']):
            return 90

        # Indicadores de média confiança
        if any(indicator in response_lower for indicator in ['provavelmente', 'likely', 'possivelmente']):
            return 70

        # Indicadores de baixa confiança
        if any(indicator in response_lower for indicator in ['talvez', 'maybe', 'não tenho certeza', 'incerto']):
            return 40

        # Padrão: se tem resposta estruturada, assume confiança média-alta
        if 'resposta correta:' in response_lower:
            return 75

        # Padrão: se é muito curta ou genérica, assume baixa confiança
        if len(response.strip()) < 50:
            return 50

        # Padrão default
        return 65

    def toggle_audio_recording(self, start_recording: bool):
        """
        Alterna gravação de áudio.

        Args:
            start_recording: True para iniciar, False para parar
        """
        if not self.audio_processor:
            if DEBUG:
                print("Processador de áudio não disponível")
            return

        try:
            if start_recording:
                success = self.audio_processor.start_recording()
                if success:
                    if DEBUG:
                        print("Gravação de áudio iniciada")
                else:
                    if DEBUG:
                        print("Falha ao iniciar gravação de áudio")
            else:
                success = self.audio_processor.stop_recording()
                if success:
                    if DEBUG:
                        print("Gravação de áudio parada")
                else:
                    if DEBUG:
                        print("Falha ao parar gravação de áudio")
        except Exception as e:
            if DEBUG:
                print(f"Erro ao alternar gravação de áudio: {e}")

    def on_audio_text_received(self, text: str):
        """
        Callback para quando texto é extraído do áudio.

        Args:
            text: Texto extraído do áudio
        """
        try:
            if DEBUG:
                print(f"Texto de áudio recebido: {text}")

            # Combina com texto OCR se disponível
            combined_text = text
            if hasattr(self, 'last_ocr_text') and self.last_ocr_text:
                combined_text = f"Texto da tela:\n{self.last_ocr_text}\n\nÁudio:\n{text}"

            # Processa o texto combinado com o LLM
            if not self.is_processing:
                self.is_processing = True
                self.ui.update_status("Processando questão de áudio...")

                # Processa em thread separada
                import threading
                thread = threading.Thread(
                    target=self._process_audio_text_thread,
                    args=(combined_text, text)
                )
                thread.daemon = True
                thread.start()
            else:
                if DEBUG:
                    print("Já processando, ignorando texto de áudio")

        except Exception as e:
            if DEBUG:
                print(f"Erro ao processar texto de áudio: {e}")

    def _process_audio_text_thread(self, combined_text: str, audio_text: str):
        """
        Thread para processar texto de áudio.

        Args:
            combined_text: Texto combinado (OCR + áudio)
            audio_text: Texto apenas do áudio
        """
        try:
            from datetime import datetime
            start_time = datetime.now()

            # Processa com o LLM
            response = self.llm_manager.process_text_question(combined_text)

            # Calcula tempo de processamento
            end_time = datetime.now()
            processing_time = (end_time - start_time).total_seconds()

            # Extrai confiança da resposta
            confidence = self._extract_confidence_from_response(response)

            # Registra no log
            try:
                current_provider = self.llm_manager.get_provider_name()
                current_model = getattr(self.llm_manager.client, "model", "desconhecido") if self.llm_manager.client else "desconhecido"

                self.question_logger.log_question(
                    ocr_text=getattr(self, 'last_ocr_text', '') or "",
                    llm_response=response,
                    llm_provider=current_provider,
                    llm_model=current_model,
                    llm_confidence=confidence,
                    processing_time=processing_time,
                    additional_metadata={
                        'mode': 'audio_processing',
                        'audio_text': audio_text,
                        'combined_processing': bool(getattr(self, 'last_ocr_text', None))
                    }
                )
            except Exception as e:
                if DEBUG:
                    print(f"Erro ao registrar questão de áudio no log: {e}")

            # Adiciona tempo de processamento à resposta
            response += f"\n\n[Processamento de áudio - Tempo: {processing_time:.2f}s]"

            # Atualiza interface
            self.ui.update_status("Questão de áudio processada")
            self.ui.update_result(response)

        except Exception as e:
            error_msg = f"Erro ao processar questão de áudio: {str(e)}"
            if DEBUG:
                print(error_msg)
            self.ui.update_status("Erro no processamento de áudio")
            self.ui.update_result(f"Erro no processamento de áudio:\n{error_msg}")
        finally:
            self.is_processing = False

    def on_audio_settings_changed(self, settings: dict):
        """
        Callback para quando configurações de áudio são alteradas.

        Args:
            settings: Dicionário com novas configurações
        """
        try:
            if DEBUG:
                print(f"Configurações de áudio alteradas: {settings}")

            # Aqui você pode implementar a aplicação das configurações
            # Por exemplo, reconfigurar o processador de áudio
            if self.audio_processor:
                # Implementar aplicação de configurações conforme necessário
                pass

        except Exception as e:
            if DEBUG:
                print(f"Erro ao aplicar configurações de áudio: {e}")

    def check_window_connection(self):
        """Verifica se a conexão com a janela ainda está ativa."""
        import time
        current_time = time.time()

        # Verifica a cada 10 segundos
        if current_time - self.last_window_check_time < 10:
            return True

        self.last_window_check_time = current_time

        if not self.current_window_handle:
            return True  # Sem janela selecionada ainda

        try:
            # Tenta capturar uma pequena área para testar a conexão
            test_image = self.screen_capture.capture_window()
            if test_image is None:
                if not self.window_connection_lost:
                    self.window_connection_lost = True
                    self.ui.update_status("⚠️ Conexão com a janela perdida. Clique em 'Reconectar' para selecionar nova janela.")
                    self.ui.update_result("**Conexão Perdida**\n\nA janela monitorada não está mais acessível.\n\n**Possíveis causas:**\n• Janela foi fechada\n• Janela foi minimizada\n• Aplicação foi fechada\n\n**Solução:** Clique em 'Parar Monitoramento' e depois 'Iniciar Monitoramento' para selecionar uma nova janela.")
                return False
            else:
                if self.window_connection_lost:
                    self.window_connection_lost = False
                    self.ui.update_status("✅ Conexão com a janela restaurada.")
                return True
        except Exception as e:
            if not self.window_connection_lost:
                self.window_connection_lost = True
                self.ui.update_status(f"⚠️ Erro na conexão: {str(e)}")
            return False

    def open_model_selector(self):
        """Abre o diálogo de seleção de modelo."""
        # Obtém o modelo e provedor atual
        current_model = self.llm_manager.get_selected_model_id() if hasattr(self.llm_manager, 'get_selected_model_id') else None
        current_provider = self.llm_manager.provider

        # Obtém o estado atual da detecção de qualquer alteração
        detect_any_change = self.screen_monitor.is_detect_any_change_enabled() if self.screen_monitor else self.detect_any_change

        # Cria e exibe o diálogo simplificado
        dialog = ModelSelectorSimpleV2Dialog(self.ui, current_model, current_provider, detect_any_change)
        if dialog.exec_():
            # Obtém o modelo e provedor selecionados
            selected_model_id = dialog.get_selected_model_id()
            selected_provider = dialog.get_selected_provider()

            # Obtém o estado da detecção de qualquer alteração
            detect_any_change = dialog.is_detect_any_change_enabled()

            # Atualiza as configurações
            changes_made = False
            status_message = ""

            # Atualiza o provedor se necessário
            if selected_provider and selected_provider != current_provider:
                # Atualiza o provedor no gerenciador de LLM
                self.llm_manager.set_provider(selected_provider)

                # Salva a configuração
                self.settings.setValue("llm_provider", selected_provider)

                changes_made = True
                status_message += f"Provedor alterado para: {self.llm_manager.get_provider_name()}. "

            # Atualiza o modelo se necessário
            if selected_model_id and selected_model_id != current_model:
                # Atualiza o modelo no gerenciador de LLM
                self.llm_manager.set_model(selected_model_id)

                # Salva a configuração
                self.settings.setValue("llm_model", selected_model_id)

                changes_made = True
                status_message += f"Modelo alterado para: {selected_model_id}. "

            # Atualiza a detecção de qualquer alteração se necessário
            if detect_any_change != self.detect_any_change:
                self.detect_any_change = detect_any_change

                # Atualiza a configuração do monitor
                if self.screen_monitor:
                    self.screen_monitor.set_detect_any_change(detect_any_change)

                # Salva a configuração
                self.settings.setValue("monitor/detect_any_change", detect_any_change)

                changes_made = True
                status_message += f"Detecção de qualquer alteração {'ativada' if detect_any_change else 'desativada'}. "

            # Atualiza o status se houve mudanças
            if changes_made:
                self.ui.update_status(status_message)
            else:
                self.ui.update_status("Nenhuma alteração realizada.")

    def show_welcome_message(self):
        """Exibe uma mensagem de boas-vindas com instruções e o modelo carregado."""
        # Obtém informações sobre o modelo atual
        provider_name = self.llm_manager.get_provider_name()
        model_id = self.llm_manager.get_selected_model_id() if hasattr(self.llm_manager, 'get_selected_model_id') else "Padrão"

        # Cria a mensagem de boas-vindas
        welcome_message = f"""
        <b>Bem-vindo ao Python Question Helper!</b>

        <p><b>Modelo atual:</b> {model_id}</p>
        <p><b>Provedor:</b> {provider_name}</p>

        <p><b>Instruções rápidas:</b></p>
        <p>1. Clique em <b>Selecionar Modelo</b> para escolher um modelo LLM</p>
        <p>2. Clique em <b>Iniciar Monitoramento</b> e selecione a janela com as questões</p>
        <p>3. O sistema processará automaticamente a primeira imagem</p>
        <p>4. Quando a questão mudar, o sistema detectará e processará automaticamente</p>
        """

        # Atualiza a interface com a mensagem de boas-vindas
        self.ui.update_result(welcome_message)

    def run(self):
        """Run the application."""
        self.ui.show()
        self.ui.update_status("Pronto - Selecione um modelo e inicie o monitoramento")

        # Exibe a mensagem de boas-vindas
        self.show_welcome_message()

        # Retorna None para que o método modificado seja usado
        return None

    def process_direct_vision(self):
        """Process an image directly with the LLM's vision capabilities."""
        if self.is_processing:
            self.ui.update_status("Já processando, por favor aguarde...")
            return

        self.is_processing = True
        self.ui.update_status("Por favor, selecione uma janela para capturar...")
        self.ui.disable_capture_buttons()

        # Start processing in a separate thread
        self.processing_thread = threading.Thread(target=self._process_direct_vision_thread)
        self.processing_thread.daemon = True
        self.processing_thread.start()

    def _process_direct_vision_thread(self):
        """Thread function for processing an image directly with vision."""
        try:
            # Inicia o contador de tempo
            start_time = datetime.now()

            # Capture the window
            self.ui.update_status("Selecionando e capturando janela...")
            image = self.screen_capture.capture_window()

            # Store the window handle for auto-monitoring
            if self.screen_capture.window_handle:
                self.current_window_handle = self.screen_capture.window_handle

            if image is None:
                self.ui.update_status("Falha ao capturar janela. Por favor, tente novamente.")
                self.ui.update_result("Erro: Não foi possível capturar a janela. Por favor, tente novamente.")
                self.is_processing = False
                self.ui.enable_capture_buttons()
                return

            # Save the image to a file
            image_path = "vision_capture.png"
            cv2.imwrite(image_path, cv2.cvtColor(image, cv2.COLOR_RGB2BGR))

            # Show the captured image and ask for confirmation (only in manual mode)
            if not self.auto_monitoring:
                self.ui.update_status("Mostrando imagem capturada. Confirme se o conteúdo está visível...")

                # Display the image in a window
                window_name = "Imagem Capturada (Pressione qualquer tecla para continuar)"
                cv2.namedWindow(window_name, cv2.WINDOW_NORMAL)
                cv2.imshow(window_name, cv2.cvtColor(image, cv2.COLOR_RGB2BGR))

                # Wait for user input
                cv2.waitKey(0)
                cv2.destroyAllWindows()

            # Extrai o texto da imagem para melhorar o prompt
            self.ui.update_status("Extraindo texto da imagem...")
            # Carrega a imagem para o OCR
            ocr_image = cv2.imread(image_path)
            # Tenta com português primeiro, se falhar usa inglês
            try:
                extracted_text = self.ocr_processor.extract_text_with_layout(ocr_image, lang='por')
                if not extracted_text:
                    self.ui.update_status("Tentando extração em inglês...")
                    extracted_text = self.ocr_processor.extract_text_with_layout(ocr_image, lang='eng')
            except Exception:
                self.ui.update_status("Erro na extração em português, tentando em inglês...")
                extracted_text = self.ocr_processor.extract_text_with_layout(ocr_image, lang='eng')

            if extracted_text:
                self.ui.update_status(f"Texto extraído com sucesso ({len(extracted_text)} caracteres). Enviando para processamento...")
            else:
                self.ui.update_status("Não foi possível extrair texto. Enviando imagem para processamento direto...")

            # Process the image with the LLM manager using validation, passando o texto extraído
            answer = self.llm_manager.process_image_with_validation(image_path, extracted_text=extracted_text)

            # Calcula o tempo de processamento
            end_time = datetime.now()
            processing_time = (end_time - start_time).total_seconds()

            # Registra a questão no sistema de logging
            try:
                current_provider = self.llm_manager.get_provider_name()
                current_model = getattr(self.llm_manager.client, "model", "desconhecido") if self.llm_manager.client else "desconhecido"

                # Extrai confiança da resposta (se disponível)
                confidence = self._extract_confidence_from_response(answer)

                self.question_logger.log_question(
                    ocr_text=extracted_text or "",
                    llm_response=answer,
                    llm_provider=current_provider,
                    llm_model=current_model,
                    llm_confidence=confidence,
                    processing_time=processing_time,
                    image_path=image_path,
                    additional_metadata={
                        'mode': 'manual_direct_vision',
                        'window_handle': str(self.current_window_handle) if self.current_window_handle else None
                    }
                )
            except Exception as e:
                if DEBUG:
                    print(f"Erro ao registrar questão no log: {e}")

            # Adiciona o tempo de processamento à resposta
            answer += f"\n\n[Tempo de processamento: {processing_time:.2f} segundos]"

            # Update UI with the answer
            self.ui.update_status("Pronto - Resposta abaixo")
            self.ui.update_result(answer)

        except Exception as e:
            error_msg = f"Erro ao processar imagem com visão direta: {str(e)}"
            if DEBUG:
                print(error_msg)
            self.ui.update_status("Ocorreu um erro")
            self.ui.update_result(f"Ocorreu um erro durante o processamento:\n{error_msg}")

        finally:
            self.is_processing = False
            self.ui.enable_capture_buttons()

    def select_area(self):
        """Allow the user to select a specific area of the window."""
        if self.is_processing:
            self.ui.update_status("Já processando, por favor aguarde...")
            return

        self.is_processing = True
        self.ui.update_status("Por favor, selecione uma janela para capturar...")
        self.ui.disable_capture_buttons()

        try:
            # Capture the window first
            image = self.screen_capture.capture_window()
            if image is None:
                self.ui.update_status("Falha ao capturar janela. Por favor, tente novamente.")
                self.is_processing = False
                self.ui.enable_capture_buttons()
                return

            # Store the window handle for auto-monitoring
            if self.screen_capture.window_handle:
                self.current_window_handle = self.screen_capture.window_handle

            # Now let the user select an area
            self.ui.update_status("Selecione a área da questão arrastando o mouse...")
            selected_area = self.screen_capture.select_area(image)

            if selected_area:
                self.ui.update_status(f"Área selecionada: {selected_area}")
                # Capture and process the selected area
                area_image = self.screen_capture.capture_selected_area()
                if area_image is not None:
                    self.process_image(area_image)
                else:
                    self.ui.update_status("Falha ao capturar área selecionada")
                    self.is_processing = False
                    self.ui.enable_capture_buttons()
            else:
                self.ui.update_status("Seleção de área cancelada")
                self.is_processing = False
                self.ui.enable_capture_buttons()

        except Exception as e:
            error_msg = f"Erro ao selecionar área: {str(e)}"
            if DEBUG:
                print(error_msg)
            self.ui.update_status("Ocorreu um erro")
            self.ui.update_result(f"Ocorreu um erro durante a seleção de área:\n{error_msg}")
            self.is_processing = False
            self.ui.enable_capture_buttons()

    def toggle_auto_monitoring(self):
        """Toggle automatic monitoring of screen changes."""
        try:
            if self.auto_monitoring:
                # Stop monitoring
                print("Parando monitoramento automático...")
                self.screen_monitor.stop_monitoring()
                self.auto_monitoring = False

                # Marcação automática desativada temporariamente
                # if self.auto_marker.is_enabled():
                #     self.auto_marker.disable()
                #     self.ui.update_status("Monitoramento e marcação automática desativados")
                # else:
                self.ui.update_status("Monitoramento automático desativado")

                self.ui.update_auto_monitor_button_text("Iniciar Monitoramento")
            else:
                # Start monitoring
                print("Iniciando monitoramento automático...")

                # Sempre seleciona uma nova janela para garantir que temos um handle válido
                self.ui.update_status("Selecione uma janela para monitorar...")
                try:
                    # Adiciona timeout para evitar travamento
                    import signal

                    def timeout_handler(signum, frame):
                        raise TimeoutError("Timeout na seleção de janela")

                    # Define timeout de 30 segundos (apenas no Windows isso pode não funcionar)
                    try:
                        signal.signal(signal.SIGALRM, timeout_handler)
                        signal.alarm(30)  # 30 segundos timeout
                    except:
                        pass  # SIGALRM pode não estar disponível no Windows

                    window_handle = self.screen_capture.select_window()

                    # Cancela o timeout
                    try:
                        signal.alarm(0)
                    except:
                        pass

                    if window_handle:
                        print(f"Janela selecionada com handle: {window_handle}")
                        self.current_window_handle = window_handle

                        # Captura uma imagem inicial para verificar se a captura funciona
                        test_image = self.screen_capture.capture_window()
                        if test_image is None:
                            print("Falha no teste de captura inicial")
                            self.ui.update_status("Falha ao capturar a janela selecionada. Tente novamente.")
                            return

                        print(f"Teste de captura bem-sucedido: {test_image.shape}")

                        # Captura e processa a primeira imagem imediatamente
                        self.ui.update_status("Processando primeira imagem...")

                        # Salva a imagem para processamento direto
                        image_path = "vision_capture.png"
                        cv2.imwrite(image_path, cv2.cvtColor(test_image, cv2.COLOR_RGB2BGR))

                        # Extrai o texto da imagem para melhorar o prompt
                        self.ui.update_status("Extraindo texto da imagem inicial...")
                        # Carrega a imagem para o OCR
                        ocr_image = cv2.imread(image_path)
                        # Tenta com português primeiro, se falhar usa inglês
                        try:
                            extracted_text = self.ocr_processor.extract_text_with_layout(ocr_image, lang='por')
                            if not extracted_text:
                                self.ui.update_status("Tentando extração em inglês...")
                                extracted_text = self.ocr_processor.extract_text_with_layout(ocr_image, lang='eng')
                        except Exception:
                            self.ui.update_status("Erro na extração em português, tentando em inglês...")
                            extracted_text = self.ocr_processor.extract_text_with_layout(ocr_image, lang='eng')

                        if extracted_text:
                            self.ui.update_status(f"Texto extraído com sucesso ({len(extracted_text)} caracteres). Enviando para processamento...")
                        else:
                            self.ui.update_status("Não foi possível extrair texto. Enviando imagem para processamento direto...")

                        # Obtém informações do modelo e provedor atuais
                        current_provider = self.llm_manager.get_provider_name()
                        current_model = self.llm_manager.client.model if hasattr(self.llm_manager.client, "model") else "desconhecido"

                        # Processa a imagem com o gerenciador de LLM usando validação, passando o texto extraído
                        self.ui.update_status(f"Enviando imagem para {current_provider} com o modelo {current_model}...")
                        answer = self.llm_manager.process_image_with_validation(image_path, extracted_text=extracted_text)

                        # Verifica se houve mudança de modelo ou provedor
                        new_provider = self.llm_manager.get_provider_name()
                        new_model = self.llm_manager.client.model if hasattr(self.llm_manager.client, "model") else "desconhecido"

                        if new_provider != current_provider or new_model != current_model:
                            # Determina o motivo da mudança com base no erro
                            if "quota" in answer.lower() or "429" in answer:
                                reason = "limitações de quota"
                            elif "503" in answer or "service unavailable" in answer.lower() or "unavailable" in answer.lower():
                                reason = "serviço indisponível"
                            else:
                                reason = "erro no provedor"

                            # Adiciona informação sobre a mudança de modelo/provedor à resposta
                            answer = f"[Alterado automaticamente para {new_provider} - {new_model} devido a {reason}]\n\n{answer}"

                        self.ui.update_result(answer)

                        # Marcação automática desativada temporariamente
                        # if self.auto_mark_enabled:
                        #     self.auto_marker.enable(window_handle)
                        #     self.ui.update_status("Marcação automática ativada. Primeira imagem processada.")
                        # else:
                        self.ui.update_status("Primeira imagem processada. Iniciando monitoramento...")

                        # Configura o monitor para detectar qualquer alteração visual por padrão
                        self.screen_monitor.set_detect_any_change(True)

                        # Depois inicia o monitoramento
                        success = self.screen_monitor.start_monitoring(window_handle)
                    else:
                        print("Nenhuma janela selecionada")
                        self.ui.update_status("Nenhuma janela selecionada. Tente novamente.")
                        return
                except TimeoutError:
                    error_msg = "Timeout na seleção de janela (30 segundos)"
                    print(error_msg)
                    self.ui.update_status("Timeout na seleção de janela. Tente novamente.")
                    return
                except KeyboardInterrupt:
                    error_msg = "Seleção de janela cancelada pelo usuário"
                    print(error_msg)
                    self.ui.update_status("Seleção cancelada. Tente novamente.")
                    return
                except Exception as e:
                    error_msg = f"Erro ao selecionar janela: {str(e)}"
                    print(error_msg)
                    import traceback
                    traceback.print_exc()

                    # Verifica se é um erro específico do Tkinter
                    if "tkinter" in str(e).lower() or "tk" in str(e).lower():
                        self.ui.update_status("Erro no diálogo de seleção. Reinicie o programa se necessário.")
                    else:
                        self.ui.update_status("Erro ao selecionar janela. Tente novamente.")
                    return

                if success:
                    self.auto_monitoring = True
                    self.ui.update_status("Monitoramento automático ativado - Aguardando mudanças na tela...")
                    self.ui.update_auto_monitor_button_text("Parar Monitoramento")
                else:
                    self.ui.update_status("Falha ao iniciar monitoramento automático. Tente novamente.")
        except Exception as e:
            error_msg = f"Erro ao alternar monitoramento: {str(e)}"
            print(error_msg)
            import traceback
            traceback.print_exc()
            self.ui.update_status("Erro ao alternar monitoramento. Tente novamente.")

    def test_detection(self):
        """Test the change detection by simulating a screen change."""
        if not self.current_window_handle:
            self.ui.update_status("Capture uma janela primeiro antes de testar a detecção")
            return

        if not self.auto_monitoring:
            self.ui.update_status("Ative o monitoramento primeiro antes de testar")
            return

        self.ui.update_status("Testando detecção de mudanças...")

        # Captura a tela atual
        image = self.screen_capture.capture_window()
        if image is None:
            self.ui.update_status("Falha ao capturar tela para teste")
            return

        # Salva a imagem original
        cv2.imwrite("test_original.png", cv2.cvtColor(image, cv2.COLOR_RGB2BGR))

        # Cria uma versão modificada da imagem (adiciona um retângulo)
        modified_image = image.copy()
        height, width = modified_image.shape[:2]
        cv2.rectangle(modified_image, (width//4, height//4),
                     (width*3//4, height*3//4), (0, 255, 0), 10)

        # Salva a imagem modificada
        cv2.imwrite("test_modified.png", cv2.cvtColor(modified_image, cv2.COLOR_RGB2BGR))

        # Simula uma mudança de tela
        self.ui.update_status("Simulando mudança de tela...")
        self.on_screen_change(modified_image)

    def on_screen_change(self, image):
        """Callback when a significant screen change is detected."""
        print("Callback de mudança de tela chamado!")

        # Salva a imagem para verificação
        cv2.imwrite("callback_image.png", cv2.cvtColor(image, cv2.COLOR_RGB2BGR))

        # Verifica se é a mesma imagem que já foi processada (usando hash)
        import hashlib
        image_hash = hashlib.md5(image.tobytes()).hexdigest()
        if hasattr(self, 'last_processed_image_hash') and image_hash == self.last_processed_image_hash:
            print(f"Imagem já foi processada, ignorando mudança")
            self.ui.update_status(f"Imagem já processada - Aguardando próxima mudança...")
            # Retoma o monitoramento sem processar
            self.screen_monitor.resume_monitoring()
            return

        # Armazena a imagem mais recente para processamento posterior
        self.latest_change_image = image.copy()
        self.has_pending_change = True

        # Garante que o monitoramento está pausado durante o processamento
        # (Isso já deve ter sido feito no _monitor_loop, mas é uma garantia adicional)
        self.screen_monitor.pause_monitoring()

        if self.is_processing:
            print("Já está processando uma imagem, armazenando esta mudança para processamento posterior")
            self.ui.update_status("Nova mudança detectada - Será processada após a atual")
            return

        # Processa a imagem mais recente
        self.process_latest_change()

    def process_latest_change(self):
        """Processa a mudança mais recente usando visão direta."""
        if not hasattr(self, 'latest_change_image') or self.latest_change_image is None:
            print("Nenhuma imagem para processar")
            # Retoma o monitoramento se não houver imagem para processar
            if self.auto_monitoring:
                self.screen_monitor.resume_monitoring()
            return

        # Define a flag de processamento para evitar processamentos simultâneos
        self.is_processing = True

        print("Iniciando processamento automático da nova tela com visão direta")
        self.ui.update_status("Mudança detectada - Processando com visão direta...")
        self.has_pending_change = False

        # Processa a imagem em uma thread separada para não bloquear o monitoramento
        processing_thread = threading.Thread(target=self._process_change_with_vision)
        processing_thread.daemon = True
        processing_thread.start()

    def _process_change_with_vision(self):
        """Processa a mudança usando visão direta."""
        try:
            # Verifica a conexão com a janela antes de processar
            if not self.check_window_connection():
                self.ui.update_status("Processamento cancelado - conexão com janela perdida")
                return

            # Inicia o contador de tempo
            start_time = datetime.now()

            # Salva a imagem para processamento direto
            image_path = "vision_capture.png"
            cv2.imwrite(image_path, cv2.cvtColor(self.latest_change_image, cv2.COLOR_RGB2BGR))

            # Extrai o texto da imagem para melhorar o prompt
            self.ui.update_status("Extraindo texto da imagem...")
            # Carrega a imagem para o OCR
            ocr_image = cv2.imread(image_path)
            # Tenta com português primeiro, se falhar usa inglês
            try:
                extracted_text = self.ocr_processor.extract_text_with_layout(ocr_image, lang='por')
                if not extracted_text:
                    self.ui.update_status("Tentando extração em inglês...")
                    extracted_text = self.ocr_processor.extract_text_with_layout(ocr_image, lang='eng')
            except Exception:
                self.ui.update_status("Erro na extração em português, tentando em inglês...")
                extracted_text = self.ocr_processor.extract_text_with_layout(ocr_image, lang='eng')

            if extracted_text:
                self.ui.update_status(f"Texto extraído com sucesso ({len(extracted_text)} caracteres). Enviando para processamento...")
            else:
                self.ui.update_status("Não foi possível extrair texto. Enviando imagem para processamento direto...")

            # Obtém informações do modelo e provedor atuais
            current_provider = self.llm_manager.get_provider_name()
            current_model = self.llm_manager.client.model if hasattr(self.llm_manager.client, "model") else "desconhecido"

            # Processa a imagem com o gerenciador de LLM usando validação, passando o texto extraído
            self.ui.update_status(f"Enviando imagem para {current_provider} com o modelo {current_model}...")
            answer = self.llm_manager.process_image_with_validation(image_path, extracted_text=extracted_text)

            # Verifica se houve mudança de modelo ou provedor
            new_provider = self.llm_manager.get_provider_name()
            new_model = self.llm_manager.client.model if hasattr(self.llm_manager.client, "model") else "desconhecido"

            if new_provider != current_provider or new_model != current_model:
                # Determina o motivo da mudança com base no erro
                if "quota" in answer.lower() or "429" in answer:
                    reason = "limitações de quota"
                elif "503" in answer or "service unavailable" in answer.lower() or "unavailable" in answer.lower():
                    reason = "serviço indisponível"
                else:
                    reason = "erro no provedor"

                # Adiciona informação sobre a mudança de modelo/provedor à resposta
                answer = f"[Alterado automaticamente para {new_provider} - {new_model} devido a {reason}]\n\n{answer}"

            # Calcula o tempo de processamento
            end_time = datetime.now()
            processing_time = (end_time - start_time).total_seconds()

            # Registra a questão no sistema de logging
            try:
                # Extrai confiança da resposta (se disponível)
                confidence = self._extract_confidence_from_response(answer)

                # Usa hash da imagem como identificador da questão
                import hashlib
                current_question = hashlib.md5(self.latest_change_image.tobytes()).hexdigest()[:8]

                self.question_logger.log_question(
                    ocr_text=extracted_text or "",
                    llm_response=answer,
                    llm_provider=new_provider,
                    llm_model=new_model,
                    llm_confidence=confidence,
                    processing_time=processing_time,
                    image_path=image_path,
                    question_number=current_question,
                    additional_metadata={
                        'mode': 'auto_monitoring',
                        'window_handle': str(self.current_window_handle) if self.current_window_handle else None,
                        'provider_changed': new_provider != current_provider,
                        'model_changed': new_model != current_model
                    }
                )
            except Exception as e:
                if DEBUG:
                    print(f"Erro ao registrar questão no log: {e}")

            # Adiciona o tempo de processamento à resposta
            answer += f"\n\n[Tempo de processamento: {processing_time:.2f} segundos]"

            # Update UI with the answer
            self.ui.update_status("Pronto - Resposta abaixo")
            self.ui.update_result(answer)

            # Registra a imagem processada para evitar reprocessamento
            if hasattr(self, 'latest_change_image') and self.latest_change_image is not None:
                self.last_processed_image_hash = hashlib.md5(self.latest_change_image.tobytes()).hexdigest()
                print(f"Imagem processada e registrada (hash: {self.last_processed_image_hash[:8]})")

            # Marcação automática desativada temporariamente para garantir estabilidade
            # if self.auto_mark_enabled and self.auto_marker.is_enabled():
            #     self.ui.update_status("Tentando marcar a resposta automaticamente...")
            #
            #     # Configura o handle da janela se necessário
            #     if self.current_window_handle and not self.auto_marker.window_handle:
            #         self.auto_marker.set_window_handle(self.current_window_handle)
            #
            #     # Tenta marcar a resposta
            #     success = self.auto_marker.mark_answer(answer)
            #
            #     if success:
            #         self.ui.update_status("Resposta marcada automaticamente")
            #     else:
            #         self.ui.update_status("Não foi possível marcar a resposta automaticamente")

            # Retoma o monitoramento após processar a imagem
            if self.auto_monitoring:
                # Garante que a detecção de qualquer alteração visual esteja ativada
                self.screen_monitor.set_detect_any_change(True)
                self.screen_monitor.resume_monitoring()
                self.ui.update_status("Monitoramento ativo - Aguardando próxima mudança na tela...")

        except Exception as e:
            error_msg = f"Erro ao processar imagem com visão direta: {str(e)}"
            if DEBUG:
                print(error_msg)
            self.ui.update_status("Ocorreu um erro")
            self.ui.update_result(f"Ocorreu um erro durante o processamento:\n{error_msg}")

            # Retoma o monitoramento mesmo em caso de erro
            if self.auto_monitoring:
                self.screen_monitor.resume_monitoring()

        finally:
            self.is_processing = False

            # Verifica se há mudanças pendentes para processar
            if self.has_pending_change and self.auto_monitoring:
                print("Processando mudança pendente após conclusão do processamento atual")
                self.ui.update_status("Processando mudança pendente...")
                # Pequeno delay para atualizar a interface
                import time
                time.sleep(0.5)
                # Processa a mudança pendente
                self.process_latest_change()

    def process_window(self):
        """Process a selected window in a separate thread."""
        if self.is_processing:
            self.ui.update_status("Já processando, por favor aguarde...")
            return

        self.is_processing = True
        self.ui.update_status("Por favor, selecione uma janela para capturar...")
        self.ui.disable_capture_buttons()

        # Start processing in a separate thread
        self.processing_thread = threading.Thread(target=self._process_window_thread)
        self.processing_thread.daemon = True
        self.processing_thread.start()

    def _process_window_thread(self):
        """Thread function for processing a window."""
        try:
            # Capture the window
            self.ui.update_status("Selecionando e capturando janela...")
            image = self.screen_capture.capture_window()

            # Store the window handle for auto-monitoring
            if self.screen_capture.window_handle:
                self.current_window_handle = self.screen_capture.window_handle

            if image is None:
                self.ui.update_status("Falha ao capturar janela. Por favor, tente novamente.")
                self.ui.update_result("Erro: Não foi possível capturar a janela. Por favor, tente novamente.")
                self.is_processing = False
                self.ui.enable_capture_buttons()
                return

            # Process the captured image
            self.process_image(image)

        except Exception as e:
            error_msg = f"Erro ao processar janela: {str(e)}"
            if DEBUG:
                print(error_msg)
            self.ui.update_status("Ocorreu um erro")
            self.ui.update_result(f"Ocorreu um erro durante o processamento:\n{error_msg}")
            self.is_processing = False
            self.ui.enable_capture_buttons()



    def process_image(self, image):
        """Process an already captured image."""
        try:

            # Always save the capture for debugging
            self.screen_capture.save_capture("current_capture.png")

            # Show the captured image and ask for confirmation (only in manual mode)
            if not self.auto_monitoring:
                self.ui.update_status("Mostrando imagem capturada. Confirme se o conteúdo está visível...")

                # Display the image in a window
                window_name = "Imagem Capturada (Pressione qualquer tecla para continuar)"
                cv2.namedWindow(window_name, cv2.WINDOW_NORMAL)
                cv2.imshow(window_name, cv2.cvtColor(image, cv2.COLOR_RGB2BGR))

                # Wait for user input
                cv2.waitKey(0)
                cv2.destroyAllWindows()
            else:
                # In auto mode, just log that we're processing a new image
                if DEBUG:
                    print("Processando nova imagem automaticamente")

            # Pre-process the image for better OCR
            self.ui.update_status("Pré-processando imagem para OCR...")

            # Salva a imagem original
            cv2.imwrite("original_capture.png", cv2.cvtColor(image, cv2.COLOR_RGB2BGR))

            # Recorta a imagem para focar na área da questão
            self.ui.update_status("Detectando e recortando área da questão...")
            cropped_image = self.image_processor.crop_to_question(image)
            cv2.imwrite("cropped_question.png", cv2.cvtColor(cropped_image, cv2.COLOR_RGB2BGR))

            # Melhora a imagem para OCR
            self.ui.update_status("Melhorando imagem para OCR...")
            enhanced_image = self.image_processor.enhance_image_for_ocr(cropped_image)
            cv2.imwrite("enhanced_for_ocr.png", enhanced_image)

            # Usa a imagem melhorada para o processamento
            image = cropped_image

            # Também prepara versões em escala de cinza e limiarizada para backup
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            _, thresh = cv2.threshold(gray, 150, 255, cv2.THRESH_BINARY)

            # Extract text using OCR with multiple approaches
            self.ui.update_status("Extraindo texto com OCR (tentando múltiplas abordagens)...")

            # Primeiro tenta com a imagem melhorada em inglês
            extracted_text = self.ocr_processor.extract_text_with_layout(enhanced_image, lang='eng')
            if DEBUG:
                print("Enhanced image OCR result length:", len(extracted_text) if extracted_text else 0)

            # Tenta com a imagem recortada em inglês
            if not extracted_text or len(extracted_text.strip()) < 20:
                cropped_text = self.ocr_processor.extract_text_with_layout(cropped_image, lang='eng')
                if cropped_text and (not extracted_text or len(cropped_text) > len(extracted_text)):
                    extracted_text = cropped_text
                    if DEBUG:
                        print("Cropped image OCR result length:", len(extracted_text))

            # Tenta com português na imagem melhorada
            if not extracted_text or len(extracted_text.strip()) < 20:
                por_text = self.ocr_processor.extract_text_with_layout(enhanced_image, lang='por')
                if por_text and (not extracted_text or len(por_text) > len(extracted_text)):
                    extracted_text = por_text
                    if DEBUG:
                        print("Portuguese OCR result length:", len(extracted_text))

            # Tenta com português+inglês na imagem melhorada
            por_eng_text = self.ocr_processor.extract_text_with_layout(enhanced_image, lang='por+eng')
            if por_eng_text and (not extracted_text or len(por_eng_text) > len(extracted_text)):
                extracted_text = por_eng_text
                if DEBUG:
                    print("Portuguese+English OCR result length:", len(extracted_text))

            # Tenta com imagem em escala de cinza
            gray_text = self.ocr_processor.extract_text_with_layout(gray, lang='eng')
            if gray_text and (not extracted_text or len(gray_text) > len(extracted_text)):
                extracted_text = gray_text
                if DEBUG:
                    print("Grayscale image OCR result length:", len(extracted_text))

            # Tenta com imagem limiarizada
            thresh_text = self.ocr_processor.extract_text_with_layout(thresh, lang='eng')
            if thresh_text and (not extracted_text or len(thresh_text) > len(extracted_text)):
                extracted_text = thresh_text
                if DEBUG:
                    print("Thresholded image OCR result length:", len(extracted_text))

            # If all OCR attempts fail
            if not extracted_text:
                self.ui.update_status("Nenhum texto extraído da janela.")
                self.ui.update_result("Erro: Não foi possível extrair texto da janela. Por favor, tente novamente.\n\nPossíveis razões:\n1. O texto pode estar em uma fonte incomum\n2. O texto pode ser muito pequeno\n3. O texto pode estar incorporado em uma imagem\n4. O Tesseract OCR pode não estar instalado corretamente")

                # Check if Tesseract is installed
                import os
                from config import TESSERACT_PATH
                if not os.path.exists(TESSERACT_PATH):
                    self.ui.update_result(self.ui.result_text.toPlainText() + f"\n\nTesseract não encontrado em: {TESSERACT_PATH}\nPor favor, instale o Tesseract OCR e atualize o caminho no arquivo config.py")

                self.is_processing = False
                self.ui.enable_capture_buttons()
                return

            # Show the extracted text for verification (only in manual mode)
            if not self.auto_monitoring:
                self.ui.update_status("Texto extraído com sucesso. Mostrando texto extraído...")

                # Create a window to show the extracted text
                extracted_window = "Texto Extraído (Pressione 'c' para continuar, 'q' para cancelar)"

                # Create a blank image to display text
                text_img = np.ones((800, 1000, 3), dtype=np.uint8) * 255

                # Add text to the image
                font = cv2.FONT_HERSHEY_SIMPLEX
                font_scale = 0.5
                font_color = (0, 0, 0)  # Black
                line_type = 1

                # Split text into lines and display
                text_lines = extracted_text.split('\n')
                y_position = 30
                for line in text_lines:
                    cv2.putText(text_img, line, (30, y_position), font, font_scale, font_color, line_type)
                    y_position += 25
                    if y_position > 780:  # Prevent text from going off the image
                        cv2.putText(text_img, "... (texto truncado)", (30, y_position), font, font_scale, font_color, line_type)
                        break

                # Display the text image
                cv2.namedWindow(extracted_window, cv2.WINDOW_NORMAL)
                cv2.imshow(extracted_window, text_img)

                # Wait for user input
                key = cv2.waitKey(0) & 0xFF
                cv2.destroyAllWindows()

                # If user pressed 'q', cancel the operation
                if key == ord('q'):
                    self.ui.update_status("Processamento cancelado pelo usuário.")
                    self.ui.update_result("O processamento foi cancelado. Por favor, tente capturar novamente.")
                    self.is_processing = False
                    self.ui.enable_capture_buttons()
                    return
            else:
                # No modo automático, apenas registra o texto extraído
                print(f"Texto extraído automaticamente ({len(extracted_text)} caracteres)")
                # Não mostra janela de confirmação no modo automático

            # Processa o texto extraído para identificar perguntas e alternativas
            self.ui.update_status("Analisando e organizando texto extraído...")

            # Verifica se o texto extraído é válido
            if not extracted_text or len(extracted_text.strip()) < 10:
                self.ui.update_status("Texto extraído muito curto ou vazio")
                self.ui.update_result("Erro: O texto extraído é muito curto ou vazio. Por favor, tente capturar novamente.")
                self.is_processing = False
                self.ui.enable_capture_buttons()
                return

            # Usa o processador de texto para extrair e organizar o conteúdo
            processed_text = self.text_processor.process_text(extracted_text)

            print(f"\n=== TEXTO PROCESSADO ===")
            print(f"Tamanho: {len(processed_text)} caracteres")
            print(f"Amostra: {processed_text[:200]}...")

            # Extrai a pergunta e as alternativas
            question, alternatives, _ = self.text_processor.extract_question_and_alternatives(extracted_text)

            if question:
                question_text = question
                context_text = "\n".join(alternatives) if alternatives else processed_text
                print(f"Pergunta identificada: {question_text[:100]}...")
                print(f"Alternativas: {len(alternatives)} encontradas")
            else:
                # Se não encontrou uma pergunta clara, trata todo o texto como contexto
                context_text = processed_text
                question_text = "Por favor, analise este texto e forneça as informações mais relevantes."
                print("Nenhuma pergunta identificada, usando texto completo como contexto")

            # Save extracted text to file for reference
            with open("extracted_text.txt", "w", encoding="utf-8") as f:
                f.write("TEXTO EXTRAÍDO COMPLETO:\n")
                f.write(extracted_text)
                f.write("\n\nTEXTO PROCESSADO:\n")
                f.write(processed_text)
                f.write("\n\nPERGUNTA:\n")
                f.write(question_text)
                f.write("\n\nALTERNATIVAS:\n")
                f.write(context_text)

            # Process with LLM
            self.ui.update_status("Processando com LLM...")

            # Sempre imprimir o texto que será enviado para a LLM
            print("\n=== PERGUNTA ENVIADA PARA LLM ===")
            print(question_text)
            print("\n=== CONTEXTO ENVIADO PARA LLM ===")
            print(context_text)

            # Verificar se o texto está vazio ou muito curto
            if not context_text or len(context_text.strip()) < 10:
                self.ui.update_status("Texto extraído muito curto ou vazio")
                self.ui.update_result("Erro: O texto extraído é muito curto ou vazio. Por favor, tente capturar novamente.")
                return

            # Obtém informações do modelo e provedor atuais
            current_provider = self.llm_manager.get_provider_name()
            current_model = self.llm_manager.client.model if hasattr(self.llm_manager.client, "model") else "desconhecido"

            # Atualiza o status com informações do modelo
            self.ui.update_status(f"Processando com {current_provider} - {current_model}...")

            # Inicia contador de tempo para logging
            start_time = datetime.now()

            # Forçar o envio do texto completo para a LLM
            answer = self.llm_client.process_question(question_text, context_text)

            # Calcula tempo de processamento
            end_time = datetime.now()
            processing_time = (end_time - start_time).total_seconds()

            # Verifica se houve mudança de modelo ou provedor
            new_provider = self.llm_manager.get_provider_name()
            new_model = self.llm_manager.client.model if hasattr(self.llm_manager.client, "model") else "desconhecido"

            if new_provider != current_provider or new_model != current_model:
                # Determina o motivo da mudança com base no erro
                if "quota" in answer.lower() or "429" in answer:
                    reason = "limitações de quota"
                elif "503" in answer or "service unavailable" in answer.lower() or "unavailable" in answer.lower():
                    reason = "serviço indisponível"
                else:
                    reason = "erro no provedor"

                # Adiciona informação sobre a mudança de modelo/provedor à resposta
                answer = f"[Alterado automaticamente para {new_provider} - {new_model} devido a {reason}]\n\n{answer}"

            # Registra a questão no sistema de logging
            try:
                # Extrai confiança da resposta (se disponível)
                confidence = self._extract_confidence_from_response(answer)

                self.question_logger.log_question(
                    ocr_text=extracted_text or "",
                    llm_response=answer,
                    llm_provider=new_provider,
                    llm_model=new_model,
                    llm_confidence=confidence,
                    processing_time=processing_time,
                    image_path="current_capture.png",  # Imagem salva anteriormente
                    additional_metadata={
                        'mode': 'manual_process_image',
                        'window_handle': str(self.current_window_handle) if self.current_window_handle else None,
                        'provider_changed': new_provider != current_provider,
                        'model_changed': new_model != current_model,
                        'question_text': question_text,
                        'alternatives_count': len(alternatives) if 'alternatives' in locals() else 0
                    }
                )
            except Exception as e:
                if DEBUG:
                    print(f"Erro ao registrar questão no log: {e}")

            print("\n=== RESPOSTA DA LLM ===")
            print(answer)

            # Update UI with just the answer (more concise)
            self.ui.update_status("Pronto - Resposta abaixo")
            # Just show the answer without repeating the question
            self.ui.update_result(answer)

            # If auto-monitoring is active, resume monitoring and update status
            if self.auto_monitoring:
                # Garante que a detecção de qualquer alteração visual esteja ativada
                self.screen_monitor.set_detect_any_change(True)
                # Retoma o monitoramento após processar a imagem
                self.screen_monitor.resume_monitoring()
                self.ui.update_status("Monitoramento ativo - Aguardando próxima mudança na tela...")

        except Exception as e:
            error_msg = f"Erro ao processar imagem: {str(e)}"
            if DEBUG:
                print(error_msg)
            self.ui.update_status("Ocorreu um erro")
            self.ui.update_result(f"Ocorreu um erro durante o processamento:\n{error_msg}")

        finally:
            self.is_processing = False
            self.ui.enable_capture_buttons()

            # Verifica se há mudanças pendentes para processar
            if self.has_pending_change and self.auto_monitoring:
                print("Processando mudança pendente após conclusão do processamento atual")
                self.ui.update_status("Processando mudança pendente...")
                # Pequeno delay para atualizar a interface
                import time
                time.sleep(0.5)
                # Processa a mudança pendente
                self.process_latest_change()
            elif self.auto_monitoring and not self.screen_monitor.is_paused():
                # Garante que o monitoramento esteja ativo se não houver mudanças pendentes
                print("Verificando estado do monitoramento após processamento...")
                if not self.screen_monitor.is_monitoring():
                    print("Reiniciando monitoramento após processamento")
                    self.screen_monitor.start_monitoring(self.current_window_handle)
                    self.ui.update_status("Monitoramento reiniciado - Aguardando mudanças na tela...")
                else:
                    print("Monitoramento já está ativo")

if __name__ == "__main__":
    # Inicializa a aplicação QApplication primeiro
    app = QApplication(sys.argv)

    # Inicializa o módulo PyQtWebEngine se os anúncios estiverem habilitados
    settings = QSettings("BoZolinO", "Python Question Helper")
    # Força a desativação dos anúncios
    settings.setValue("ads/enabled", False)
    ads_enabled = False

    # Verifica se o modo de depuração está ativado
    debug_mode = settings.value("debug", DEBUG, type=bool)
    if debug_mode:
        print("Modo de depuração ativado")
    else:
        print("Modo de produção ativado")

    # Módulo de anúncios desativado
    print("Módulo de anúncios desativado - Ignorando inicialização do PyQtWebEngine")

    # Cria a instância do QuestionHelper, mas não cria uma nova QApplication
    helper = QuestionHelper()

    # Modifica o método run para não criar uma nova QApplication
    original_run = helper.run
    def modified_run():
        helper.ui.show()
        helper.ui.update_status("Pronto - Selecione um modelo e inicie o monitoramento")
        helper.show_welcome_message()

        # Exibe informações sobre os anúncios
        print("Módulo de anúncios desativado.")

        return app.exec_()

    helper.run = modified_run

    # Executa a aplicação
    print("Iniciando aplicação Python Question Helper...")
    exit_code = helper.run()
    print("Aplicação encerrada.")
    sys.exit(exit_code)
