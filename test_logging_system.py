#!/usr/bin/env python3
"""
Teste completo do sistema de logging e análise do Python Question Helper.
Testa todas as funcionalidades implementadas.
"""

import os
import sys
import time
import cv2
import numpy as np
from datetime import datetime

# Adiciona o diretório atual ao path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from question_logger import QuestionLogger
from analytics_module import AnalyticsModule
from config import DEBUG

def create_test_data():
    """Cria dados de teste para o sistema de logging."""
    print("=== CRIANDO DADOS DE TESTE ===\n")
    
    logger = QuestionLogger()
    
    # Dados de teste simulados
    test_questions = [
        {
            'ocr_text': 'Questão 1: Qual é a velocidade máxima permitida em vias urbanas? A) 40 km/h B) 50 km/h C) 60 km/h D) 70 km/h',
            'llm_response': 'Resposta correta: C) 60 km/h\n\nEm vias urbanas, a velocidade máxima permitida é de 60 km/h, conforme estabelecido pelo Código de Trânsito Brasileiro.',
            'provider': 'OpenRouter',
            'model': 'gpt-4-vision-preview',
            'confidence': 95,
            'processing_time': 2.3,
            'domain': 'legislacao'
        },
        {
            'ocr_text': 'Questão 2: Em caso de acidente, qual é o primeiro procedimento? A) Sinalizar o local B) Chamar a polícia C) Verificar feridos D) Remover os veículos',
            'llm_response': 'Resposta correta: C) Verificar feridos\n\nO primeiro procedimento em caso de acidente é verificar se há feridos e prestar os primeiros socorros necessários.',
            'provider': 'Gemini',
            'model': 'gemini-pro-vision',
            'confidence': 88,
            'processing_time': 1.8,
            'domain': 'primeiros_socorros'
        },
        {
            'ocr_text': 'Questão 3: A placa R-1 significa: A) Parada obrigatória B) Proibido virar à esquerda C) Velocidade máxima D) Sentido proibido',
            'llm_response': 'Resposta correta: A) Parada obrigatória\n\nA placa R-1 indica parada obrigatória, sendo uma placa de regulamentação.',
            'provider': 'HuggingFace',
            'model': 'llava-hf/llava-1.5-7b-hf',
            'confidence': 72,
            'processing_time': 4.1,
            'domain': 'sinalizacao'
        },
        {
            'ocr_text': 'Questão 4: Para calcular a distância de frenagem, considera-se: A) Apenas a velocidade B) Velocidade e peso C) Velocidade, peso e condições da via D) Apenas as condições da via',
            'llm_response': 'Resposta correta: C) Velocidade, peso e condições da via\n\nA distância de frenagem depende de múltiplos fatores incluindo velocidade, peso do veículo e condições da via.',
            'provider': 'OpenRouter',
            'model': 'gpt-4-vision-preview',
            'confidence': 91,
            'processing_time': 2.7,
            'domain': 'direcao_defensiva'
        },
        {
            'ocr_text': 'Questão 5: Qual combustível é menos poluente? A) Gasolina B) Diesel C) Etanol D) GNV',
            'llm_response': 'Resposta correta: D) GNV\n\nO GNV (Gás Natural Veicular) é considerado o combustível menos poluente entre as opções apresentadas.',
            'provider': 'Gemini',
            'model': 'gemini-pro-vision',
            'confidence': 85,
            'processing_time': 2.1,
            'domain': 'meio_ambiente'
        }
    ]
    
    # Cria imagem de teste
    test_image = np.ones((400, 600, 3), dtype=np.uint8) * 255
    cv2.putText(test_image, "IMAGEM DE TESTE", (150, 200), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
    test_image_path = "test_question_image.png"
    cv2.imwrite(test_image_path, test_image)
    
    # Registra questões de teste
    question_hashes = []
    for i, question in enumerate(test_questions):
        print(f"Registrando questão {i+1}...")
        
        hash_id = logger.log_question(
            ocr_text=question['ocr_text'],
            llm_response=question['llm_response'],
            llm_provider=question['provider'],
            llm_model=question['model'],
            llm_confidence=question['confidence'],
            processing_time=question['processing_time'],
            image_path=test_image_path,
            question_number=i+1,
            additional_metadata={
                'test_data': True,
                'domain_override': question['domain']
            }
        )
        
        if hash_id:
            question_hashes.append(hash_id)
            print(f"✅ Questão {i+1} registrada: {hash_id[:8]}")
        else:
            print(f"❌ Erro ao registrar questão {i+1}")
    
    # Simula algumas validações manuais
    print(f"\nSimulando validações manuais...")
    validations = [
        (question_hashes[0], True, None, "Resposta correta e bem explicada"),
        (question_hashes[1], True, None, "Procedimento correto"),
        (question_hashes[2], False, "Interpretação incorreta", "Confundiu com outra placa"),
        (question_hashes[3], True, None, "Explicação completa"),
        (question_hashes[4], False, "Conhecimento desatualizado", "Informação pode estar desatualizada")
    ]
    
    for hash_id, is_correct, error_type, notes in validations:
        if hash_id:
            success = logger.mark_validation(hash_id, is_correct, error_type, notes)
            status = "✅" if success else "❌"
            print(f"{status} Validação: {hash_id[:8]} - {'Correto' if is_correct else 'Incorreto'}")
    
    print(f"\n✅ Dados de teste criados com sucesso!")
    print(f"📊 Total de questões: {len(question_hashes)}")
    print(f"📝 Validações realizadas: {len(validations)}")
    
    return logger, question_hashes

def test_analytics():
    """Testa o módulo de análise."""
    print("\n=== TESTANDO MÓDULO DE ANÁLISE ===\n")
    
    analytics = AnalyticsModule()
    
    # Gera relatório completo
    print("Gerando relatório abrangente...")
    report = analytics.generate_comprehensive_report(days_back=7)
    
    if 'error' in report:
        print(f"❌ Erro no relatório: {report['error']}")
        return False
    
    # Exibe resumo do relatório
    print("✅ Relatório gerado com sucesso!")
    print(f"\n📊 RESUMO DO RELATÓRIO:")
    print(f"   • Período: {report['period']['start_date']} a {report['period']['end_date']}")
    print(f"   • Total de questões: {report['period']['total_questions']}")
    
    if 'accuracy_analysis' in report and 'overall_accuracy' in report['accuracy_analysis']:
        accuracy = report['accuracy_analysis']['overall_accuracy']
        print(f"   • Precisão geral: {accuracy:.1%}")
    
    if 'confidence_analysis' in report and 'statistics' in report['confidence_analysis']:
        avg_conf = report['confidence_analysis']['statistics']['mean']
        print(f"   • Confiança média: {avg_conf:.1f}%")
    
    if 'domain_analysis' in report and 'total_domains' in report['domain_analysis']:
        domains = report['domain_analysis']['total_domains']
        print(f"   • Domínios analisados: {domains}")
    
    if 'recommendations' in report:
        print(f"   • Recomendações: {len(report['recommendations'])}")
    
    # Testa geração de relatório visual
    print(f"\nGerando relatório visual...")
    visual_success = analytics.generate_visual_report("test_analytics_output")
    
    if visual_success:
        print("✅ Relatório visual gerado em 'test_analytics_output/'")
    else:
        print("⚠️ Erro ao gerar relatório visual (pode ser falta de matplotlib)")
    
    return True

def test_review_interface():
    """Testa a interface de revisão (apenas importação)."""
    print("\n=== TESTANDO INTERFACE DE REVISÃO ===\n")
    
    try:
        from review_interface import ReviewInterface
        print("✅ Interface de revisão importada com sucesso")
        print("ℹ️ Para testar completamente, execute: python review_interface.py")
        return True
    except ImportError as e:
        print(f"❌ Erro ao importar interface de revisão: {e}")
        print("⚠️ Verifique se PyQt5 está instalado")
        return False

def test_statistics():
    """Testa estatísticas do logger."""
    print("\n=== TESTANDO ESTATÍSTICAS ===\n")
    
    logger = QuestionLogger()
    stats = logger.get_statistics()
    
    if not stats:
        print("❌ Erro ao obter estatísticas")
        return False
    
    print("✅ Estatísticas obtidas com sucesso!")
    print(f"\n📈 ESTATÍSTICAS GERAIS:")
    print(f"   • Total de questões: {stats.get('total_questions', 0)}")
    print(f"   • Questões validadas: {stats.get('validated_questions', 0)}")
    print(f"   • Respostas corretas: {stats.get('correct_answers', 0)}")
    print(f"   • Precisão geral: {stats.get('overall_accuracy', 0):.1%}")
    print(f"   • Taxa de validação: {stats.get('validation_rate', 0):.1%}")
    print(f"   • Tempo médio: {stats.get('avg_processing_time', 0):.2f}s")
    
    # Estatísticas por domínio
    if 'domain_stats' in stats and stats['domain_stats']:
        print(f"\n📊 POR DOMÍNIO:")
        for domain_stat in stats['domain_stats']:
            domain = domain_stat['domain']
            count = domain_stat['count']
            accuracy = domain_stat['accuracy']
            print(f"   • {domain}: {count} questões, {accuracy:.1%} precisão")
    
    # Estatísticas por provedor
    if 'provider_stats' in stats and stats['provider_stats']:
        print(f"\n🤖 POR PROVEDOR:")
        for provider_stat in stats['provider_stats']:
            provider = provider_stat['provider']
            count = provider_stat['count']
            accuracy = provider_stat['accuracy']
            avg_conf = provider_stat['avg_confidence']
            print(f"   • {provider}: {count} questões, {accuracy:.1%} precisão, {avg_conf:.1f}% confiança")
    
    return True

def test_export():
    """Testa exportação de dados."""
    print("\n=== TESTANDO EXPORTAÇÃO ===\n")
    
    logger = QuestionLogger()
    
    # Testa exportação CSV
    csv_file = "test_export.csv"
    csv_success = logger.export_data(csv_file, 'csv')
    
    if csv_success and os.path.exists(csv_file):
        print(f"✅ Exportação CSV bem-sucedida: {csv_file}")
        file_size = os.path.getsize(csv_file)
        print(f"   Tamanho: {file_size} bytes")
    else:
        print(f"❌ Erro na exportação CSV")
    
    # Testa exportação JSON
    json_file = "test_export.json"
    json_success = logger.export_data(json_file, 'json')
    
    if json_success and os.path.exists(json_file):
        print(f"✅ Exportação JSON bem-sucedida: {json_file}")
        file_size = os.path.getsize(json_file)
        print(f"   Tamanho: {file_size} bytes")
    else:
        print(f"❌ Erro na exportação JSON")
    
    return csv_success and json_success

def main():
    """Função principal de teste."""
    print("🔧 TESTANDO SISTEMA DE LOGGING E ANÁLISE")
    print("="*60)
    
    # Executa os testes
    logger, hashes = create_test_data()
    test1_passed = test_statistics()
    test2_passed = test_analytics()
    test3_passed = test_review_interface()
    test4_passed = test_export()
    
    print("\n" + "="*60)
    print("📋 RELATÓRIO FINAL:")
    print(f"✅ Criação de dados: {'PASSOU' if hashes else 'FALHOU'}")
    print(f"✅ Estatísticas: {'PASSOU' if test1_passed else 'FALHOU'}")
    print(f"✅ Análise: {'PASSOU' if test2_passed else 'FALHOU'}")
    print(f"✅ Interface de revisão: {'PASSOU' if test3_passed else 'FALHOU'}")
    print(f"✅ Exportação: {'PASSOU' if test4_passed else 'FALHOU'}")
    
    total_passed = sum([bool(hashes), test1_passed, test2_passed, test3_passed, test4_passed])
    
    if total_passed == 5:
        print("\n🎉 SISTEMA DE LOGGING TOTALMENTE FUNCIONAL!")
        print("🔥 Funcionalidades implementadas:")
        print("   • Logging estruturado de questões")
        print("   • Categorização automática por domínio")
        print("   • Sistema de validação manual")
        print("   • Análise de padrões e erros")
        print("   • Geração de insights e recomendações")
        print("   • Interface de revisão (PyQt5)")
        print("   • Exportação de dados (CSV/JSON)")
        print("   • Relatórios visuais (matplotlib)")
        print("\n✨ O sistema está pronto para uso em produção!")
        
        print("\n📖 PRÓXIMOS PASSOS:")
        print("1. Execute 'python review_interface.py' para revisar questões")
        print("2. Use analytics_module para gerar relatórios periódicos")
        print("3. Configure limpeza automática de dados antigos")
        print("4. Monitore métricas de qualidade regularmente")
        
    else:
        print(f"\n⚠️ {5-total_passed} teste(s) falharam.")
        print("Verifique os logs acima para detalhes.")
    
    print("="*60)

if __name__ == "__main__":
    main()
