"""
<PERSON><PERSON><PERSON><PERSON> para criar um apontador visual que destaca a resposta correta na tela.
"""
import re
import time
import cv2
import numpy as np
import pyautogui
from PyQt5.QtWidgets import QWidget, QLabel, QVBoxLayout
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QColor, QPainter, QPen, QBrush
from config import DEBUG

class AnswerPointer:
    """
    Classe para criar um apontador visual que destaca a resposta correta na tela.
    """
    def __init__(self):
        """
        Inicializa o apontador visual.
        """
        self.window_handle = None
        self.is_active = False
        self.overlay = None
        self.answer_letter = None
        self.answer_position = None

    def extract_answer_letter(self, answer_text):
        """
        Extrai a letra da resposta correta do texto da LLM.

        Args:
            answer_text: Texto da resposta da LLM

        Returns:
            Letra da resposta correta ou None se não encontrada
        """
        if not answer_text:
            return None

        # Imprime o texto da resposta para debug
        if DEBUG:
            print(f"Texto da resposta: {answer_text[:100]}...")

        # Padrões para encontrar a letra da resposta
        patterns = [
            r'Resposta correta:\s*([A-Ea-e])\)',  # Resposta correta: A)
            r'Resposta correta:\s*\(([A-Ea-e])\)',  # Resposta correta: (A)
            r'Resposta correta:\s*([A-Ea-e])[\.:\)]',  # Resposta correta: A. ou A: ou A)
            r'Resposta correta:\s*([A-Ea-e])',  # Resposta correta: A (sem pontuação)
            r'Resposta:\s*([A-Ea-e])[\.:\)]',  # Resposta: A. ou A: ou A)
            r'Alternativa\s*([A-Ea-e])[\.:\)]',  # Alternativa A. ou A: ou A)
            r'letra\s*([A-Ea-e])[\.:\)]',  # letra A. ou A: ou A)
            r'opção\s*([A-Ea-e])[\.:\)]',  # opção A. ou A: ou A)
            r'[^\w]([A-Ea-e])\)',  # Qualquer ocorrência de A) isolada
            r'correta[:\s]+([A-Ea-e])',  # correta: A ou correta A
            r'resposta[:\s]+([A-Ea-e])',  # resposta: A ou resposta A
        ]

        for pattern in patterns:
            matches = re.search(pattern, answer_text, re.IGNORECASE)
            if matches:
                letter = matches.group(1).upper()
                if DEBUG:
                    print(f"Letra da resposta encontrada: {letter}")
                return letter

        # Se não encontrou com os padrões específicos, procura por qualquer letra isolada
        # que pareça ser uma alternativa
        words = answer_text.split()
        for word in words[:20]:  # Verifica mais palavras
            if re.match(r'^[A-Ea-e][\.:\)]$', word):
                letter = word[0].upper()
                if DEBUG:
                    print(f"Letra da resposta encontrada (formato simples): {letter}")
                return letter

        # Se ainda não encontrou, tenta um método mais agressivo
        # Procura por qualquer letra maiúscula isolada que possa ser uma alternativa
        for letter in ['A', 'B', 'C', 'D', 'E']:
            if f" {letter} " in answer_text or f" {letter}." in answer_text or f" {letter})" in answer_text:
                if DEBUG:
                    print(f"Letra da resposta encontrada (método agressivo): {letter}")
                return letter

        # Se tudo falhar, assume a primeira letra que aparece após "Resposta correta"
        if "Resposta correta" in answer_text:
            pos = answer_text.find("Resposta correta")
            for i in range(pos + 16, min(pos + 30, len(answer_text))):
                if answer_text[i].upper() in ['A', 'B', 'C', 'D', 'E']:
                    letter = answer_text[i].upper()
                    if DEBUG:
                        print(f"Letra da resposta encontrada (último recurso): {letter}")
                    return letter

        if DEBUG:
            print("Nenhuma letra de resposta encontrada")
        return None

    def show_pointer(self, answer_text, window_handle=None):
        """
        Mostra o apontador visual para a resposta correta.

        Args:
            answer_text: Texto da resposta da LLM
            window_handle: Handle da janela onde mostrar o apontador

        Returns:
            True se o apontador foi mostrado com sucesso, False caso contrário
        """
        # Extrai a letra da resposta
        self.answer_letter = self.extract_answer_letter(answer_text)
        if not self.answer_letter:
            if DEBUG:
                print("Não foi possível extrair a letra da resposta")
            return False

        # Define o handle da janela
        if window_handle:
            self.window_handle = window_handle

        if not self.window_handle:
            if DEBUG:
                print("Handle da janela não definido")
            return False

        # Localiza a posição da alternativa na tela
        self.answer_position = self.locate_answer_option(self.answer_letter)
        if not self.answer_position:
            if DEBUG:
                print(f"Não foi possível localizar a alternativa {self.answer_letter} na tela")
            return False

        # Cria e mostra o overlay
        self.create_overlay()

        return True

    def locate_answer_option(self, letter):
        """
        Localiza a posição da alternativa na tela usando uma abordagem simplificada.

        Args:
            letter: Letra da alternativa (A, B, C, D, E)

        Returns:
            Tupla (x, y) com a posição estimada da alternativa
        """
        try:
            # Verifica se o handle da janela é válido
            if not self.window_handle:
                if DEBUG:
                    print("Handle da janela não definido")
                # Usa posições fixas baseadas em uma tela padrão
                return self._get_fixed_position(letter)

            # Captura a tela apenas para obter as dimensões
            from screen_capture_simple import ScreenCapture
            screen_capture = ScreenCapture()

            # Tenta capturar a janela, mas não insiste muito para não atrasar o processamento
            try:
                image = screen_capture.capture_window(self.window_handle)
            except Exception as e:
                if DEBUG:
                    print(f"Erro ao capturar janela: {str(e)}")
                # Usa posições fixas em caso de erro
                return self._get_fixed_position(letter)

            if image is None:
                if DEBUG:
                    print("Falha ao capturar a tela")
                # Usa posições fixas em caso de falha
                return self._get_fixed_position(letter)

            # Obtém as dimensões da tela
            height, width = image.shape[:2]

            # Mapeia a letra para um índice (A=0, B=1, C=2, D=3, E=4)
            letter_index = ord(letter.upper()) - ord('A')

            if letter_index < 0 or letter_index > 4:
                if DEBUG:
                    print(f"Letra inválida: {letter}")
                return self._get_fixed_position(letter)

            # Calcula a posição estimada com base na letra
            # Assume que as alternativas estão distribuídas verticalmente na metade inferior da tela
            estimated_x = width // 4
            estimated_y = height // 2 + (letter_index + 1) * (height // 10)

            if DEBUG:
                print(f"Posição estimada para alternativa {letter}: ({estimated_x}, {estimated_y})")

            return (estimated_x, estimated_y)

        except Exception as e:
            if DEBUG:
                print(f"Erro ao estimar posição da alternativa: {str(e)}")
            # Usa posições fixas em caso de erro
            return self._get_fixed_position(letter)

    def _get_fixed_position(self, letter):
        """
        Retorna posições fixas para as alternativas baseadas em uma tela padrão.

        Args:
            letter: Letra da alternativa (A, B, C, D, E)

        Returns:
            Tupla (x, y) com a posição fixa da alternativa
        """
        # Posições fixas para uma tela de 1920x1080
        positions = {
            'A': (300, 400),
            'B': (300, 500),
            'C': (300, 600),
            'D': (300, 700),
            'E': (300, 800)
        }

        letter = letter.upper()
        if letter in positions:
            if DEBUG:
                print(f"Usando posição fixa para alternativa {letter}: {positions[letter]}")
            return positions[letter]
        else:
            # Posição padrão no centro da tela
            if DEBUG:
                print(f"Usando posição padrão para letra desconhecida: {letter}")
            return (300, 500)

    def create_overlay(self):
        """
        Cria um overlay para destacar a resposta correta.
        """
        if not self.answer_position:
            return

        # Cria um widget de overlay
        self.overlay = AnswerOverlay(self.answer_position, self.answer_letter)
        self.overlay.show()

        # Define um timer para remover o overlay após alguns segundos
        self.is_active = True
        QTimer.singleShot(5000, self.hide_pointer)

    def hide_pointer(self):
        """
        Esconde o apontador visual.
        """
        if self.overlay:
            self.overlay.close()
            self.overlay = None

        self.is_active = False

    def set_window_handle(self, window_handle):
        """
        Define o handle da janela onde mostrar o apontador.

        Args:
            window_handle: Handle da janela
        """
        self.window_handle = window_handle

    def is_pointer_active(self):
        """
        Verifica se o apontador está ativo.

        Returns:
            True se o apontador estiver ativo, False caso contrário
        """
        return self.is_active

class AnswerOverlay(QWidget):
    """
    Widget de overlay para destacar a resposta correta.
    """
    def __init__(self, position, letter):
        """
        Inicializa o widget de overlay.

        Args:
            position: Tupla (x, y) com a posição da alternativa
            letter: Letra da alternativa (A, B, C, D, E)
        """
        super().__init__()

        self.position = position
        self.letter = letter
        self.size = 120  # Tamanho do círculo aumentado para maior visibilidade

        # Configura a janela
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.WindowStaysOnTopHint | Qt.Tool)
        self.setAttribute(Qt.WA_TranslucentBackground)
        self.setGeometry(
            position[0] - self.size//2,
            position[1] - self.size//2,
            self.size,
            self.size
        )

        # Cria um timer para animar o círculo
        self.animation_timer = QTimer(self)
        self.animation_timer.timeout.connect(self.update)
        self.animation_timer.start(50)
        self.animation_step = 0

    def paintEvent(self, event):
        """
        Desenha o círculo ao redor da alternativa.
        """
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)

        # Define a cor e o estilo do círculo
        color = QColor(255, 0, 0, 180)  # Vermelho mais visível
        pen = QPen(color, 5)  # Linha mais grossa
        painter.setPen(pen)

        # Calcula o tamanho do círculo com base na animação
        pulse = abs(np.sin(self.animation_step * 0.1)) * 15
        size = self.size - pulse

        # Desenha o círculo
        painter.drawEllipse(pulse//2, pulse//2, size, size)

        # Desenha um círculo de fundo para a letra
        background_color = QColor(0, 0, 0, 150)  # Fundo preto semi-transparente
        painter.setBrush(QBrush(background_color))
        painter.setPen(Qt.NoPen)
        painter.drawEllipse(self.size//2 - 20, self.size//2 - 20, 40, 40)

        # Desenha a letra no centro
        font = painter.font()
        font.setPointSize(24)  # Fonte maior
        font.setBold(True)  # Negrito
        painter.setFont(font)
        painter.setPen(QPen(QColor(255, 255, 255)))  # Texto branco
        painter.drawText(self.rect(), Qt.AlignCenter, self.letter)

        # Incrementa o passo da animação
        self.animation_step += 1
