"""
Módulo de processamento de áudio para captura e conversão de fala em texto.
"""
import os
import time
import threading
import tempfile
import numpy as np
from typing import Optional, Callable, List, Dict, Any
from pathlib import Path

from ..core.config import (
    ENABLE_AUDIO_MODULE, AUDIO_SAMPLE_RATE, AUDIO_CHANNELS, AUDIO_CHUNK_SIZE,
    AUDIO_DEVICE_INDEX, AUDIO_CAPTURE_DURATION, AUDIO_MIN_DURATION,
    AUDIO_SILENCE_THRESHOLD, AUDIO_SILENCE_DURATION, STT_PROVIDER,
    ENABLE_NOISE_REDUCTION, ENABLE_VOICE_ENHANCEMENT, AUDIO_GAIN,
    VAD_ENABLED, AUDIO_SAVE_RECORDINGS, AUDIO_DELETE_AFTER_PROCESS,
    DEBUG
)
from .speech_to_text import SpeechToTextConverter


class AudioProcessor:
    """
    Processador de áudio para captura e conversão de fala em texto.
    """
    
    def __init__(self, callback_on_text: Optional[Callable[[str], None]] = None):
        """
        Inicializa o processador de áudio.
        
        Args:
            callback_on_text: Função chamada quando texto é extraído do áudio
        """
        self.callback_on_text = callback_on_text
        self.is_recording = False
        self.is_processing = False
        self.recording_thread = None
        self.audio_data = []
        self.temp_files = []
        
        # Verificar se o módulo está habilitado
        if not ENABLE_AUDIO_MODULE:
            if DEBUG:
                print("Módulo de áudio desabilitado na configuração")
            return
        
        # Inicializar componentes de áudio
        self._initialize_audio_components()

        # Inicializar conversor de fala para texto
        self.stt_converter = SpeechToTextConverter()

        if DEBUG:
            print("AudioProcessor inicializado com sucesso")
    
    def _initialize_audio_components(self):
        """Inicializa os componentes de áudio necessários."""
        try:
            # Importações opcionais
            global sounddevice, webrtcvad, noisereduce, librosa
            
            try:
                import sounddevice as sd
                sounddevice = sd
                if DEBUG:
                    print("sounddevice carregado com sucesso")
            except ImportError:
                if DEBUG:
                    print("sounddevice não encontrado. Instale com: pip install sounddevice")
                sounddevice = None
            
            try:
                import webrtcvad
                if DEBUG:
                    print("webrtcvad carregado com sucesso")
            except ImportError:
                if DEBUG:
                    print("webrtcvad não encontrado. Instale com: pip install webrtcvad")
                webrtcvad = None
            
            try:
                import noisereduce as nr
                noisereduce = nr
                if DEBUG:
                    print("noisereduce carregado com sucesso")
            except ImportError:
                if DEBUG:
                    print("noisereduce não encontrado. Instale com: pip install noisereduce")
                noisereduce = None
            
            try:
                import librosa
                if DEBUG:
                    print("librosa carregado com sucesso")
            except ImportError:
                if DEBUG:
                    print("librosa não encontrado. Instale com: pip install librosa")
                librosa = None
            
            # Verificar dispositivos de áudio disponíveis
            if sounddevice:
                self._list_audio_devices()
                
        except Exception as e:
            if DEBUG:
                print(f"Erro ao inicializar componentes de áudio: {e}")
    
    def _list_audio_devices(self):
        """Lista dispositivos de áudio disponíveis."""
        try:
            if sounddevice:
                devices = sounddevice.query_devices()
                if DEBUG:
                    print("Dispositivos de áudio disponíveis:")
                    for i, device in enumerate(devices):
                        print(f"  {i}: {device['name']} - {device['max_input_channels']} canais de entrada")
        except Exception as e:
            if DEBUG:
                print(f"Erro ao listar dispositivos de áudio: {e}")
    
    def is_available(self) -> bool:
        """
        Verifica se o módulo de áudio está disponível e configurado.
        
        Returns:
            True se o módulo está disponível
        """
        return (ENABLE_AUDIO_MODULE and 
                sounddevice is not None and 
                hasattr(self, '_initialize_audio_components'))
    
    def start_recording(self) -> bool:
        """
        Inicia a gravação de áudio.
        
        Returns:
            True se a gravação foi iniciada com sucesso
        """
        if not self.is_available():
            if DEBUG:
                print("Módulo de áudio não disponível")
            return False
        
        if self.is_recording:
            if DEBUG:
                print("Gravação já está em andamento")
            return False
        
        try:
            self.is_recording = True
            self.audio_data = []
            
            # Inicia thread de gravação
            self.recording_thread = threading.Thread(target=self._recording_loop)
            self.recording_thread.daemon = True
            self.recording_thread.start()
            
            if DEBUG:
                print("Gravação de áudio iniciada")
            return True
            
        except Exception as e:
            if DEBUG:
                print(f"Erro ao iniciar gravação: {e}")
            self.is_recording = False
            return False
    
    def stop_recording(self) -> bool:
        """
        Para a gravação de áudio.
        
        Returns:
            True se a gravação foi parada com sucesso
        """
        if not self.is_recording:
            return True
        
        try:
            self.is_recording = False
            
            # Aguarda thread de gravação terminar
            if self.recording_thread and self.recording_thread.is_alive():
                self.recording_thread.join(timeout=5.0)
            
            if DEBUG:
                print("Gravação de áudio parada")
            return True
            
        except Exception as e:
            if DEBUG:
                print(f"Erro ao parar gravação: {e}")
            return False
    
    def _recording_loop(self):
        """Loop principal de gravação de áudio."""
        try:
            if not sounddevice:
                return
            
            # Configurações de gravação
            duration = AUDIO_CAPTURE_DURATION
            sample_rate = AUDIO_SAMPLE_RATE
            channels = AUDIO_CHANNELS
            device = AUDIO_DEVICE_INDEX
            
            if DEBUG:
                print(f"Iniciando gravação: {duration}s, {sample_rate}Hz, {channels} canais")
            
            # Grava áudio
            audio_data = sounddevice.rec(
                int(duration * sample_rate),
                samplerate=sample_rate,
                channels=channels,
                device=device,
                dtype='float32'
            )
            
            # Aguarda gravação ou interrupção
            start_time = time.time()
            silence_start = None
            
            while self.is_recording and (time.time() - start_time) < duration:
                sounddevice.wait(100)  # Aguarda 100ms
                
                # Verifica silêncio se VAD estiver habilitado
                if VAD_ENABLED and self._detect_silence(audio_data):
                    if silence_start is None:
                        silence_start = time.time()
                    elif (time.time() - silence_start) > AUDIO_SILENCE_DURATION:
                        if DEBUG:
                            print("Silêncio detectado, parando gravação")
                        break
                else:
                    silence_start = None
            
            # Para gravação
            sounddevice.stop()
            
            # Verifica duração mínima
            actual_duration = time.time() - start_time
            if actual_duration < AUDIO_MIN_DURATION:
                if DEBUG:
                    print(f"Áudio muito curto ({actual_duration:.1f}s), ignorando")
                return
            
            # Processa áudio gravado
            if len(audio_data) > 0:
                self._process_audio(audio_data, sample_rate)
                
        except Exception as e:
            if DEBUG:
                print(f"Erro no loop de gravação: {e}")
        finally:
            self.is_recording = False
    
    def _detect_silence(self, audio_data: np.ndarray) -> bool:
        """
        Detecta se o áudio atual contém silêncio.
        
        Args:
            audio_data: Dados de áudio
            
        Returns:
            True se silêncio for detectado
        """
        try:
            if len(audio_data) == 0:
                return True
            
            # Calcula RMS (Root Mean Square) do áudio
            rms = np.sqrt(np.mean(audio_data ** 2))
            
            # Compara com threshold de silêncio
            is_silent = rms < AUDIO_SILENCE_THRESHOLD
            
            if DEBUG and is_silent:
                print(f"Silêncio detectado (RMS: {rms:.4f})")
            
            return is_silent
            
        except Exception as e:
            if DEBUG:
                print(f"Erro na detecção de silêncio: {e}")
            return False
    
    def _process_audio(self, audio_data: np.ndarray, sample_rate: int):
        """
        Processa o áudio gravado e extrai texto.
        
        Args:
            audio_data: Dados de áudio
            sample_rate: Taxa de amostragem
        """
        try:
            if self.is_processing:
                if DEBUG:
                    print("Processamento já em andamento, ignorando")
                return
            
            self.is_processing = True
            
            if DEBUG:
                print(f"Processando áudio: {len(audio_data)} amostras, {sample_rate}Hz")
            
            # Aplica processamento de áudio
            processed_audio = self._enhance_audio(audio_data, sample_rate)
            
            # Salva áudio temporário
            temp_file = self._save_temp_audio(processed_audio, sample_rate)
            
            if temp_file:
                # Converte áudio para texto
                text = self._audio_to_text(temp_file)
                
                if text and len(text.strip()) > 0:
                    if DEBUG:
                        print(f"Texto extraído: {text}")
                    
                    # Chama callback se definido
                    if self.callback_on_text:
                        self.callback_on_text(text.strip())
                else:
                    if DEBUG:
                        print("Nenhum texto extraído do áudio")
                
                # Limpa arquivo temporário
                if not AUDIO_SAVE_RECORDINGS and AUDIO_DELETE_AFTER_PROCESS:
                    self._cleanup_temp_file(temp_file)
            
        except Exception as e:
            if DEBUG:
                print(f"Erro no processamento de áudio: {e}")
        finally:
            self.is_processing = False

    def _enhance_audio(self, audio_data: np.ndarray, sample_rate: int) -> np.ndarray:
        """
        Aplica melhorias no áudio (redução de ruído, normalização, etc.).

        Args:
            audio_data: Dados de áudio
            sample_rate: Taxa de amostragem

        Returns:
            Áudio processado
        """
        try:
            processed = audio_data.copy()

            # Aplica ganho se configurado
            if AUDIO_GAIN != 1.0:
                processed = processed * AUDIO_GAIN
                if DEBUG:
                    print(f"Ganho aplicado: {AUDIO_GAIN}")

            # Redução de ruído
            if ENABLE_NOISE_REDUCTION and noisereduce:
                processed = noisereduce.reduce_noise(y=processed, sr=sample_rate)
                if DEBUG:
                    print("Redução de ruído aplicada")

            # Normalização
            if hasattr(self, '_normalize_audio'):
                processed = self._normalize_audio(processed)

            # Melhoria de voz
            if ENABLE_VOICE_ENHANCEMENT and librosa:
                processed = self._enhance_voice(processed, sample_rate)

            return processed

        except Exception as e:
            if DEBUG:
                print(f"Erro no processamento de áudio: {e}")
            return audio_data

    def _normalize_audio(self, audio_data: np.ndarray) -> np.ndarray:
        """
        Normaliza o áudio para melhorar a qualidade.

        Args:
            audio_data: Dados de áudio

        Returns:
            Áudio normalizado
        """
        try:
            # Normalização por pico
            max_val = np.max(np.abs(audio_data))
            if max_val > 0:
                normalized = audio_data / max_val
                if DEBUG:
                    print(f"Áudio normalizado (pico: {max_val:.4f})")
                return normalized
            return audio_data

        except Exception as e:
            if DEBUG:
                print(f"Erro na normalização: {e}")
            return audio_data

    def _enhance_voice(self, audio_data: np.ndarray, sample_rate: int) -> np.ndarray:
        """
        Aplica melhorias específicas para voz humana.

        Args:
            audio_data: Dados de áudio
            sample_rate: Taxa de amostragem

        Returns:
            Áudio com voz melhorada
        """
        try:
            if not librosa:
                return audio_data

            # Filtro passa-banda para frequências de voz (80Hz - 8kHz)
            enhanced = librosa.effects.preemphasis(audio_data)

            if DEBUG:
                print("Melhoria de voz aplicada")

            return enhanced

        except Exception as e:
            if DEBUG:
                print(f"Erro na melhoria de voz: {e}")
            return audio_data

    def _save_temp_audio(self, audio_data: np.ndarray, sample_rate: int) -> Optional[str]:
        """
        Salva áudio em arquivo temporário.

        Args:
            audio_data: Dados de áudio
            sample_rate: Taxa de amostragem

        Returns:
            Caminho do arquivo temporário ou None se erro
        """
        try:
            # Cria arquivo temporário
            temp_file = tempfile.NamedTemporaryFile(
                suffix='.wav',
                delete=False,
                dir=tempfile.gettempdir()
            )
            temp_path = temp_file.name
            temp_file.close()

            # Salva áudio usando soundfile se disponível
            try:
                import soundfile as sf
                sf.write(temp_path, audio_data, sample_rate)
                if DEBUG:
                    print(f"Áudio salvo em: {temp_path}")
            except ImportError:
                # Fallback para scipy
                try:
                    from scipy.io import wavfile
                    # Converte para int16
                    audio_int16 = (audio_data * 32767).astype(np.int16)
                    wavfile.write(temp_path, sample_rate, audio_int16)
                    if DEBUG:
                        print(f"Áudio salvo em: {temp_path} (scipy)")
                except ImportError:
                    if DEBUG:
                        print("Nenhuma biblioteca de áudio disponível para salvar")
                    return None

            # Adiciona à lista de arquivos temporários
            self.temp_files.append(temp_path)

            return temp_path

        except Exception as e:
            if DEBUG:
                print(f"Erro ao salvar áudio temporário: {e}")
            return None

    def _cleanup_temp_file(self, file_path: str):
        """
        Remove arquivo temporário.

        Args:
            file_path: Caminho do arquivo a ser removido
        """
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                if file_path in self.temp_files:
                    self.temp_files.remove(file_path)
                if DEBUG:
                    print(f"Arquivo temporário removido: {file_path}")
        except Exception as e:
            if DEBUG:
                print(f"Erro ao remover arquivo temporário: {e}")

    def _audio_to_text(self, audio_file_path: str) -> Optional[str]:
        """
        Converte arquivo de áudio para texto.

        Args:
            audio_file_path: Caminho do arquivo de áudio

        Returns:
            Texto extraído ou None se erro
        """
        try:
            if not hasattr(self, 'stt_converter') or not self.stt_converter:
                if DEBUG:
                    print("Conversor STT não disponível")
                return None

            # Converte áudio para texto
            text = self.stt_converter.convert_audio_to_text(audio_file_path)

            if DEBUG:
                print(f"Texto extraído do áudio: {text}")

            return text

        except Exception as e:
            if DEBUG:
                print(f"Erro na conversão de áudio para texto: {e}")
            return None

    def cleanup_all_temp_files(self):
        """Remove todos os arquivos temporários."""
        for file_path in self.temp_files.copy():
            self._cleanup_temp_file(file_path)

    def get_status(self) -> Dict[str, Any]:
        """
        Retorna status atual do processador de áudio.

        Returns:
            Dicionário com informações de status
        """
        return {
            'available': self.is_available(),
            'recording': self.is_recording,
            'processing': self.is_processing,
            'temp_files': len(self.temp_files),
            'module_enabled': ENABLE_AUDIO_MODULE
        }
