# =============================================================================
# CONFIGURAÇÃO DO MÓDULO DE ÁUDIO - Python Question Helper
# =============================================================================
# 
# Copie este arquivo para .env e configure conforme necessário
# 
# IMPORTANTE: O módulo de áudio é OPCIONAL e requer dependências adicionais
# Instale com: pip install -r requirements-audio.txt

# =============================================================================
# ATIVAÇÃO DO MÓDULO
# =============================================================================

# Habilitar módulo de áudio (true/false)
ENABLE_AUDIO_MODULE=false

# =============================================================================
# CONFIGURAÇÕES DE CAPTURA DE ÁUDIO
# =============================================================================

# Taxa de amostragem em Hz (22050, 44100, 48000)
# Maior = melhor qualidade, mais recursos
AUDIO_SAMPLE_RATE=44100

# Número de canais (1=mono, 2=estéreo)
# Mono é suficiente para voz, estéreo para música
AUDIO_CHANNELS=1

# Tamanho do buffer de áudio
AUDIO_CHUNK_SIZE=1024

# Formato de áudio (int16, int32, float32)
AUDIO_FORMAT=int16

# Índice do dispositivo de áudio (deixe vazio para padrão)
# Use python -c "import sounddevice; print(sounddevice.query_devices())" para listar
AUDIO_DEVICE_INDEX=

# Duração máxima de captura em segundos (5-120)
AUDIO_CAPTURE_DURATION=30

# Duração mínima para processar em segundos (1-10)
AUDIO_MIN_DURATION=2

# Threshold para detectar silêncio (0.001-0.1)
# Menor = mais sensível ao silêncio
AUDIO_SILENCE_THRESHOLD=0.01

# Duração de silêncio para parar captura em segundos (1-10)
AUDIO_SILENCE_DURATION=3

# =============================================================================
# CONFIGURAÇÕES DE SPEECH-TO-TEXT
# =============================================================================

# Provedor de STT (whisper, google, azure, aws)
# whisper = recomendado (offline, gratuito)
STT_PROVIDER=whisper

# Idioma para reconhecimento
# pt-BR, en-US, es-ES, fr-FR, de-DE, it-IT
STT_LANGUAGE=pt-BR

# Tamanho do modelo Whisper (tiny, base, small, medium, large)
# tiny = mais rápido, menos preciso
# large = mais lento, mais preciso
STT_MODEL_SIZE=base

# =============================================================================
# APIS DE SPEECH-TO-TEXT (OPCIONAL)
# =============================================================================

# Google Speech-to-Text
# Requer conta Google Cloud e arquivo de credenciais
GOOGLE_SPEECH_API_KEY=

# Azure Speech Services
# Requer conta Microsoft Azure
AZURE_SPEECH_KEY=
AZURE_SPEECH_REGION=

# AWS Transcribe
# Requer conta Amazon Web Services
AWS_ACCESS_KEY=
AWS_SECRET_KEY=

# =============================================================================
# PROCESSAMENTO DE ÁUDIO
# =============================================================================

# Habilitar redução de ruído (true/false)
ENABLE_NOISE_REDUCTION=true

# Habilitar melhoria de voz (true/false)
ENABLE_VOICE_ENHANCEMENT=true

# Ganho de áudio (0.1-3.0)
# 1.0 = normal, >1.0 = amplificar, <1.0 = reduzir
AUDIO_GAIN=1.0

# Normalizar volume do áudio (true/false)
AUDIO_NORMALIZE=true

# =============================================================================
# DETECÇÃO DE ATIVIDADE DE VOZ (VAD)
# =============================================================================

# Habilitar VAD (true/false)
VAD_ENABLED=true

# Agressividade do VAD (0-3)
# 0 = menos agressivo (detecta mais fala)
# 3 = mais agressivo (detecta menos fala)
VAD_AGGRESSIVENESS=2

# Duração do frame em ms (10, 20, 30)
VAD_FRAME_DURATION=30

# =============================================================================
# CONFIGURAÇÕES DE PRIVACIDADE
# =============================================================================

# Salvar gravações para debug (true/false)
# ATENÇÃO: Pode ocupar espaço em disco
AUDIO_SAVE_RECORDINGS=false

# Deletar áudio após processamento (true/false)
# Recomendado: true para privacidade
AUDIO_DELETE_AFTER_PROCESS=true

# Criptografar armazenamento temporário (true/false)
AUDIO_ENCRYPT_STORAGE=true

# =============================================================================
# CONFIGURAÇÕES RECOMENDADAS POR CENÁRIO
# =============================================================================

# CENÁRIO 1: MÁXIMA PRIVACIDADE (Whisper local)
# ENABLE_AUDIO_MODULE=true
# STT_PROVIDER=whisper
# STT_MODEL_SIZE=base
# AUDIO_SAVE_RECORDINGS=false
# AUDIO_DELETE_AFTER_PROCESS=true

# CENÁRIO 2: MÁXIMA PRECISÃO (Google Cloud)
# ENABLE_AUDIO_MODULE=true
# STT_PROVIDER=google
# GOOGLE_SPEECH_API_KEY=path/to/credentials.json
# ENABLE_NOISE_REDUCTION=true
# ENABLE_VOICE_ENHANCEMENT=true

# CENÁRIO 3: PERFORMANCE RÁPIDA (Whisper tiny)
# ENABLE_AUDIO_MODULE=true
# STT_PROVIDER=whisper
# STT_MODEL_SIZE=tiny
# AUDIO_CAPTURE_DURATION=15
# ENABLE_NOISE_REDUCTION=false

# CENÁRIO 4: ALTA QUALIDADE (Whisper large)
# ENABLE_AUDIO_MODULE=true
# STT_PROVIDER=whisper
# STT_MODEL_SIZE=large
# AUDIO_SAMPLE_RATE=48000
# ENABLE_VOICE_ENHANCEMENT=true

# =============================================================================
# TROUBLESHOOTING
# =============================================================================

# PROBLEMA: Módulo não carrega
# SOLUÇÃO: pip install -r requirements-audio.txt

# PROBLEMA: Sem dispositivos de áudio
# SOLUÇÃO: Verificar AUDIO_DEVICE_INDEX ou deixar vazio

# PROBLEMA: Whisper muito lento
# SOLUÇÃO: Usar STT_MODEL_SIZE=tiny ou base

# PROBLEMA: Baixa precisão
# SOLUÇÃO: Usar STT_MODEL_SIZE=large ou provedor cloud

# PROBLEMA: Muito ruído
# SOLUÇÃO: ENABLE_NOISE_REDUCTION=true, ajustar AUDIO_GAIN

# PROBLEMA: Não detecta fala
# SOLUÇÃO: Ajustar AUDIO_SILENCE_THRESHOLD e VAD_AGGRESSIVENESS

# =============================================================================
# INSTALAÇÃO DE DEPENDÊNCIAS
# =============================================================================

# BÁSICO (Whisper + captura):
# pip install sounddevice librosa openai-whisper

# COMPLETO (todos os provedores):
# pip install -r requirements-audio.txt

# LINUX (PortAudio):
# sudo apt-get install portaudio19-dev

# WINDOWS (geralmente funciona direto):
# pip install sounddevice

# MACOS (PortAudio):
# brew install portaudio

# =============================================================================
# TESTE DE FUNCIONAMENTO
# =============================================================================

# Para testar se o módulo está funcionando:
# 1. Configure ENABLE_AUDIO_MODULE=true
# 2. Execute o Python Question Helper
# 3. Verifique se aparece a seção "🎤 Processamento de Áudio"
# 4. Clique em "Iniciar Captura de Áudio"
# 5. Fale algo e verifique se o texto aparece

# =============================================================================
# SUPORTE E DOCUMENTAÇÃO
# =============================================================================

# Documentação completa: docs/MODULO_AUDIO.md
# Dependências: requirements-audio.txt
# Exemplos: src/audio/
# Issues: https://github.com/hiagodrigo/Python_Question_Helper/issues
