#!/usr/bin/env python3
"""
Teste do sistema de fallback de provedores corrigido:
1. Detecção de modelos inexistentes (404)
2. Fallback automático para outros modelos/provedores
3. Tratamento melhorado de erros
"""

import os
import sys
import cv2
import numpy as np
from datetime import datetime

# Adiciona o diretório atual ao path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from llm_manager import LLMManager
from huggingface_client import HuggingFaceClient
from config import DEBUG

def create_test_image():
    """Cria uma imagem de teste simples."""
    img = np.ones((400, 600, 3), dtype=np.uint8) * 255
    
    # Adiciona texto de teste
    cv2.putText(img, "TESTE DE QUESTAO", (50, 50), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
    cv2.putText(img, "Qual e a resposta correta?", (50, 120), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 2)
    cv2.putText(img, "A) Primeira opcao", (50, 180), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 1)
    cv2.putText(img, "B) Segunda opcao", (50, 220), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 1)
    cv2.putText(img, "C) Terceira opcao", (50, 260), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 1)
    cv2.putText(img, "D) Quarta opcao", (50, 300), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 1)
    
    return img

def test_huggingface_model_fallback():
    """Testa o fallback de modelos no Hugging Face."""
    print("=== TESTE 1: FALLBACK DE MODELOS HUGGING FACE ===\n")
    
    try:
        # Cria cliente com modelo inexistente
        client = HuggingFaceClient(model_id="modelo/inexistente-404")
        
        print(f"✅ Cliente criado com modelo: {client.model}")
        print(f"✅ Disponível: {client.is_available}")
        
        # Lista modelos disponíveis
        models = client.get_available_models()
        print(f"✅ Modelos disponíveis: {len(models)}")
        for model in models:
            print(f"   - {model['id']} ({model['name']})")
        
        # Testa mudança para modelo válido
        success = client.set_model("llava-hf/llava-1.5-7b-hf")
        print(f"✅ Mudança para modelo válido: {success}")
        print(f"✅ Modelo atual: {client.model}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro: {str(e)}")
        return False

def test_llm_manager_fallback():
    """Testa o fallback completo do LLM Manager."""
    print("\n=== TESTE 2: FALLBACK COMPLETO DO LLM MANAGER ===\n")
    
    try:
        # Cria imagem de teste
        test_image = create_test_image()
        image_path = "test_fallback.png"
        cv2.imwrite(image_path, test_image)
        
        print("1. Inicializando LLM Manager...")
        llm_manager = LLMManager()
        
        print(f"✅ Provedor inicial: {llm_manager.get_provider_name()}")
        print(f"✅ Cliente disponível: {llm_manager.client is not None}")
        
        # Lista provedores disponíveis
        providers = llm_manager.get_available_providers()
        print(f"✅ Provedores disponíveis: {providers}")
        
        print("\n2. Testando processamento com fallback...")
        
        start_time = datetime.now()
        
        # Tenta processar a imagem (pode ativar fallback se houver erro)
        response = llm_manager.process_image_with_validation(image_path, extracted_text="")
        
        end_time = datetime.now()
        processing_time = (end_time - start_time).total_seconds()
        
        print(f"\n✅ Provedor final: {llm_manager.get_provider_name()}")
        print(f"✅ Tempo de processamento: {processing_time:.2f}s")
        
        print("\n=== RESPOSTA ===")
        print(response)
        print("================")
        
        # Verifica se a resposta não é um erro
        is_error = any(word in response.lower() for word in ["erro", "error", "404", "not found"])
        has_answer = "resposta correta:" in response.lower()
        
        print(f"\n✅ Não é erro: {not is_error}")
        print(f"✅ Tem resposta: {has_answer}")
        
        # Cleanup
        if os.path.exists(image_path):
            os.remove(image_path)
        
        return not is_error and has_answer
        
    except Exception as e:
        print(f"❌ Erro: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_error_detection():
    """Testa a detecção de diferentes tipos de erro."""
    print("\n=== TESTE 3: DETECÇÃO DE TIPOS DE ERRO ===\n")
    
    error_scenarios = [
        ("404 - Not Found", "modelo não encontrado"),
        ("503 - Service Unavailable", "serviço indisponível"),
        ("429 - Too Many Requests", "quota excedida"),
        ("Erro na API da Hugging Face: 404", "modelo não encontrado"),
        ("quota exceeded", "quota excedida"),
        ("rate limit", "quota excedida"),
        ("service unavailable", "serviço indisponível"),
    ]
    
    all_passed = True
    
    for error_text, expected_type in error_scenarios:
        # Simula a lógica de detecção de erro do LLM Manager
        error_str = error_text.lower()
        is_quota_error = "quota" in error_str or "rate limit" in error_str or "429" in error_str
        is_service_unavailable = "503" in error_str or "service unavailable" in error_str or "unavailable" in error_str
        is_model_not_found = "404" in error_str or "not found" in error_str or "modelo não encontrado" in error_str
        
        if is_quota_error:
            detected_type = "quota excedida"
        elif is_service_unavailable:
            detected_type = "serviço indisponível"
        elif is_model_not_found:
            detected_type = "modelo não encontrado"
        else:
            detected_type = "erro desconhecido"
        
        passed = detected_type == expected_type
        print(f"✅ '{error_text}' -> '{detected_type}' (esperado: '{expected_type}') - {'PASSOU' if passed else 'FALHOU'}")
        
        if not passed:
            all_passed = False
    
    return all_passed

def test_provider_switching():
    """Testa a troca de provedores."""
    print("\n=== TESTE 4: TROCA DE PROVEDORES ===\n")
    
    try:
        llm_manager = LLMManager()
        
        initial_provider = llm_manager.get_provider_name()
        print(f"✅ Provedor inicial: {initial_provider}")
        
        # Lista todos os provedores disponíveis
        available_providers = llm_manager.get_available_providers()
        print(f"✅ Provedores disponíveis: {available_providers}")
        
        # Tenta trocar para cada provedor
        successful_switches = 0
        for provider in available_providers:
            if provider != llm_manager.provider:
                success = llm_manager.set_provider(provider)
                if success:
                    successful_switches += 1
                    print(f"✅ Troca para {provider}: SUCESSO")
                else:
                    print(f"❌ Troca para {provider}: FALHOU")
        
        print(f"\n✅ Trocas bem-sucedidas: {successful_switches}/{len(available_providers)-1}")
        
        return successful_switches > 0
        
    except Exception as e:
        print(f"❌ Erro: {str(e)}")
        return False

if __name__ == "__main__":
    print("🔧 TESTANDO SISTEMA DE FALLBACK CORRIGIDO")
    print("="*60)
    
    # Executa os testes
    test1_passed = test_huggingface_model_fallback()
    test2_passed = test_llm_manager_fallback()
    test3_passed = test_error_detection()
    test4_passed = test_provider_switching()
    
    print("\n" + "="*60)
    print("📋 RELATÓRIO FINAL:")
    print(f"✅ Fallback modelos HF: {'PASSOU' if test1_passed else 'FALHOU'}")
    print(f"✅ Fallback LLM Manager: {'PASSOU' if test2_passed else 'FALHOU'}")
    print(f"✅ Detecção de erros: {'PASSOU' if test3_passed else 'FALHOU'}")
    print(f"✅ Troca de provedores: {'PASSOU' if test4_passed else 'FALHOU'}")
    
    total_passed = sum([test1_passed, test2_passed, test3_passed, test4_passed])
    
    if total_passed == 4:
        print("\n🎉 SISTEMA DE FALLBACK TOTALMENTE FUNCIONAL!")
        print("🔥 Correções implementadas:")
        print("   • Detecção de modelos inexistentes (404)")
        print("   • Fallback automático para modelos válidos")
        print("   • Troca inteligente de provedores")
        print("   • Tratamento robusto de diferentes tipos de erro")
        print("\n✨ O problema de troca automática foi resolvido!")
    else:
        print(f"\n⚠️ {4-total_passed} teste(s) falharam.")
        print("Verifique os logs acima para detalhes.")
    
    print("="*60)
