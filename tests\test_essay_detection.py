"""
Testes para o sistema de detecção de questões dissertativas.
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.processing.essay_detector import EssayQuestionDetector
from src.processing.essay_processor import EssayQuestionProcessor


def test_essay_detection():
    """Testa a detecção de questões dissertativas."""
    detector = EssayQuestionDetector()
    
    # Casos de teste positivos (devem ser detectados como dissertativos)
    positive_cases = [
        "Explique o conceito de inteligência artificial e suas aplicações na medicina moderna.",
        "Descreva o processo de fotossíntese e sua importância para os ecossistemas.",
        "Analise as principais causas da Revolução Industrial e suas consequências sociais.",
        "Justifique a importância da educação ambiental nas escolas públicas.",
        "Redija um texto sobre os impactos da tecnologia na sociedade contemporânea.",
        "Elabore uma dissertação sobre os desafios da sustentabilidade urbana.",
        "Discuta as vantagens e desvantagens do ensino à distância.",
        "Argumente sobre a relevância da preservação do patrimônio histórico.",
        "Compare os sistemas educacionais brasileiro e finlandês.",
        "Contraste as teorias de aprendizagem behaviorista e construtivista.",
        "Write an essay about the impact of social media on modern communication.",
        "Explain the importance of renewable energy sources for environmental sustainability.",
        "You attended a robotics conference. Write an article explaining the latest advancements in the field.",
        "Type your answer here\nTarget: 100 words",
        "Escreva sua resposta aqui\nMínimo: 50 palavras",
        "Em sua opinião, qual a importância da arte na formação humana?",
        "O que você pensa sobre o papel da tecnologia na educação?",
        "Segundo sua análise, quais são os principais desafios ambientais atuais?"
    ]
    
    # Casos de teste negativos (NÃO devem ser detectados como dissertativos)
    negative_cases = [
        "A) Verdadeiro\nB) Falso\nC) Parcialmente verdadeiro\nD) Não se aplica",
        "Qual das alternativas abaixo representa corretamente o conceito de fotossíntese?\nA) Processo de respiração das plantas\nB) Conversão de luz solar em energia química\nC) Absorção de água pelas raízes\nD) Liberação de oxigênio durante a noite\nE) Crescimento das folhas",
        "Marque a opção correta:\n(A) 2 + 2 = 4\n(B) 2 + 2 = 5\n(C) 2 + 2 = 6\n(D) 2 + 2 = 7",
        "Verdadeiro ou Falso: A Terra é redonda.",
        "Selecione a resposta correta:\n1) Sim\n2) Não\n3) Talvez",
        "Choose the correct answer:\nA- Paris\nB- London\nC- Berlin\nD- Madrid"
    ]
    
    print("=== TESTE DE DETECÇÃO DE QUESTÕES DISSERTATIVAS ===\n")
    
    print("📝 CASOS POSITIVOS (devem ser detectados como dissertativos):")
    print("-" * 60)
    
    positive_results = []
    for i, case in enumerate(positive_cases, 1):
        result = detector.detect_essay_question(case)
        positive_results.append(result)
        
        status = "✅ DETECTADO" if result['is_essay'] else "❌ NÃO DETECTADO"
        confidence = result['confidence']
        
        print(f"{i:2d}. {status} (Confiança: {confidence:.0%})")
        print(f"    Texto: {case[:80]}{'...' if len(case) > 80 else ''}")
        if result['indicators']:
            print(f"    Indicadores: {', '.join(result['indicators'][:2])}{'...' if len(result['indicators']) > 2 else ''}")
        print()
    
    print("\n" + "="*60)
    print("❌ CASOS NEGATIVOS (NÃO devem ser detectados como dissertativos):")
    print("-" * 60)
    
    negative_results = []
    for i, case in enumerate(negative_cases, 1):
        result = detector.detect_essay_question(case)
        negative_results.append(result)
        
        status = "❌ INCORRETO" if result['is_essay'] else "✅ CORRETO"
        confidence = result['confidence']
        
        print(f"{i:2d}. {status} (Confiança: {confidence:.0%})")
        print(f"    Texto: {case[:80]}{'...' if len(case) > 80 else ''}")
        if result['indicators']:
            print(f"    Indicadores: {', '.join(result['indicators'][:2])}{'...' if len(result['indicators']) > 2 else ''}")
        print()
    
    # Estatísticas
    print("\n" + "="*60)
    print("📊 ESTATÍSTICAS DOS TESTES:")
    print("-" * 60)
    
    positive_detected = sum(1 for r in positive_results if r['is_essay'])
    negative_detected = sum(1 for r in negative_results if r['is_essay'])
    
    positive_accuracy = (positive_detected / len(positive_cases)) * 100
    negative_accuracy = ((len(negative_cases) - negative_detected) / len(negative_cases)) * 100
    overall_accuracy = ((positive_detected + (len(negative_cases) - negative_detected)) / 
                       (len(positive_cases) + len(negative_cases))) * 100
    
    print(f"Casos Positivos: {positive_detected}/{len(positive_cases)} detectados ({positive_accuracy:.1f}%)")
    print(f"Casos Negativos: {len(negative_cases) - negative_detected}/{len(negative_cases)} corretos ({negative_accuracy:.1f}%)")
    print(f"Precisão Geral: {overall_accuracy:.1f}%")
    
    # Análise de confiança
    positive_confidences = [r['confidence'] for r in positive_results if r['is_essay']]
    negative_confidences = [r['confidence'] for r in negative_results if r['is_essay']]
    
    if positive_confidences:
        avg_positive_confidence = sum(positive_confidences) / len(positive_confidences)
        print(f"Confiança Média (Positivos): {avg_positive_confidence:.0%}")
    
    if negative_confidences:
        avg_negative_confidence = sum(negative_confidences) / len(negative_confidences)
        print(f"Confiança Média (Falsos Positivos): {avg_negative_confidence:.0%}")


def test_essay_processing():
    """Testa o processamento de questões dissertativas."""
    processor = EssayQuestionProcessor()
    
    print("\n" + "="*60)
    print("🧠 TESTE DE PROCESSAMENTO DE QUESTÕES DISSERTATIVAS")
    print("="*60)
    
    # Casos de teste para processamento
    test_cases = [
        {
            'question': "Explique o conceito de sustentabilidade e sua importância para o futuro da humanidade.",
            'detection_result': {
                'is_essay': True,
                'confidence': 0.85,
                'estimated_length': 'medium',
                'writing_style': 'academic',
                'word_count_hint': {'type': 'target', 'target': 200}
            }
        },
        {
            'question': "Descreva o procedimento para configurar um servidor web Apache em ambiente Linux.",
            'detection_result': {
                'is_essay': True,
                'confidence': 0.78,
                'estimated_length': 'long',
                'writing_style': 'technical',
                'word_count_hint': None
            }
        },
        {
            'question': "Em sua opinião, qual o papel da arte na educação infantil?",
            'detection_result': {
                'is_essay': True,
                'confidence': 0.72,
                'estimated_length': 'short',
                'writing_style': 'formal',
                'word_count_hint': {'type': 'range', 'min': 50, 'max': 100}
            }
        }
    ]
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n📝 CASO {i}:")
        print(f"Questão: {case['question']}")
        print(f"Estilo: {case['detection_result']['writing_style']}")
        print(f"Tamanho: {case['detection_result']['estimated_length']}")
        
        # Gera prompt
        prompt = processor.generate_essay_prompt(
            case['question'], 
            case['detection_result']
        )
        
        print(f"\n🎯 PROMPT GERADO:")
        print("-" * 40)
        # Mostra apenas as primeiras linhas do prompt
        prompt_lines = prompt.split('\n')
        for line in prompt_lines[:15]:  # Primeiras 15 linhas
            print(line)
        if len(prompt_lines) > 15:
            print("...")
            print(f"[Prompt completo tem {len(prompt_lines)} linhas]")
        
        print("-" * 40)


def main():
    """Executa todos os testes."""
    print("🧪 INICIANDO TESTES DO SISTEMA DE QUESTÕES DISSERTATIVAS")
    print("="*70)
    
    try:
        # Teste de detecção
        test_essay_detection()
        
        # Teste de processamento
        test_essay_processing()
        
        print("\n" + "="*70)
        print("✅ TODOS OS TESTES CONCLUÍDOS COM SUCESSO!")
        print("="*70)
        
    except Exception as e:
        print(f"\n❌ ERRO DURANTE OS TESTES: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
