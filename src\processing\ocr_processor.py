"""
Module for OCR processing using Tesseract.
"""
import os
import subprocess
import pytesseract
import cv2
import numpy as np
from PIL import Image
from ..core.config import TESSERACT_PATH, DEBUG

class OCRProcessor:
    def __init__(self):
        # Try to find Tesseract executable
        tesseract_found = False

        # First try the main path
        if TESSERACT_PATH and os.path.exists(TESSERACT_PATH):
            pytesseract.pytesseract.tesseract_cmd = TESSERACT_PATH
            tesseract_found = True
            if DEBUG:
                print(f"Tesseract found at {TESSERACT_PATH}")
        else:
            # Try alternative paths
            alternative_paths = [
                r"C:\Program Files\Tesseract-OCR\tesseract.exe",
                r"C:\Program Files (x86)\Tesseract-OCR\tesseract.exe",
                "/usr/bin/tesseract",
                "/usr/local/bin/tesseract"
            ]
            for path in alternative_paths:
                if os.path.exists(path):
                    pytesseract.pytesseract.tesseract_cmd = path
                    tesseract_found = True
                    if DEBUG:
                        print(f"Tesseract found at alternative path: {path}")
                    break

            # If still not found, try using system PATH
            if not tesseract_found:
                try:
                    # Try to run tesseract version command
                    import subprocess
                    result = subprocess.run(['tesseract', '--version'],
                                          stdout=subprocess.PIPE,
                                          stderr=subprocess.PIPE,
                                          text=True,
                                          timeout=5)
                    if result.returncode == 0:
                        pytesseract.pytesseract.tesseract_cmd = 'tesseract'
                        tesseract_found = True
                        if DEBUG:
                            print("Tesseract found in system PATH")
                except Exception as e:
                    if DEBUG:
                        print(f"Error checking tesseract in PATH: {e}")

        # Final warning if not found
        if not tesseract_found:
            print("WARNING: Tesseract OCR not found. OCR functionality will not work.")
            print("Please install Tesseract OCR from: https://github.com/UB-Mannheim/tesseract/wiki")
            print("After installation, update the TESSERACT_PATH in config.py")

    def preprocess_image(self, image, enhance_mode='default'):
        """
        Preprocess the image to improve OCR accuracy.

        Args:
            image: Input image
            enhance_mode: Preprocessing mode ('default', 'text', 'document', 'low_contrast')

        Returns:
            Preprocessed image
        """
        # Save original for debugging if needed
        original = image.copy()

        # Convert to grayscale if it's not already
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
        else:
            gray = image

        # Resize image if too small (helps with OCR accuracy)
        h, w = gray.shape
        min_size = 1000
        scale_factor = 1
        if h < min_size or w < min_size:
            scale_factor = min_size / min(h, w)
            gray = cv2.resize(gray, None, fx=scale_factor, fy=scale_factor, interpolation=cv2.INTER_CUBIC)

        # Apply different preprocessing based on the mode
        if enhance_mode == 'text':
            # For text-heavy content
            # Increase contrast
            gray = cv2.convertScaleAbs(gray, alpha=1.5, beta=0)

            # Apply adaptive thresholding
            binary = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                                          cv2.THRESH_BINARY, 11, 2)

            # Noise removal
            kernel = np.ones((1, 1), np.uint8)
            binary = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)

            # Dilation to make text more visible
            kernel = np.ones((1, 1), np.uint8)
            processed = cv2.dilate(binary, kernel, iterations=1)

        elif enhance_mode == 'document':
            # For document-like content
            # Deskew the image (correct rotation)
            coords = np.column_stack(np.where(gray > 0))
            angle = cv2.minAreaRect(coords)[-1]
            if angle < -45:
                angle = -(90 + angle)
            else:
                angle = -angle
            (h, w) = gray.shape[:2]
            center = (w // 2, h // 2)
            M = cv2.getRotationMatrix2D(center, angle, 1.0)
            gray = cv2.warpAffine(gray, M, (w, h), flags=cv2.INTER_CUBIC, borderMode=cv2.BORDER_REPLICATE)

            # Increase contrast
            gray = cv2.convertScaleAbs(gray, alpha=1.3, beta=10)

            # Apply Otsu's thresholding
            _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

            # Noise removal
            processed = cv2.medianBlur(binary, 3)

        elif enhance_mode == 'low_contrast':
            # For low contrast images
            # Apply CLAHE (Contrast Limited Adaptive Histogram Equalization)
            clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
            gray = clahe.apply(gray)

            # Sharpen the image
            kernel = np.array([[-1, -1, -1], [-1, 9, -1], [-1, -1, -1]])
            gray = cv2.filter2D(gray, -1, kernel)

            # Apply adaptive thresholding
            processed = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                                             cv2.THRESH_BINARY, 11, 2)
        else:
            # Default processing
            # Apply bilateral filter to preserve edges while removing noise
            gray = cv2.bilateralFilter(gray, 9, 75, 75)

            # Apply adaptive thresholding
            binary = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                                          cv2.THRESH_BINARY, 11, 2)

            # Noise removal
            processed = cv2.medianBlur(binary, 3)

        # Save intermediate results for debugging if needed
        if DEBUG:
            cv2.imwrite("debug_original.png", original)
            cv2.imwrite("debug_processed.png", processed)

        return processed

    def extract_text(self, image, lang='eng'):
        """
        Extract text from the image using Tesseract OCR.
        Tries multiple preprocessing modes to get the best result.
        """
        # Check if Tesseract is available
        if not os.path.exists(pytesseract.pytesseract.tesseract_cmd) and pytesseract.pytesseract.tesseract_cmd != 'tesseract':
            error_msg = f"Tesseract not found at: {pytesseract.pytesseract.tesseract_cmd}"
            print(error_msg)
            return ""

        # Try to check available languages
        try:
            result = subprocess.run([pytesseract.pytesseract.tesseract_cmd, '--list-langs'],
                                  stdout=subprocess.PIPE,
                                  stderr=subprocess.PIPE,
                                  text=True,
                                  timeout=5)

            if result.returncode == 0:
                # Languages are usually returned in stderr
                langs = result.stderr.strip().split('\n')
                if len(langs) > 1:
                    langs = langs[1:]  # Remove header line

                if DEBUG:
                    print(f"Available languages: {langs}")

                # Check if Portuguese is available
                if 'por' in langs and lang == 'eng':
                    print("Portuguese language pack is available. Consider using lang='por' for better results with Portuguese text.")
                    # We'll still use the requested language

                # Check if requested language is available
                if lang not in langs and lang != 'eng':
                    print(f"Warning: Requested language '{lang}' not available. Falling back to English.")
                    lang = 'eng'  # Fall back to English
        except Exception as e:
            if DEBUG:
                print(f"Error checking languages: {e}")
                # Continue with the requested language

        try:
            # Try different preprocessing modes and combine results
            modes = ['default', 'text', 'document', 'low_contrast']
            all_texts = []

            for mode in modes:
                # Preprocess the image with current mode
                processed_image = self.preprocess_image(image, enhance_mode=mode)

                # Extract text using Tesseract with optimized configuration
                config = '--psm 6'  # Assume a single block of text
                if mode == 'document':
                    config = '--psm 3'  # Fully automatic page segmentation
                elif mode == 'text':
                    config = '--psm 6 --oem 1'  # Use LSTM OCR Engine

                try:
                    text = pytesseract.image_to_string(processed_image, lang=lang, config=config)

                    # Only add non-empty results
                    if text.strip():
                        all_texts.append(text)
                        if DEBUG:
                            print(f"Mode '{mode}' extracted {len(text)} characters")
                except Exception as mode_error:
                    # Suprimir erros relacionados a arquivos de idioma não encontrados
                    if DEBUG and not "Error opening data file" in str(mode_error):
                        print(f"Error in mode '{mode}': {mode_error}")
                    continue

            # Choose the best result (longest text, assuming it captured more content)
            if all_texts:
                best_text = max(all_texts, key=len)
                if DEBUG:
                    print(f"Best extraction: {len(best_text)} characters")
                return best_text
            else:
                if DEBUG:
                    # Mensagem mais amigável quando não há texto extraído
                    print("Nenhum texto extraído - continuando sem OCR")
                return ""

        except Exception as e:
            # Suprimir erros relacionados a arquivos de idioma não encontrados
            if DEBUG and not "Error opening data file" in str(e):
                print(f"OCR error: {e}")
            return ""

    def extract_text_with_layout(self, image, lang='eng'):
        """
        Extract text with layout information.
        Otimizado para desempenho - usa apenas os modos mais eficientes.
        Focuses on extracting questions and answer choices.
        """
        # Check if Tesseract is available
        if not os.path.exists(pytesseract.pytesseract.tesseract_cmd) and pytesseract.pytesseract.tesseract_cmd != 'tesseract':
            error_msg = f"Tesseract not found at: {pytesseract.pytesseract.tesseract_cmd}"
            print(error_msg)
            return ""

        try:
            # Usar apenas os modos mais eficientes para melhorar o desempenho
            # Reduzido de 4 para 2 modos para maior velocidade
            modes = ['text', 'default']  # 'text' geralmente é melhor para questões
            best_data = None
            best_text_length = 0

            for mode in modes:
                try:
                    # Preprocess the image with current mode
                    processed_image = self.preprocess_image(image, enhance_mode=mode)

                    # Extract text with layout information - configuração otimizada
                    # Usar PSM 6 (bloco único de texto) é mais rápido e geralmente suficiente para questões
                    config = '--psm 6 --oem 1'  # LSTM é mais preciso para texto

                    # Adicionar configurações para melhorar a velocidade
                    config += ' --tessdata-dir "' + os.path.dirname(pytesseract.pytesseract.tesseract_cmd) + '/../tessdata"'

                    # Limitar o tempo de processamento para garantir resposta rápida
                    config += ' --dpi 300'  # Definir DPI para evitar auto-detecção lenta

                    data = pytesseract.image_to_data(processed_image, lang=lang, config=config,
                                                   output_type=pytesseract.Output.DICT)

                    # Count total text length
                    total_text = ''.join([text for text in data['text'] if text.strip()])

                    if len(total_text) > best_text_length:
                        best_text_length = len(total_text)
                        best_data = data

                    if DEBUG:
                        print(f"Mode '{mode}' extracted {len(total_text)} characters with layout")

                    # Se já encontrou texto suficiente, não tenta outros modos
                    if len(total_text) > 100:  # Texto suficiente para análise
                        break

                except Exception as mode_error:
                    # Suprimir erros relacionados a arquivos de idioma não encontrados
                    if DEBUG and not "Error opening data file" in str(mode_error):
                        print(f"Error in layout mode '{mode}': {mode_error}")
                    continue

            # If no text was extracted from any mode
            if not best_data or best_text_length == 0:
                if DEBUG:
                    # Mensagem mais amigável quando não há texto extraído
                    print("Nenhum texto extraído - continuando sem OCR")
                return ""

            # Organize text by lines and positions
            lines = {}
            for i in range(len(best_data['text'])):
                if best_data['text'][i].strip():
                    line_num = best_data['block_num'][i] * 1000 + best_data['line_num'][i]
                    if line_num not in lines:
                        lines[line_num] = []
                    lines[line_num].append({
                        'text': best_data['text'][i],
                        'left': best_data['left'][i],
                        'top': best_data['top'][i],
                        'width': best_data['width'][i],
                        'height': best_data['height'][i],
                        'conf': best_data['conf'][i]
                    })

            # Sort lines by vertical position and words by horizontal position
            sorted_lines = []
            for line_num in sorted(lines.keys()):
                sorted_words = sorted(lines[line_num], key=lambda x: x['left'])
                line_text = ' '.join([word['text'] for word in sorted_words])
                sorted_lines.append(line_text)

            # Filtrar e organizar o texto para focar em questões e alternativas
            import re

            # Padrões para identificar questões e alternativas
            question_patterns = [
                r'[Qq]uest[aãoõ]+\s*\d+',  # Questão X
                r'[Qq]\s*\d+',  # Q X
                r'\d+\s*[\.:\-\)]\s*[A-Za-z]',  # X. Texto (formato de lista numerada)
            ]

            alternative_patterns = [
                r'^\s*\(?[A-Ea-e]\)?\s+.+$',  # A) Texto ou (A) Texto
                r'^\s*\(?[A-Ea-e]\)?\)\s+.+$',  # A)) Texto ou (A)) Texto
                r'^\s*\[[A-Ea-e]\]\s+.+$',    # [A] Texto
                r'^\s*[A-Ea-e][\.:-]\s+.+$',  # A. Texto ou A: Texto
                r'^\s*[A-Ea-e]\s+.+$',        # A Texto (sem separador)
            ]

            # Identificar linhas importantes
            important_lines = []
            question_found = False

            for line in sorted_lines:
                line = line.strip()
                if not line:
                    continue

                # Verificar se é uma questão
                is_question = False
                for pattern in question_patterns:
                    if re.search(pattern, line):
                        is_question = True
                        question_found = True
                        break

                # Verificar se é uma alternativa
                is_alternative = False
                for pattern in alternative_patterns:
                    if re.match(pattern, line):
                        is_alternative = True
                        break

                # Se for uma questão ou alternativa, ou se estiver próximo a uma questão, adicionar
                if is_question or is_alternative or question_found:
                    important_lines.append(line)

            # Se não encontrou linhas importantes, usar todas as linhas
            if not important_lines:
                important_lines = sorted_lines

            result = '\n'.join(important_lines)

            if DEBUG:
                print(f"Final layout extraction: {len(result)} characters")
                print(f"Important lines: {len(important_lines)} of {len(sorted_lines)}")

            return result

        except Exception as e:
            # Suprimir erros relacionados a arquivos de idioma não encontrados
            if DEBUG and not "Error opening data file" in str(e):
                print(f"OCR layout error: {e}")
            return ""

# For testing
if __name__ == "__main__":
    processor = OCRProcessor()
    # Load a test image
    test_image = cv2.imread("capture.png")
    if test_image is not None:
        # Test with enhanced OCR
        text = processor.extract_text(test_image)
        print("\n=== Extracted text (best mode) ===")
        print(text)

        # Test with layout
        text_with_layout = processor.extract_text_with_layout(test_image)
        print("\n=== Extracted text with layout (best mode) ===")
        print(text_with_layout)

        # Test individual modes
        print("\n=== Testing individual modes ===")
        for mode in ['default', 'text', 'document', 'low_contrast']:
            processed = processor.preprocess_image(test_image, enhance_mode=mode)
            mode_text = pytesseract.image_to_string(processed)
            print(f"\nMode: {mode}")
            print(f"Characters: {len(mode_text)}")
            print("Sample: " + mode_text[:100] + "..." if len(mode_text) > 100 else mode_text)

            # Save processed image for inspection
            cv2.imwrite(f"debug_processed_{mode}.png", processed)
    else:
        print("No test image found")
