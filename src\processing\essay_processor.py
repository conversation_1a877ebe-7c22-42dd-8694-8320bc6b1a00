"""
Processador especializado para questões dissertativas/subjetivas.
"""
from typing import Dict, Optional, List
from ..core.config import DEBUG
from ..core.monitor_config import QUESTION_TYPE_CONFIGS


class EssayQuestionProcessor:
    """
    Processador especializado para gerar respostas estruturadas para questões dissertativas.
    """
    
    def __init__(self):
        """Inicializa o processador de questões dissertativas."""
        self.config = QUESTION_TYPE_CONFIGS.get('essay', {})
        if DEBUG:
            print("EssayQuestionProcessor inicializado")
    
    def generate_essay_prompt(self, question_text: str, detection_result: Dict, 
                            custom_config: Optional[Dict] = None) -> str:
        """
        Gera prompt especializado para questões dissertativas.
        
        Args:
            question_text: Texto da questão
            detection_result: Resultado da detecção de questão dissertativa
            custom_config: Configurações customizadas (opcional)
            
        Returns:
            Prompt otimizado para questões dissertativas
        """
        try:
            # Usa configuração customizada ou padrão
            config = custom_config if custom_config else self.config
            
            # Extrai características da questão
            response_length = detection_result.get('estimated_length', config.get('response_length', 'medium'))
            writing_style = detection_result.get('writing_style', config.get('writing_style', 'academic'))
            word_count_hint = detection_result.get('word_count_hint')
            
            # Constrói o prompt base
            prompt = self._build_base_prompt(question_text, config)
            
            # Adiciona instruções de estrutura
            if config.get('include_structure', True):
                prompt += self._add_structure_instructions(response_length)
            
            # Adiciona instruções de estilo
            prompt += self._add_style_instructions(writing_style, config)
            
            # Adiciona instruções de tamanho
            prompt += self._add_length_instructions(response_length, word_count_hint, config)
            
            # Adiciona instruções específicas
            prompt += self._add_specific_instructions(config)
            
            # Adiciona exemplo de formato (se configurado)
            if config.get('format_response', True):
                prompt += self._add_format_example(response_length)
            
            if DEBUG:
                print(f"Prompt dissertativo gerado - Estilo: {writing_style}, Tamanho: {response_length}")
            
            return prompt
            
        except Exception as e:
            if DEBUG:
                print(f"Erro ao gerar prompt dissertativo: {e}")
            return self._get_fallback_prompt(question_text)
    
    def _build_base_prompt(self, question_text: str, config: Dict) -> str:
        """Constrói o prompt base."""
        language = config.get('language', 'pt-BR')
        
        if language == 'pt-BR':
            base = f"""Você é um assistente especializado em questões dissertativas e subjetivas. 
Sua tarefa é fornecer uma resposta completa, bem estruturada e fundamentada para a seguinte questão:

QUESTÃO:
{question_text}

INSTRUÇÕES PARA SUA RESPOSTA:
"""
        else:
            base = f"""You are an assistant specialized in essay and subjective questions.
Your task is to provide a complete, well-structured and well-founded answer to the following question:

QUESTION:
{question_text}

INSTRUCTIONS FOR YOUR ANSWER:
"""
        
        return base
    
    def _add_structure_instructions(self, response_length: str) -> str:
        """Adiciona instruções de estrutura."""
        if response_length == 'short':
            return """
• Organize sua resposta de forma clara e direta
• Use parágrafos bem definidos
• Apresente os pontos principais de forma objetiva
"""
        elif response_length == 'long':
            return """
• Estruture sua resposta com introdução, desenvolvimento e conclusão
• Use múltiplos parágrafos para organizar as ideias
• Desenvolva cada ponto com detalhes e exemplos
• Faça conexões entre os conceitos apresentados
• Conclua de forma sintética e coerente
"""
        else:  # medium
            return """
• Organize sua resposta em parágrafos bem estruturados
• Apresente uma introdução breve ao tema
• Desenvolva os pontos principais com clareza
• Conclua de forma coerente
"""
    
    def _add_style_instructions(self, writing_style: str, config: Dict) -> str:
        """Adiciona instruções de estilo."""
        style_instructions = {
            'academic': """
• Use linguagem formal e acadêmica
• Fundamente suas afirmações com argumentos sólidos
• Evite linguagem coloquial ou informal
• Use terminologia técnica quando apropriado
• Mantenha objetividade e imparcialidade
""",
            'technical': """
• Use terminologia técnica precisa
• Seja específico e detalhado
• Inclua procedimentos ou métodos quando relevante
• Mantenha clareza e precisão técnica
• Evite ambiguidades
""",
            'formal': """
• Use linguagem formal e respeitosa
• Mantenha tom profissional
• Seja claro e direto
• Evite gírias ou expressões coloquiais
• Use estrutura gramatical correta
""",
            'casual': """
• Use linguagem clara e acessível
• Seja direto e objetivo
• Mantenha tom amigável mas profissional
• Use exemplos práticos quando possível
"""
        }
        
        return style_instructions.get(writing_style, style_instructions['formal'])
    
    def _add_length_instructions(self, response_length: str, word_count_hint: Optional[Dict], 
                                config: Dict) -> str:
        """Adiciona instruções de tamanho."""
        if word_count_hint:
            if word_count_hint['type'] == 'target':
                return f"""
• Sua resposta deve ter aproximadamente {word_count_hint['target']} palavras
• Mantenha-se próximo ao tamanho solicitado
"""
            else:  # range
                return f"""
• Sua resposta deve ter entre {word_count_hint['min']} e {word_count_hint['max']} palavras
• Mantenha-se dentro do intervalo especificado
"""
        
        # Instruções baseadas no tamanho estimado
        length_instructions = {
            'short': f"""
• Resposta concisa: {config.get('min_words', 50)}-{config.get('min_words', 50) + 50} palavras
• Seja direto e objetivo
• Foque nos pontos essenciais
""",
            'medium': f"""
• Resposta moderada: {config.get('min_words', 50)}-{config.get('max_words', 500)} palavras
• Desenvolva os pontos principais adequadamente
• Mantenha equilíbrio entre detalhamento e concisão
""",
            'long': f"""
• Resposta detalhada: {config.get('max_words', 500)}+ palavras
• Desenvolva o tema com profundidade
• Inclua múltiplos aspectos e perspectivas
• Use exemplos e argumentações extensas
"""
        }
        
        return length_instructions.get(response_length, length_instructions['medium'])
    
    def _add_specific_instructions(self, config: Dict) -> str:
        """Adiciona instruções específicas baseadas na configuração."""
        instructions = ""
        
        if config.get('include_examples', True):
            instructions += """
• Inclua exemplos práticos quando apropriado
• Use casos concretos para ilustrar seus pontos
"""
        
        if config.get('include_citations', False):
            instructions += """
• Referencie fontes quando possível
• Mencione autores ou teorias relevantes
"""
        
        if config.get('academic_tone', True):
            instructions += """
• Mantenha tom acadêmico e científico
• Use argumentação lógica e fundamentada
"""
        
        return instructions
    
    def _add_format_example(self, response_length: str) -> str:
        """Adiciona exemplo de formato."""
        if response_length == 'short':
            return """
FORMATO ESPERADO:
[Resposta direta e objetiva em 1-2 parágrafos bem estruturados]

RESPOSTA:
"""
        elif response_length == 'long':
            return """
FORMATO ESPERADO:
INTRODUÇÃO: [Apresentação do tema e contextualização]

DESENVOLVIMENTO: [Argumentação detalhada com múltiplos pontos]
- Ponto 1: [Desenvolvimento com exemplos]
- Ponto 2: [Desenvolvimento com exemplos]
- Ponto 3: [Desenvolvimento com exemplos]

CONCLUSÃO: [Síntese e considerações finais]

RESPOSTA:
"""
        else:  # medium
            return """
FORMATO ESPERADO:
[Introdução breve]
[Desenvolvimento dos pontos principais]
[Conclusão coerente]

RESPOSTA:
"""
    
    def _get_fallback_prompt(self, question_text: str) -> str:
        """Retorna prompt de fallback em caso de erro."""
        return f"""Responda de forma completa e estruturada à seguinte questão:

{question_text}

Organize sua resposta de forma clara, com introdução, desenvolvimento e conclusão.
Use linguagem formal e fundamente suas afirmações.
"""
    
    def process_essay_response(self, raw_response: str, detection_result: Dict, 
                             config: Optional[Dict] = None) -> str:
        """
        Processa e formata a resposta de uma questão dissertativa.
        
        Args:
            raw_response: Resposta bruta do LLM
            detection_result: Resultado da detecção
            config: Configurações (opcional)
            
        Returns:
            Resposta formatada e processada
        """
        try:
            config = config if config else self.config
            
            # Remove prompt residual se presente
            response = self._clean_response(raw_response)
            
            # Adiciona formatação se configurado
            if config.get('format_response', True):
                response = self._format_response(response, detection_result)
            
            # Adiciona metadados se configurado
            response = self._add_metadata(response, detection_result, config)
            
            return response
            
        except Exception as e:
            if DEBUG:
                print(f"Erro ao processar resposta dissertativa: {e}")
            return raw_response
    
    def _clean_response(self, response: str) -> str:
        """Remove elementos indesejados da resposta."""
        # Remove prompts residuais comuns
        cleanup_patterns = [
            'RESPOSTA:',
            'FORMATO ESPERADO:',
            'INSTRUÇÕES PARA SUA RESPOSTA:',
            'QUESTÃO:',
            'QUESTION:'
        ]
        
        cleaned = response
        for pattern in cleanup_patterns:
            cleaned = cleaned.replace(pattern, '').strip()
        
        return cleaned
    
    def _format_response(self, response: str, detection_result: Dict) -> str:
        """Aplica formatação à resposta."""
        # Adiciona quebras de linha entre parágrafos se necessário
        paragraphs = response.split('\n\n')
        if len(paragraphs) == 1:
            # Tenta dividir por pontos finais seguidos de maiúscula
            import re
            sentences = re.split(r'\.(?=\s+[A-Z])', response)
            if len(sentences) > 3:
                # Agrupa sentenças em parágrafos
                formatted_paragraphs = []
                current_paragraph = []
                for sentence in sentences:
                    current_paragraph.append(sentence.strip())
                    if len(current_paragraph) >= 2:
                        formatted_paragraphs.append('. '.join(current_paragraph) + '.')
                        current_paragraph = []
                
                if current_paragraph:
                    formatted_paragraphs.append('. '.join(current_paragraph))
                
                response = '\n\n'.join(formatted_paragraphs)
        
        return response
    
    def _add_metadata(self, response: str, detection_result: Dict, config: Dict) -> str:
        """Adiciona metadados à resposta."""
        metadata = []
        
        # Adiciona informações sobre o tipo de questão
        if detection_result.get('is_essay'):
            confidence = detection_result.get('confidence', 0)
            metadata.append(f"Questão dissertativa detectada (confiança: {confidence:.0%})")
        
        # Adiciona informações sobre contagem de palavras
        word_count = len(response.split())
        metadata.append(f"Resposta: {word_count} palavras")
        
        # Adiciona estilo detectado
        writing_style = detection_result.get('writing_style', 'academic')
        style_names = {
            'academic': 'Acadêmico',
            'technical': 'Técnico', 
            'formal': 'Formal',
            'casual': 'Casual'
        }
        metadata.append(f"Estilo: {style_names.get(writing_style, writing_style)}")
        
        if metadata:
            return f"{response}\n\n[{' • '.join(metadata)}]"
        
        return response
