[{"id": 5, "session_id": "session_20250531_082137", "timestamp": "2025-05-31T08:21:38.132986", "question_hash": "6a584c84a28351731dfe7ce03a133387", "question_number": 5, "domain": "mecanica", "question_type": "geral", "has_images": 0, "ocr_text": "Questão 5: Qual combustível é menos poluente? A) Gasolina B) Diesel C) Etanol D) GNV", "ocr_confidence": null, "llm_provider": "Gemini", "llm_model": "gemini-pro-vision", "llm_response": "Resposta correta: D) GNV\n\nO GNV (Gás Natural Veicular) é considerado o combustível menos poluente entre as opções apresentadas.", "llm_confidence": 85, "selected_answer": "D", "processing_time": 2.1, "image_path": "logged_images\\2025-05\\6a584c84a28351731dfe7ce03a133387_082138.png", "metadata": "{\"session_id\": \"session_20250531_082137\", \"original_image_path\": \"test_question_image.png\", \"text_length\": 84, \"response_length\": 127, \"has_calculation\": false, \"confidence_category\": \"alta\", \"additional\": {\"test_data\": true, \"domain_override\": \"meio_ambiente\"}}", "is_validated": 1, "is_correct": 0, "error_type": "Conhecimento desatualizado", "reviewer_notes": "Informação pode estar desatualizada", "created_at": "2025-05-31 11:21:38"}, {"id": 4, "session_id": "session_20250531_082137", "timestamp": "2025-05-31T08:21:38.022692", "question_hash": "8f81324e1f0b214ae4d41f3ef25ea8fb", "question_number": 4, "domain": "distancias", "question_type": "calculo", "has_images": 0, "ocr_text": "Questão 4: Para calcular a distância de frenagem, considera-se: A) Apenas a velocidade B) Velocidade e peso C) Velocidade, peso e condições da via D) Apenas as condições da via", "ocr_confidence": null, "llm_provider": "OpenRouter", "llm_model": "gpt-4-vision-preview", "llm_response": "Resposta correta: C) Velocidade, peso e condições da via\n\nA distância de frenagem depende de múltiplos fatores incluindo velocidade, peso do veículo e condições da via.", "llm_confidence": 91, "selected_answer": "C", "processing_time": 2.7, "image_path": "logged_images\\2025-05\\8f81324e1f0b214ae4d41f3ef25ea8fb_082138.png", "metadata": "{\"session_id\": \"session_20250531_082137\", \"original_image_path\": \"test_question_image.png\", \"text_length\": 176, \"response_length\": 168, \"has_calculation\": false, \"confidence_category\": \"muito_alta\", \"additional\": {\"test_data\": true, \"domain_override\": \"direcao_defensiva\"}}", "is_validated": 1, "is_correct": 1, "error_type": null, "reviewer_notes": "Explicação completa", "created_at": "2025-05-31 11:21:38"}, {"id": 3, "session_id": "session_20250531_082137", "timestamp": "2025-05-31T08:21:37.910087", "question_hash": "5a8a2df1b8a60096663664131230d73e", "question_number": 3, "domain": "<PERSON><PERSON><PERSON><PERSON>", "question_type": "calculo", "has_images": 1, "ocr_text": "Questão 3: A placa R-1 significa: A) Parada obrigatória B) Proibido virar à esquerda C) Velocidade máxima D) Sentido proibido", "ocr_confidence": null, "llm_provider": "HuggingFace", "llm_model": "llava-hf/llava-1.5-7b-hf", "llm_response": "Resposta correta: A) Parada obrigatória\n\nA placa R-1 indica parada obrigatória, sendo uma placa de regulamentação.", "llm_confidence": 72, "selected_answer": "A", "processing_time": 4.1, "image_path": "logged_images\\2025-05\\5a8a2df1b8a60096663664131230d73e_082137.png", "metadata": "{\"session_id\": \"session_20250531_082137\", \"original_image_path\": \"test_question_image.png\", \"text_length\": 125, \"response_length\": 114, \"has_calculation\": false, \"confidence_category\": \"media\", \"additional\": {\"test_data\": true, \"domain_override\": \"sinalizacao\"}}", "is_validated": 1, "is_correct": 0, "error_type": "Interpretação incorreta", "reviewer_notes": "Confundiu com outra placa", "created_at": "2025-05-31 11:21:37"}, {"id": 2, "session_id": "session_20250531_082137", "timestamp": "2025-05-31T08:21:37.790277", "question_hash": "b7b778044a7e54c9013e5c9f768d5e05", "question_number": 2, "domain": "<PERSON><PERSON><PERSON><PERSON>", "question_type": "interpretacao", "has_images": 1, "ocr_text": "Questão 2: <PERSON> caso de acidente, qual é o primeiro procedimento? A) Sinalizar o local B) Chamar a polícia C) Verificar feridos D) Remover os veículos", "ocr_confidence": null, "llm_provider": "Gemini", "llm_model": "gemini-pro-vision", "llm_response": "Resposta correta: C) Verificar feridos\n\nO primeiro procedimento em caso de acidente é verificar se há feridos e prestar os primeiros socorros necessários.", "llm_confidence": 88, "selected_answer": "C", "processing_time": 1.8, "image_path": "logged_images\\2025-05\\b7b778044a7e54c9013e5c9f768d5e05_082137.png", "metadata": "{\"session_id\": \"session_20250531_082137\", \"original_image_path\": \"test_question_image.png\", \"text_length\": 148, \"response_length\": 154, \"has_calculation\": false, \"confidence_category\": \"alta\", \"additional\": {\"test_data\": true, \"domain_override\": \"primeiros_socorros\"}}", "is_validated": 1, "is_correct": 1, "error_type": null, "reviewer_notes": "Procedimento correto", "created_at": "2025-05-31 11:21:37"}, {"id": 1, "session_id": "session_20250531_082137", "timestamp": "2025-05-31T08:21:37.671768", "question_hash": "3f559e0eb3592746fbeac6269e64f86a", "question_number": 1, "domain": "geral", "question_type": "calculo", "has_images": 0, "ocr_text": "Questão 1: Qual é a velocidade máxima permitida em vias urbanas? A) 40 km/h B) 50 km/h C) 60 km/h D) 70 km/h", "ocr_confidence": null, "llm_provider": "OpenRouter", "llm_model": "gpt-4-vision-preview", "llm_response": "Resposta correta: C) 60 km/h\n\nEm vias urbanas, a velocidade máxima permitida é de 60 km/h, conforme estabelecido pelo Código de Trânsito Brasileiro.", "llm_confidence": 95, "selected_answer": "C", "processing_time": 2.3, "image_path": "logged_images\\2025-05\\3f559e0eb3592746fbeac6269e64f86a_082137.png", "metadata": "{\"session_id\": \"session_20250531_082137\", \"original_image_path\": \"test_question_image.png\", \"text_length\": 108, \"response_length\": 148, \"has_calculation\": false, \"confidence_category\": \"muito_alta\", \"additional\": {\"test_data\": true, \"domain_override\": \"legislacao\"}}", "is_validated": 1, "is_correct": 1, "error_type": null, "reviewer_notes": "Resposta correta e bem explicada", "created_at": "2025-05-31 11:21:37"}]