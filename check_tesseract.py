"""
Script para verificar a instalação do Tesseract OCR.
"""
import os
import sys
import subprocess

def check_tesseract():
    """Verifica se o Tesseract OCR está instalado e configurado corretamente."""
    print("Verificando instalação do Tesseract OCR...")
    
    # Caminhos comuns para o Tesseract OCR
    tesseract_paths = [
        r"C:\Program Files\Tesseract-OCR\tesseract.exe",  # Caminho padrão 64-bit
        r"C:\Program Files (x86)\Tesseract-OCR\tesseract.exe",  # Caminho padrão 32-bit
        r"C:\Users\<USER>\Tesseract-OCR\tesseract.exe",  # Caminho alternativo
        r"tesseract",  # Usar o PATH do sistema
    ]
    
    # Verificar cada caminho
    for path in tesseract_paths:
        if path == "tesseract":
            print(f"\nVerificando Tesseract no PATH do sistema...")
            try:
                result = subprocess.run(['tesseract', '--version'], 
                                      stdout=subprocess.PIPE, 
                                      stderr=subprocess.PIPE,
                                      text=True,
                                      timeout=5)
                if result.returncode == 0:
                    print("✅ Tesseract encontrado no PATH do sistema!")
                    print(f"Versão: {result.stdout.splitlines()[0]}")
                    return True
            except Exception as e:
                print(f"❌ Erro ao verificar Tesseract no PATH: {e}")
        else:
            print(f"\nVerificando caminho: {path}")
            if os.path.exists(path):
                print(f"✅ Tesseract encontrado em: {path}")
                try:
                    result = subprocess.run([path, '--version'], 
                                          stdout=subprocess.PIPE, 
                                          stderr=subprocess.PIPE,
                                          text=True,
                                          timeout=5)
                    if result.returncode == 0:
                        print(f"Versão: {result.stdout.splitlines()[0]}")
                        return True
                except Exception as e:
                    print(f"❌ Erro ao verificar versão: {e}")
            else:
                print(f"❌ Tesseract não encontrado em: {path}")
    
    print("\n❌ Tesseract OCR não encontrado em nenhum caminho comum.")
    print("\nPara instalar o Tesseract OCR:")
    print("1. Baixe o instalador em: https://github.com/UB-Mannheim/tesseract/wiki")
    print("2. Execute o instalador e siga as instruções")
    print("3. Anote o diretório de instalação (geralmente C:\\Program Files\\Tesseract-OCR)")
    print("4. Atualize o caminho no arquivo config.py")
    
    return False

if __name__ == "__main__":
    check_tesseract()
    
    # Aguardar entrada do usuário antes de fechar
    input("\nPressione Enter para sair...")
