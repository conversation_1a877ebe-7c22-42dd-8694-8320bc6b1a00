"""
Gerenciador de clientes LLM.
"""
from ..core.config import LLM_PROVIDER, DEBUG
from ..core.enhanced_prompt_manager import EnhancedPromptManager
from ..processing.essay_processor import EssayQuestionProcessor

class LLMManager:
    """
    Gerenciador de clientes LLM que permite alternar entre diferentes provedores.
    """

    def __init__(self, provider=None):
        """
        Inicializa o gerenciador de clientes LLM.

        Args:
            provider: Nome do provedor a ser usado (openrouter, ollama, gemini)
        """
        self.provider = provider or LLM_PROVIDER
        self.client = None
        self.available_providers = self._get_available_providers()
        self.essay_processor = EssayQuestionProcessor()

        # Inicializa o cliente
        self._initialize_client()

    def _get_available_providers(self):
        """
        Retorna a lista de provedores disponíveis.

        Returns:
            Lista de provedores disponíveis
        """
        providers = []

        # Verifica se o OpenRouter está disponível
        try:
            from ..core.config import LLM_API_KEY
            if LLM_API_KEY:
                providers.append("openrouter")
        except (ImportError, AttributeError):
            if DEBUG:
                print("Aviso: Cliente OpenRouter não disponível")

        # Verifica se o Hugging Face está disponível
        try:
            from ..core.config import HUGGINGFACE_API_KEY
            if HUGGINGFACE_API_KEY:
                providers.append("huggingface")
        except (ImportError, AttributeError):
            if DEBUG:
                print("Aviso: Cliente Hugging Face não disponível")

        # Verifica se o Ollama está disponível
        try:
            # Apenas verifica se o módulo está disponível, não tenta conectar
            providers.append("ollama")
        except:
            pass

        # Verifica se o Gemini está disponível
        try:
            from ..core.config import GEMINI_API_KEY
            if GEMINI_API_KEY:
                # Verifica se a biblioteca está instalada
                try:
                    import google.generativeai
                    providers.append("gemini")
                except ImportError:
                    if DEBUG:
                        print("Aviso: Biblioteca google-generativeai não instalada")
        except (ImportError, AttributeError):
            if DEBUG:
                print("Aviso: Cliente Gemini não disponível")

        return providers

    def _initialize_client(self):
        """
        Inicializa o cliente LLM com base no provedor selecionado.
        """
        if self.provider == "openrouter":
            try:
                from .direct_vision_client import DirectVisionClient
                self.client = DirectVisionClient()
                if DEBUG:
                    print("Cliente OpenRouter inicializado")
            except ImportError:
                if DEBUG:
                    print("Erro ao inicializar cliente OpenRouter")
                self._try_next_provider()

        elif self.provider == "huggingface":
            try:
                from .huggingface_client import HuggingFaceClient
                self.client = HuggingFaceClient()
                if not self.client.is_available:
                    if DEBUG:
                        print("Cliente Hugging Face não está disponível")
                    self._try_next_provider()
                elif DEBUG:
                    print("Cliente Hugging Face inicializado")
            except ImportError:
                if DEBUG:
                    print("Erro ao inicializar cliente Hugging Face")
                self._try_next_provider()

        elif self.provider == "ollama":
            try:
                from .ollama_client import OllamaClient
                self.client = OllamaClient()
                if not self.client.is_available:
                    if DEBUG:
                        print("Cliente Ollama não está disponível")
                    self._try_next_provider()
                elif DEBUG:
                    print("Cliente Ollama inicializado")
            except ImportError:
                if DEBUG:
                    print("Erro ao inicializar cliente Ollama")
                self._try_next_provider()

        elif self.provider == "gemini":
            try:
                from .gemini_client import GeminiClient
                self.client = GeminiClient()
                if not self.client.is_available:
                    if DEBUG:
                        print("Cliente Gemini não está disponível")
                    self._try_next_provider()
                elif DEBUG:
                    print("Cliente Gemini inicializado")
            except ImportError:
                if DEBUG:
                    print("Erro ao inicializar cliente Gemini")
                self._try_next_provider()

        else:
            if DEBUG:
                print(f"Provedor {self.provider} não reconhecido")
            self._try_next_provider()

    def _try_next_provider(self, error_type=None):
        """
        Tenta inicializar o próximo provedor disponível.

        Args:
            error_type: Tipo de erro que causou a mudança de provedor (opcional)
        """
        if not self.available_providers:
            if DEBUG:
                print("Nenhum provedor disponível")
            return

        # Remove o provedor atual da lista
        current_provider = self.provider
        if current_provider in self.available_providers:
            self.available_providers.remove(current_provider)

        # Tenta o próximo provedor
        if self.available_providers:
            # Prioriza provedores com base no tipo de erro
            if error_type == "service_unavailable" and "huggingface" in self.available_providers:
                # Se o Hugging Face estiver indisponível, evita tentar novamente
                self.available_providers.remove("huggingface")
                if DEBUG:
                    print(f"Removendo Hugging Face da lista de provedores disponíveis devido a serviço indisponível")
            elif error_type == "model_not_found" and "huggingface" in self.available_providers:
                # Se o modelo do Hugging Face não foi encontrado, remove temporariamente
                self.available_providers.remove("huggingface")
                if DEBUG:
                    print(f"Removendo Hugging Face da lista de provedores disponíveis devido a modelo não encontrado")

            # Se não houver mais provedores após a remoção, retorna
            if not self.available_providers:
                if DEBUG:
                    print("Não há mais provedores disponíveis após filtrar provedores com problemas")
                return

            # Seleciona o próximo provedor
            self.provider = self.available_providers[0]
            if DEBUG:
                print(f"Alternando de {current_provider} para {self.provider}")
            self._initialize_client()
        else:
            if DEBUG:
                print("Não há mais provedores disponíveis")

    def _try_next_model(self):
        """
        Tenta alternar para o próximo modelo disponível no provedor atual.

        Returns:
            bool: True se conseguiu alternar para outro modelo, False caso contrário
        """
        if not self.client:
            return False

        try:
            # Obtém a lista de modelos disponíveis
            models = self.client.get_available_models()

            if not models:
                if DEBUG:
                    print("Nenhum modelo disponível para este provedor")
                return False

            # Obtém o modelo atual
            current_model = None
            if hasattr(self.client, "model"):
                current_model = self.client.model

            # Encontra o próximo modelo na lista
            next_model = None
            for i, model in enumerate(models):
                if model["id"] == current_model:
                    # Se não for o último modelo da lista, pega o próximo
                    if i < len(models) - 1:
                        next_model = models[i + 1]["id"]
                        break

            # Se não encontrou o próximo modelo ou o atual não está na lista,
            # usa o primeiro modelo da lista (diferente do atual)
            if not next_model:
                for model in models:
                    if model["id"] != current_model:
                        next_model = model["id"]
                        break

            # Se encontrou um modelo alternativo, tenta usá-lo
            if next_model and next_model != current_model:
                if DEBUG:
                    print(f"Alternando para o modelo: {next_model}")
                return self.client.set_model(next_model)

            return False
        except Exception as e:
            if DEBUG:
                print(f"Erro ao tentar alternar para outro modelo: {str(e)}")
            return False

    def process_image_with_validation(self, image_path, extracted_text=None, prompt=None):
        """
        Processa uma imagem com sistema de validação integrado.

        Args:
            image_path: Caminho para a imagem
            extracted_text: Texto extraído da imagem (opcional)
            prompt: Prompt opcional para enviar junto com a imagem

        Returns:
            Resposta validada e formatada do LLM
        """
        if not self.client:
            return "Erro: Nenhum cliente LLM disponível"

        try:
            # Verifica se o cliente suporta validação
            if hasattr(self.client, 'process_image_with_validation'):
                if DEBUG:
                    if extracted_text:
                        print("Usando processamento com validação integrada (com texto extraído)")
                    else:
                        print("Usando processamento com validação integrada (sem texto extraído)")
                response = self.client.process_image_with_validation(image_path, extracted_text or "")
                return response
            else:
                # Fallback para processamento normal
                if DEBUG:
                    print("Cliente não suporta validação, usando processamento normal")
                return self.process_image(image_path, prompt, extracted_text)

        except Exception as e:
            error_msg = f"Erro ao processar imagem com validação: {str(e)}"
            if DEBUG:
                print(error_msg)

            # Fallback para processamento normal em caso de erro
            return self.process_image(image_path, prompt, extracted_text)

    def process_image(self, image_path, prompt=None, extracted_text=None):
        """
        Processa uma imagem com o cliente LLM atual, usando prompts aprimorados.

        Args:
            image_path: Caminho para a imagem
            prompt: Prompt opcional para enviar junto com a imagem
            extracted_text: Texto extraído da imagem (opcional)

        Returns:
            Resposta do LLM
        """
        if not self.client:
            return "Erro: Nenhum cliente LLM disponível"

        try:
            # Se temos texto extraído, podemos criar um prompt aprimorado
            if extracted_text:
                if DEBUG:
                    print(f"Usando texto extraído para criar prompt aprimorado: {len(extracted_text)} caracteres")

                # Cria um prompt aprimorado com base no texto extraído
                enhanced_prompt = EnhancedPromptManager.create_enhanced_prompt(extracted_text)

                if DEBUG:
                    print("Prompt aprimorado criado com base na categoria detectada")

                # Usa o prompt aprimorado
                response = self.client.process_image(image_path, enhanced_prompt)

                # Pós-processamento para garantir o formato correto
                return self._ensure_correct_format(response)
            elif not prompt:
                # Se não temos texto extraído nem prompt personalizado, cria um prompt padrão
                default_prompt = EnhancedPromptManager.create_enhanced_prompt("Questão de prova")
                response = self.client.process_image(image_path, default_prompt)
                return self._ensure_correct_format(response)
            else:
                # Usa o prompt fornecido
                response = self.client.process_image(image_path, prompt)
                return self._ensure_correct_format(response)

        except Exception as e:
            error_msg = f"Erro ao processar imagem com {self.get_provider_name()}: {str(e)}"
            if DEBUG:
                print(error_msg)

            # Verifica se é um erro de quota, serviço indisponível ou modelo não encontrado
            error_str = str(e).lower()
            is_quota_error = "quota" in error_str or "rate limit" in error_str or "429" in error_str
            is_service_unavailable = "503" in error_str or "service unavailable" in error_str or "unavailable" in error_str
            is_model_not_found = "404" in error_str or "not found" in error_str or "modelo não encontrado" in error_str
            should_try_alternative = is_quota_error or is_service_unavailable or is_model_not_found

            if is_quota_error:
                error_type = "quota excedida"
            elif is_service_unavailable:
                error_type = "serviço indisponível"
            elif is_model_not_found:
                error_type = "modelo não encontrado"
            else:
                error_type = "erro desconhecido"

            # Primeiro tenta alternar para outro modelo no mesmo provedor
            if should_try_alternative and self._try_next_model():
                if DEBUG:
                    print(f"Alternado para outro modelo no provedor {self.get_provider_name()} devido a {error_type}")

                try:
                    # Tenta novamente com o novo modelo
                    if extracted_text:
                        enhanced_prompt = EnhancedPromptManager.create_enhanced_prompt(extracted_text)
                        response = self.client.process_image(image_path, enhanced_prompt)
                        return self._ensure_correct_format(response)
                    elif not prompt:
                        default_prompt = EnhancedPromptManager.create_enhanced_prompt("Questão de prova")
                        response = self.client.process_image(image_path, default_prompt)
                        return self._ensure_correct_format(response)
                    else:
                        response = self.client.process_image(image_path, prompt)
                        return self._ensure_correct_format(response)
                except Exception as e_model:
                    if DEBUG:
                        print(f"Erro ao processar imagem com modelo alternativo: {str(e_model)}")
                    # Se falhar com o novo modelo, tenta outro provedor

            # Se não conseguiu alternar de modelo ou não é erro de quota/serviço, tenta outro provedor
            if is_service_unavailable:
                provider_error_type = "service_unavailable"
            elif is_quota_error:
                provider_error_type = "quota_exceeded"
            elif is_model_not_found:
                provider_error_type = "model_not_found"
            else:
                provider_error_type = None

            self._try_next_provider(error_type=provider_error_type)

            if self.client:
                try:
                    # Tenta novamente com o novo provedor
                    if extracted_text:
                        enhanced_prompt = EnhancedPromptManager.create_enhanced_prompt(extracted_text)
                        response = self.client.process_image(image_path, enhanced_prompt)
                        return self._ensure_correct_format(response)
                    elif not prompt:
                        default_prompt = EnhancedPromptManager.create_enhanced_prompt("Questão de prova")
                        response = self.client.process_image(image_path, default_prompt)
                        return self._ensure_correct_format(response)
                    else:
                        response = self.client.process_image(image_path, prompt)
                        return self._ensure_correct_format(response)
                except Exception as e2:
                    # Se falhar com o novo provedor, tenta alternar para outro modelo neste provedor
                    if self._try_next_model():
                        try:
                            # Tenta novamente com o novo modelo do novo provedor
                            if extracted_text:
                                enhanced_prompt = EnhancedPromptManager.create_enhanced_prompt(extracted_text)
                                response = self.client.process_image(image_path, enhanced_prompt)
                                return self._ensure_correct_format(response)
                            elif not prompt:
                                default_prompt = EnhancedPromptManager.create_enhanced_prompt("Questão de prova")
                                response = self.client.process_image(image_path, default_prompt)
                                return self._ensure_correct_format(response)
                            else:
                                response = self.client.process_image(image_path, prompt)
                                return self._ensure_correct_format(response)
                        except Exception as e3:
                            return f"Erro ao processar imagem com todos os provedores e modelos disponíveis. Último erro: {str(e3)}"
                    else:
                        return f"Erro ao processar imagem com provedor alternativo: {str(e2)}"
            else:
                return error_msg

    def process_essay_question(self, question_text, detection_result=None, custom_config=None):
        """
        Processa uma questão dissertativa/subjetiva usando prompt especializado.

        Args:
            question_text: Texto da questão
            detection_result: Resultado da detecção de questão dissertativa (opcional)
            custom_config: Configurações customizadas (opcional)

        Returns:
            Resposta estruturada para questão dissertativa
        """
        if not self.client:
            return "Erro: Nenhum cliente LLM disponível"

        try:
            # Gera prompt especializado para questões dissertativas
            essay_prompt = self.essay_processor.generate_essay_prompt(
                question_text,
                detection_result or {},
                custom_config
            )

            if DEBUG:
                print("Processando questão dissertativa com prompt especializado")
                print(f"Tipo de resposta: {detection_result.get('estimated_length', 'medium') if detection_result else 'medium'}")
                print(f"Estilo: {detection_result.get('writing_style', 'academic') if detection_result else 'academic'}")

            # Processa usando o prompt especializado
            response = self.client.process_text(essay_prompt)

            # Pós-processa a resposta
            if detection_result:
                response = self.essay_processor.process_essay_response(
                    response,
                    detection_result,
                    custom_config
                )

            return response

        except Exception as e:
            error_msg = f"Erro ao processar questão dissertativa: {str(e)}"
            if DEBUG:
                print(error_msg)

            # Fallback para processamento normal
            try:
                return self.process_text_question(question_text)
            except Exception as e2:
                return f"Erro no processamento: {str(e2)}"

    def process_text_question(self, question_text):
        """
        Processa uma questão baseada apenas em texto.

        Args:
            question_text: Texto da questão

        Returns:
            Resposta do LLM
        """
        if not self.client:
            return "Erro: Nenhum cliente LLM disponível"

        try:
            # Verifica se o cliente suporta processamento de texto
            if hasattr(self.client, 'process_text'):
                return self.client.process_text(question_text)
            else:
                # Fallback: cria um prompt básico
                prompt = f"Responda à seguinte questão de forma completa e estruturada:\n\n{question_text}"
                return prompt  # Retorna o prompt se não conseguir processar

        except Exception as e:
            error_msg = f"Erro ao processar questão de texto: {str(e)}"
            if DEBUG:
                print(error_msg)
            return error_msg

    def get_available_models(self):
        """
        Retorna a lista de modelos disponíveis para o cliente atual.

        Returns:
            Lista de dicionários com informações dos modelos
        """
        if not self.client:
            return []

        try:
            models = self.client.get_available_models()

            # Adiciona o nome do provedor aos modelos
            provider_name = self.get_provider_name()
            for model in models:
                model["provider"] = provider_name

            return models
        except Exception as e:
            if DEBUG:
                print(f"Erro ao obter modelos: {str(e)}")
            return []

    def set_model(self, model_id):
        """
        Define o modelo a ser usado pelo cliente atual.

        Args:
            model_id: ID do modelo

        Returns:
            True se o modelo foi definido com sucesso, False caso contrário
        """
        if not self.client:
            return False

        try:
            return self.client.set_model(model_id)
        except Exception as e:
            if DEBUG:
                print(f"Erro ao definir modelo: {str(e)}")
            return False

    def get_provider_name(self):
        """
        Retorna o nome do provedor atual.

        Returns:
            Nome do provedor
        """
        if not self.client:
            return "Nenhum provedor disponível"

        try:
            return self.client.get_provider_name()
        except Exception:
            return self.provider.capitalize()

    def set_provider(self, provider):
        """
        Define o provedor a ser usado.

        Args:
            provider: Nome do provedor

        Returns:
            True se o provedor foi definido com sucesso, False caso contrário
        """
        if provider not in self.available_providers:
            if DEBUG:
                print(f"Provedor {provider} não disponível")
            return False

        self.provider = provider
        self._initialize_client()
        return self.client is not None

    def _ensure_correct_format(self, response):
        """
        Garante que a resposta esteja no formato correto, começando com "Resposta correta:".
        Implementa verificações adicionais para garantir que a resposta siga o formato esperado.

        Args:
            response: Resposta original do LLM

        Returns:
            Resposta formatada corretamente
        """
        if not response:
            return "Erro: Não foi possível obter uma resposta do LLM."

        # Procura pela frase "Resposta correta:" na resposta
        import re
        match = re.search(r'Resposta correta:\s*([A-Ea-e])\)\s*(.*?)(?:\n|$)', response, re.DOTALL)

        if match:
            # Se encontrou, extrai a letra e o texto da alternativa
            letter = match.group(1)
            text = match.group(2).strip()

            # Formata a resposta corretamente
            formatted_start = f"Resposta correta: {letter}) {text}"

            # Encontra o índice onde começa a frase "Resposta correta:"
            start_index = response.find("Resposta correta:")

            # Pega o resto da resposta após a alternativa
            rest_of_response = response[start_index + len(formatted_start):].strip()

            # Verifica se há justificativas para as alternativas incorretas
            has_justification = False
            for alt_letter in ['A', 'B', 'C', 'D', 'E']:
                if alt_letter.lower() != letter.lower() and f"Alternativa {alt_letter})" in rest_of_response:
                    has_justification = True
                    break

            # Se não encontrou justificativas, procura por explicações gerais
            if not has_justification:
                # Procura por seções de explicação
                explanation_patterns = [
                    r'Justificativa:',
                    r'Explicação:',
                    r'Por que as outras alternativas estão incorretas:',
                    r'Análise das alternativas:',
                    r'Alternativas incorretas:'
                ]

                for pattern in explanation_patterns:
                    if pattern in rest_of_response:
                        has_justification = True
                        break

            # Destaca a resposta em negrito
            formatted_start = f"**{formatted_start}**"

            # Monta a resposta final
            final_response = f"{formatted_start}\n\n{rest_of_response}"

            # Se não há justificativas, adiciona um aviso
            if not has_justification and len(rest_of_response) > 10:
                final_response += "\n\n[Aviso: Não foram encontradas justificativas explícitas para as alternativas incorretas]"

            return final_response
        else:
            # Se não encontrou o formato esperado, tenta encontrar apenas a letra da alternativa
            alt_match = re.search(r'([A-Ea-e])\)\s*(.*?)(?:\n|$)', response, re.DOTALL)

            if alt_match:
                letter = alt_match.group(1)
                text = alt_match.group(2).strip()

                # Adiciona o prefixo correto e destaca em negrito
                formatted_response = f"**Resposta correta: {letter}) {text}**\n\n"

                # Adiciona o resto da resposta, removendo a parte que já foi formatada
                start_index = response.find(f"{letter})")
                end_index = start_index + len(f"{letter}) {text}")

                # Pega o início da resposta (antes da alternativa) e o fim (após a alternativa)
                beginning = response[:start_index].strip()
                ending = response[end_index:].strip()

                # Remove qualquer menção a "resposta correta" do início
                beginning = re.sub(r'.*[Rr]esposta\s+[Cc]orreta\s*:?\s*', '', beginning).strip()

                # Monta a resposta final
                if beginning:
                    formatted_response += f"{beginning}\n\n"

                formatted_response += ending

                # Verifica se há justificativas para as alternativas incorretas
                has_justification = False
                for alt_letter in ['A', 'B', 'C', 'D', 'E']:
                    if alt_letter.lower() != letter.lower() and f"Alternativa {alt_letter})" in formatted_response:
                        has_justification = True
                        break

                # Se não há justificativas, adiciona um aviso
                if not has_justification and len(formatted_response) > 100:
                    formatted_response += "\n\n[Aviso: Não foram encontradas justificativas explícitas para as alternativas incorretas]"

                return formatted_response
            else:
                # Se não conseguiu encontrar nenhum formato de alternativa, retorna a resposta original
                # mas adiciona um aviso
                return f"Resposta correta: Não identificada\n\n{response}\n\n[Aviso: Formato de resposta não reconhecido]"

    def get_available_providers(self):
        """
        Retorna a lista de provedores disponíveis.

        Returns:
            Lista de provedores disponíveis
        """
        return self.available_providers
