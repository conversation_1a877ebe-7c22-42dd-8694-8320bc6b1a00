"""
Detector especializado para questões dissertativas/subjetivas.
"""
import re
import cv2
import numpy as np
from typing import Dict, List, Optional, Tuple
from ..core.config import DEBUG


class EssayQuestionDetector:
    """
    Detector especializado para identificar questões dissertativas/subjetivas.
    """
    
    # Palavras-chave que indicam questões dissertativas
    ESSAY_KEYWORDS = {
        'commands': [
            'explique', 'descreva', 'justifique', 'analise', 'comente', 'redija',
            'elabore', 'discuta', 'argumente', 'desenvolva', 'compare', 'contraste',
            'avalie', 'critique', 'interprete', 'relacione', 'caracterize',
            'exemplifique', 'demonstre', 'fundamente', 'exponha', 'sintetize'
        ],
        'phrases': [
            'escreva sobre', 'escreva um texto', 'faça um texto', 'redigir',
            'sua opinião', 'seu ponto de vista', 'o que você pensa',
            'em sua opinião', 'segundo sua análise', 'de acordo com',
            'baseado em', 'fundamentado em', 'com base em',
            'dissertação', 'redação', 'ensaio', 'artigo', 'texto dissertativo'
        ],
        'question_starters': [
            'por que', 'como', 'de que forma', 'de que maneira',
            'qual a importância', 'qual o papel', 'quais são',
            'o que significa', 'qual o significado'
        ],
        'english': [
            'explain', 'describe', 'justify', 'analyze', 'comment', 'write',
            'elaborate', 'discuss', 'argue', 'develop', 'compare', 'contrast',
            'evaluate', 'critique', 'interpret', 'relate', 'characterize',
            'exemplify', 'demonstrate', 'write about', 'in your opinion'
        ]
    }
    
    # Padrões que indicam campos de texto livre
    TEXT_AREA_PATTERNS = [
        r'type\s+your\s+answer\s+here',
        r'digite\s+sua\s+resposta\s+aqui',
        r'escreva\s+aqui',
        r'resposta:?\s*$',
        r'answer:?\s*$',
        r'texto:?\s*$',
        r'redação:?\s*$',
        r'dissertação:?\s*$',
        r'target:?\s*\d+\s*words?',
        r'mínimo:?\s*\d+\s*palavras?',
        r'máximo:?\s*\d+\s*palavras?',
        r'\d+\s*-\s*\d+\s*palavras?',
        r'\d+\s*words?'
    ]
    
    # Padrões que indicam ausência de alternativas múltipla escolha
    NO_ALTERNATIVES_INDICATORS = [
        r'^(?!.*[A-E]\s*[\)\.])',  # Não contém A) B) C) D) E)
        r'^(?!.*[A-E]\s*-)',       # Não contém A- B- C- D- E-
        r'^(?!.*\([A-E]\))',       # Não contém (A) (B) (C) (D) (E)
    ]
    
    def __init__(self):
        """Inicializa o detector de questões dissertativas."""
        if DEBUG:
            print("EssayQuestionDetector inicializado")
    
    def detect_essay_question(self, text: str, image: Optional[np.ndarray] = None) -> Dict:
        """
        Detecta se uma questão é dissertativa/subjetiva.
        
        Args:
            text: Texto extraído da questão
            image: Imagem da questão (opcional)
            
        Returns:
            Dicionário com informações da detecção
        """
        try:
            result = {
                'is_essay': False,
                'confidence': 0.0,
                'indicators': [],
                'question_type': 'essay',
                'estimated_length': 'medium',
                'writing_style': 'academic',
                'has_text_area': False,
                'word_count_hint': None
            }
            
            if not text or len(text.strip()) < 10:
                return result
            
            text_lower = text.lower()
            
            # 1. Verifica palavras-chave de comando
            command_score = self._check_command_keywords(text_lower)
            if command_score > 0:
                result['indicators'].append(f"Palavras de comando detectadas (score: {command_score})")
                result['confidence'] += command_score * 0.3
            
            # 2. Verifica frases indicativas
            phrase_score = self._check_phrase_patterns(text_lower)
            if phrase_score > 0:
                result['indicators'].append(f"Frases dissertativas detectadas (score: {phrase_score})")
                result['confidence'] += phrase_score * 0.25
            
            # 3. Verifica ausência de alternativas múltipla escolha
            no_alternatives_score = self._check_no_alternatives(text)
            if no_alternatives_score > 0:
                result['indicators'].append("Ausência de alternativas múltipla escolha")
                result['confidence'] += no_alternatives_score * 0.2
            
            # 4. Verifica padrões de área de texto
            text_area_score = self._check_text_area_patterns(text_lower)
            if text_area_score > 0:
                result['indicators'].append("Padrões de área de texto detectados")
                result['confidence'] += text_area_score * 0.15
                result['has_text_area'] = True
            
            # 5. Análise visual (se imagem disponível)
            if image is not None:
                visual_score = self._analyze_visual_elements(image)
                if visual_score > 0:
                    result['indicators'].append("Elementos visuais de texto livre detectados")
                    result['confidence'] += visual_score * 0.1
                    result['has_text_area'] = True
            
            # 6. Verifica indicadores de contagem de palavras
            word_count = self._extract_word_count_hint(text)
            if word_count:
                result['word_count_hint'] = word_count
                result['indicators'].append(f"Indicação de contagem: {word_count}")
                result['confidence'] += 0.1
            
            # Determina se é questão dissertativa (threshold reduzido para melhor detecção)
            result['is_essay'] = result['confidence'] >= 0.15
            
            # Estima características da resposta
            if result['is_essay']:
                result['estimated_length'] = self._estimate_response_length(text, word_count)
                result['writing_style'] = self._determine_writing_style(text)
            
            # Limita confiança a 1.0
            result['confidence'] = min(1.0, result['confidence'])
            
            if DEBUG and result['is_essay']:
                print(f"Questão dissertativa detectada - Confiança: {result['confidence']:.2f}")
                print(f"Indicadores: {result['indicators']}")
            
            return result
            
        except Exception as e:
            if DEBUG:
                print(f"Erro na detecção de questão dissertativa: {e}")
            return {'is_essay': False, 'confidence': 0.0, 'indicators': [], 'question_type': 'essay'}
    
    def _check_command_keywords(self, text: str) -> float:
        """Verifica palavras-chave de comando."""
        score = 0.0
        
        # Verifica comandos em português
        for keyword in self.ESSAY_KEYWORDS['commands']:
            if keyword in text:
                score += 0.2
        
        # Verifica comandos em inglês
        for keyword in self.ESSAY_KEYWORDS['english']:
            if keyword in text:
                score += 0.15
        
        # Verifica iniciadores de questão
        for starter in self.ESSAY_KEYWORDS['question_starters']:
            if starter in text:
                score += 0.1
        
        return min(1.0, score)
    
    def _check_phrase_patterns(self, text: str) -> float:
        """Verifica padrões de frases dissertativas."""
        score = 0.0
        
        for phrase in self.ESSAY_KEYWORDS['phrases']:
            if phrase in text:
                score += 0.3
        
        return min(1.0, score)
    
    def _check_no_alternatives(self, text: str) -> float:
        """Verifica ausência de alternativas múltipla escolha."""
        # Verifica se há padrões de múltipla escolha
        mc_patterns = [
            r'[A-E]\s*[\)\.]',  # A) B) C) etc.
            r'[A-E]\s*-',       # A- B- C- etc.
            r'\([A-E]\)',       # (A) (B) (C) etc.
        ]
        
        for pattern in mc_patterns:
            if re.search(pattern, text):
                return 0.0  # Tem alternativas, não é dissertativa
        
        # Se não tem alternativas e tem texto suficiente, pode ser dissertativa
        if len(text.strip()) > 50:
            return 0.5
        
        return 0.0
    
    def _check_text_area_patterns(self, text: str) -> float:
        """Verifica padrões que indicam área de texto livre."""
        score = 0.0
        
        for pattern in self.TEXT_AREA_PATTERNS:
            if re.search(pattern, text, re.IGNORECASE):
                score += 0.4
        
        return min(1.0, score)
    
    def _analyze_visual_elements(self, image: np.ndarray) -> float:
        """Analisa elementos visuais que indicam área de texto."""
        try:
            # Converte para escala de cinza
            gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
            
            # Detecta retângulos grandes (possíveis áreas de texto)
            edges = cv2.Canny(gray, 50, 150)
            contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            large_rectangles = 0
            for contour in contours:
                area = cv2.contourArea(contour)
                if area > 5000:  # Área grande
                    # Verifica se é retangular
                    approx = cv2.approxPolyDP(contour, 0.02 * cv2.arcLength(contour, True), True)
                    if len(approx) == 4:
                        x, y, w, h = cv2.boundingRect(contour)
                        aspect_ratio = w / h
                        if 1.5 < aspect_ratio < 10:  # Retângulo horizontal típico de área de texto
                            large_rectangles += 1
            
            # Se há retângulos grandes, pode ser área de texto
            if large_rectangles > 0:
                return min(1.0, large_rectangles * 0.3)
            
            return 0.0
            
        except Exception as e:
            if DEBUG:
                print(f"Erro na análise visual: {e}")
            return 0.0
    
    def _extract_word_count_hint(self, text: str) -> Optional[Dict]:
        """Extrai indicações de contagem de palavras."""
        patterns = [
            r'target:?\s*(\d+)\s*words?',
            r'mínimo:?\s*(\d+)\s*palavras?',
            r'máximo:?\s*(\d+)\s*palavras?',
            r'(\d+)\s*-\s*(\d+)\s*palavras?',
            r'(\d+)\s*words?'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                if len(match.groups()) == 2:  # Range
                    return {
                        'type': 'range',
                        'min': int(match.group(1)),
                        'max': int(match.group(2))
                    }
                else:  # Single number
                    return {
                        'type': 'target',
                        'target': int(match.group(1))
                    }
        
        return None
    
    def _estimate_response_length(self, text: str, word_count: Optional[Dict]) -> str:
        """Estima o tamanho da resposta esperada."""
        if word_count:
            if word_count['type'] == 'target':
                target = word_count['target']
            else:  # range
                target = (word_count['min'] + word_count['max']) // 2
            
            if target < 100:
                return 'short'
            elif target < 300:
                return 'medium'
            else:
                return 'long'
        
        # Estima baseado no tamanho da questão
        text_length = len(text.split())
        if text_length < 20:
            return 'short'
        elif text_length < 50:
            return 'medium'
        else:
            return 'long'
    
    def _determine_writing_style(self, text: str) -> str:
        """Determina o estilo de escrita apropriado."""
        text_lower = text.lower()
        
        # Indicadores de estilo acadêmico
        academic_indicators = [
            'analise', 'fundamente', 'argumente', 'critique', 'avalie',
            'baseado em', 'de acordo com', 'segundo', 'teoria', 'conceito'
        ]
        
        # Indicadores de estilo técnico
        technical_indicators = [
            'procedimento', 'processo', 'método', 'técnica', 'algoritmo',
            'implementação', 'sistema', 'função', 'operação'
        ]
        
        # Indicadores de estilo formal
        formal_indicators = [
            'justifique', 'explique', 'descreva', 'elabore', 'demonstre'
        ]
        
        academic_score = sum(1 for indicator in academic_indicators if indicator in text_lower)
        technical_score = sum(1 for indicator in technical_indicators if indicator in text_lower)
        formal_score = sum(1 for indicator in formal_indicators if indicator in text_lower)
        
        if academic_score >= technical_score and academic_score >= formal_score:
            return 'academic'
        elif technical_score >= formal_score:
            return 'technical'
        else:
            return 'formal'
