"""
Módulo para marcar automaticamente as questões.
"""
import re
import time
import pyautogui
import cv2
import numpy as np
from config import DEBUG

class AutoMarker:
    def __init__(self):
        """Inicializa o marcador automático."""
        self.enabled = False
        self.window_handle = None
        self.last_answer = None
        self.last_answer_letter = None
        
    def enable(self, window_handle=None):
        """Ativa o marcador automático."""
        self.enabled = True
        if window_handle:
            self.window_handle = window_handle
        return self.enabled
        
    def disable(self):
        """Desativa o marcador automático."""
        self.enabled = False
        return self.enabled
        
    def is_enabled(self):
        """Verifica se o marcador automático está ativado."""
        return self.enabled
        
    def set_window_handle(self, window_handle):
        """Define o handle da janela."""
        self.window_handle = window_handle
        
    def extract_answer_letter(self, text):
        """
        Extrai a letra da resposta correta do texto.
        
        Args:
            text: Texto da resposta
            
        Returns:
            Letra da resposta (A, B, C, D, E) ou None se não encontrada
        """
        if not text:
            return None
            
        # Procura pelo padrão "Resposta correta: X)"
        match = re.search(r"Resposta correta:\s*([A-Ea-e])\)", text)
        if match:
            return match.group(1).upper()
            
        # Procura pelo padrão "Resposta correta: X."
        match = re.search(r"Resposta correta:\s*([A-Ea-e])\.", text)
        if match:
            return match.group(1).upper()
            
        # Procura pelo padrão "Resposta: X)"
        match = re.search(r"Resposta:\s*([A-Ea-e])\)", text)
        if match:
            return match.group(1).upper()
            
        # Procura pelo padrão "Alternativa X"
        match = re.search(r"Alternativa\s*([A-Ea-e])", text)
        if match:
            return match.group(1).upper()
            
        # Procura pelo padrão "A resposta é X"
        match = re.search(r"A resposta é\s*([A-Ea-e])", text)
        if match:
            return match.group(1).upper()
            
        # Procura por qualquer letra isolada entre parênteses
        match = re.search(r"\(([A-Ea-e])\)", text)
        if match:
            return match.group(1).upper()
            
        return None
        
    def mark_answer(self, text):
        """
        Marca a resposta correta na questão.
        
        Args:
            text: Texto da resposta
            
        Returns:
            True se a resposta foi marcada com sucesso, False caso contrário
        """
        if not self.enabled or not self.window_handle:
            if DEBUG:
                print("Marcador automático desativado ou sem janela definida")
            return False
            
        # Extrai a letra da resposta
        answer_letter = self.extract_answer_letter(text)
        if not answer_letter:
            if DEBUG:
                print("Não foi possível extrair a letra da resposta")
            return False
            
        # Salva a última resposta
        self.last_answer = text
        self.last_answer_letter = answer_letter
        
        if DEBUG:
            print(f"Letra da resposta: {answer_letter}")
            
        # Tenta localizar e clicar na alternativa
        try:
            # Ativa a janela
            pyautogui.click(x=self.window_handle.left + 100, y=self.window_handle.top + 100)
            time.sleep(0.5)
            
            # Procura por padrões comuns de alternativas
            patterns = [
                f"{answer_letter})",  # A)
                f"{answer_letter}.",  # A.
                f"({answer_letter})",  # (A)
                f"[ {answer_letter} ]",  # [ A ]
                f"[{answer_letter}]",  # [A]
            ]
            
            # Captura a tela
            screenshot = pyautogui.screenshot()
            screenshot = np.array(screenshot)
            screenshot = cv2.cvtColor(screenshot, cv2.COLOR_RGB2BGR)
            
            # Converte para escala de cinza para OCR
            gray = cv2.cvtColor(screenshot, cv2.COLOR_BGR2GRAY)
            
            # Aplica threshold para melhorar o reconhecimento
            _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
            
            # Usa OCR para localizar o texto das alternativas
            # (Esta parte seria implementada com Tesseract, mas para simplificar,
            # vamos usar uma abordagem baseada em cliques em posições relativas)
            
            # Clica na alternativa com base na letra
            # Assumindo que as alternativas estão em ordem (A, B, C, D, E)
            # e que estão dispostas verticalmente
            base_y = self.window_handle.top + 300  # Posição Y base (ajustar conforme necessário)
            spacing = 50  # Espaçamento entre alternativas (ajustar conforme necessário)
            
            # Mapeamento de letras para índices (0-based)
            letter_to_index = {
                'A': 0,
                'B': 1,
                'C': 2,
                'D': 3,
                'E': 4
            }
            
            if answer_letter in letter_to_index:
                index = letter_to_index[answer_letter]
                click_y = base_y + (index * spacing)
                click_x = self.window_handle.left + 50  # Posição X (ajustar conforme necessário)
                
                # Clica na alternativa
                pyautogui.click(x=click_x, y=click_y)
                time.sleep(0.5)
                
                if DEBUG:
                    print(f"Clicou na alternativa {answer_letter} em ({click_x}, {click_y})")
                
                # Procura e clica no botão "Próximo" ou similar
                self.click_next_button()
                
                return True
            else:
                if DEBUG:
                    print(f"Letra da resposta não reconhecida: {answer_letter}")
                return False
                
        except Exception as e:
            if DEBUG:
                print(f"Erro ao marcar resposta: {str(e)}")
            return False
            
    def click_next_button(self):
        """
        Procura e clica no botão "Próximo" ou similar.
        
        Returns:
            True se o botão foi clicado com sucesso, False caso contrário
        """
        try:
            # Tenta localizar o botão "Próximo" ou similar
            # Estratégia 1: Procura por botões na parte inferior direita da tela
            window_width = self.window_handle.width
            window_height = self.window_handle.height
            
            # Posições comuns para botões "Próximo"
            positions = [
                # Botão inferior direito
                (self.window_handle.left + window_width - 100, self.window_handle.top + window_height - 50),
                # Botão inferior centro
                (self.window_handle.left + (window_width // 2), self.window_handle.top + window_height - 50),
                # Seta para a direita (canto inferior direito)
                (self.window_handle.left + window_width - 50, self.window_handle.top + (window_height // 2)),
            ]
            
            # Tenta clicar em cada posição
            for pos_x, pos_y in positions:
                pyautogui.click(x=pos_x, y=pos_y)
                time.sleep(0.5)
                
                if DEBUG:
                    print(f"Tentou clicar em 'Próximo' em ({pos_x}, {pos_y})")
                
                # Verifica se houve mudança na tela (implementação simplificada)
                # Em uma implementação completa, verificaríamos se a tela mudou
                
            return True
            
        except Exception as e:
            if DEBUG:
                print(f"Erro ao clicar no botão 'Próximo': {str(e)}")
            return False
            
# Para testes
if __name__ == "__main__":
    marker = AutoMarker()
    marker.enable()
    
    # Testa a extração de letras
    test_texts = [
        "Resposta correta: A) Alternativa A",
        "Resposta correta: B) Esta é a alternativa B",
        "A resposta é C porque...",
        "Alternativa D é a correta",
        "A resposta correta é (E)"
    ]
    
    for text in test_texts:
        letter = marker.extract_answer_letter(text)
        print(f"Texto: {text}")
        print(f"Letra extraída: {letter}")
        print()
