"""
Module for creating a transparent overlay UI.
"""
import sys
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                            QTextEdit, QPushButton, QLabel, QHBoxLayout)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QObject
import pyautogui
from config import (OVERLAY_OPACITY, OVERLAY_WIDTH, OVERLAY_HEIGHT,
                   OVERLAY_POSITION_X, OVERLAY_POSITION_Y,
                   ADS_ENABLED, ADS_CLIENT_ID)

# Importa o gerenciador de anúncios apenas se os anúncios estiverem habilitados
if ADS_ENABLED:
    try:
        # Importa o gerenciador de anúncios baseado em WebEngine para AdSense
        from ad_manager import AdManager
        ads_available = True
        print("Usando gerenciador de anúncios baseado em WebEngine para AdSense")
    except ImportError:
        print("Aviso: Módulo PyQtWebEngine não encontrado. Anúncios desabilitados.")
        ads_available = False
else:
    ads_available = False

class SignalEmitter(QObject):
    """Class to emit signals for thread-safe UI updates."""
    update_result = pyqtSignal(str)
    update_status = pyqtSignal(str)

class OverlayUI(QMainWindow):
    def __init__(self, ads_enabled=True, ads_client_id=None):
        super().__init__()

        # Configurações de anúncios
        self.ads_enabled = ads_enabled and ads_available
        self.ads_client_id = ads_client_id or ADS_CLIENT_ID

        # Create signal emitter
        self.signals = SignalEmitter()
        self.signals.update_result.connect(self.set_result_text)
        self.signals.update_status.connect(self.set_status_text)

        # Set window properties
        self.setWindowTitle("Python Question Helper - Desenvolvido por BoZolinO")
        self.setWindowFlags(Qt.WindowStaysOnTopHint | Qt.FramelessWindowHint | Qt.Tool)
        self.setAttribute(Qt.WA_TranslucentBackground)
        self.setWindowOpacity(OVERLAY_OPACITY)

        # Set window size and position
        screen_size = pyautogui.size()
        self.setGeometry(
            screen_size[0] - OVERLAY_WIDTH - OVERLAY_POSITION_X,
            OVERLAY_POSITION_Y,
            OVERLAY_WIDTH,
            OVERLAY_HEIGHT
        )

        # Create central widget
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)

        # Create layout
        self.layout = QVBoxLayout(self.central_widget)
        self.layout.setContentsMargins(10, 10, 10, 10)

        # Create header
        self.header_layout = QHBoxLayout()
        self.title_label = QLabel("Python Question Helper - Desenvolvido por BoZolinO")
        self.title_label.setStyleSheet("color: white; font-weight: bold; font-size: 12pt;")
        self.header_layout.addWidget(self.title_label)

        # Create close button
        self.close_button = QPushButton("×")
        self.close_button.setFixedSize(20, 20)
        self.close_button.setStyleSheet("background-color: red; color: white; border-radius: 10px;")
        self.close_button.clicked.connect(self.close)
        self.header_layout.addWidget(self.close_button)

        self.layout.addLayout(self.header_layout)

        # Create status label
        self.status_label = QLabel("Ready")
        self.status_label.setStyleSheet("color: white;")
        self.layout.addWidget(self.status_label)

        # Create result text area with improved styling
        self.result_text = QTextEdit()
        self.result_text.setReadOnly(True)
        self.result_text.setStyleSheet("""
            background-color: rgba(30, 30, 30, 200);
            color: white;
            border: 1px solid gray;
            border-radius: 5px;
            font-size: 12pt;
            line-height: 1.4;
            padding: 10px;
        """)
        # Set a better font for reading
        font = self.result_text.font()
        font.setPointSize(11)
        self.result_text.setFont(font)

        self.layout.addWidget(self.result_text)

        # Create a container for buttons
        self.buttons_layout = QHBoxLayout()

        # Add empty layout for buttons
        self.layout.addLayout(self.buttons_layout)

        # Adiciona espaço para anúncios
        if self.ads_enabled and ads_available:
            self.setup_ads()

        # Set background color
        self.central_widget.setStyleSheet("""
            background-color: rgba(40, 40, 40, 200);
            border-radius: 10px;
            border: 1px solid gray;
        """)

        # Make the window draggable
        self.draggable = False
        self.offset = None

    def mousePressEvent(self, event):
        if event.button() == Qt.LeftButton:
            self.draggable = True
            self.offset = event.pos()

    def mouseMoveEvent(self, event):
        if self.draggable and self.offset:
            self.move(self.pos() + event.pos() - self.offset)

    def mouseReleaseEvent(self, event):
        if event.button() == Qt.LeftButton:
            self.draggable = False

    def closeEvent(self, event):
        """Handle the window close event (X button)."""
        # Ensure application exits completely when window is closed
        QApplication.quit()
        # Accept the close event
        event.accept()

    def setup_ads(self):
        """Configura os anúncios na interface de forma discreta."""
        # Cria um separador sutil para os anúncios
        ad_separator = QLabel()
        ad_separator.setStyleSheet("border-top: 1px solid rgba(150, 150, 150, 50); margin-top: 5px; margin-bottom: 5px;")
        ad_separator.setFixedHeight(1)
        self.layout.addWidget(ad_separator)

        # Cria o gerenciador de anúncios baseado em WebEngine para AdSense
        ad_manager = AdManager(ad_client=self.ads_client_id)
        print("Configurando anúncios com o gerenciador baseado em WebEngine para AdSense")

        # Cria um anúncio multiplex (formato mais rico em conteúdo)
        ad_banner = ad_manager.create_banner(self, ad_type="multiplex")

        # Adiciona o banner à interface de forma discreta
        ad_container = QHBoxLayout()
        ad_container.addStretch()
        ad_container.addWidget(ad_banner)
        ad_container.addStretch()

        # Remove completamente as margens para uma aparência mais integrada
        ad_container.setContentsMargins(0, 0, 0, 0)

        # Adiciona o container à interface, mas com peso menor para não dominar o layout
        self.layout.addLayout(ad_container, 0)  # Peso 0 para minimizar o espaço ocupado

    def set_result_text(self, text):
        """Set the result text in a thread-safe way with simple bold highlighting."""
        try:
            # Verifica se o texto contém o formato de resposta correta
            if "Resposta correta:" in text:
                # Divide o texto em partes: antes, durante e depois da resposta correta
                parts = text.split("Resposta correta:", 1)
                before_text = parts[0]
                after_text = parts[1]

                # Encontra o final da resposta (primeiro ponto ou quebra de linha após o parêntese)
                end_markers = [". ", ".\n", "\n"]
                end_pos = len(after_text)

                for marker in end_markers:
                    pos = after_text.find(marker)
                    if pos > 0 and pos < end_pos:
                        end_pos = pos + len(marker) - 1

                # Separa a resposta do resto do texto
                answer_text = after_text[:end_pos]
                remaining_text = after_text[end_pos:]

                # Cria um cursor para manipular o texto
                cursor = self.result_text.textCursor()
                self.result_text.clear()

                # Adiciona o texto antes da resposta
                cursor.insertText(before_text)

                # Adiciona a resposta em negrito
                cursor.insertText("Resposta correta:")

                # Configura o formato para negrito
                format = cursor.charFormat()
                format.setFontWeight(700)  # Bold
                format.setFontUnderline(True)  # Underline
                cursor.setCharFormat(format)

                # Insere o texto da resposta em negrito
                cursor.insertText(answer_text)

                # Volta ao formato normal
                format.setFontWeight(400)  # Normal
                format.setFontUnderline(False)  # No underline
                cursor.setCharFormat(format)

                # Adiciona o resto do texto
                cursor.insertText(remaining_text)

                # Aplica o cursor
                self.result_text.setTextCursor(cursor)

                # Rola para o topo
                self.result_text.verticalScrollBar().setValue(0)
            else:
                # Se não encontrar o padrão, exibe o texto normal
                self.result_text.setText(text)
        except Exception as e:
            # Em caso de erro, volta para o comportamento padrão
            print(f"Erro ao formatar resposta: {str(e)}")
            self.result_text.setText(text)

    def set_status_text(self, text):
        """Set the status text in a thread-safe way."""
        self.status_label.setText(text)

    def update_result(self, text):
        """Update the result text from any thread with a simplified fade effect."""
        # Verifica se o texto está vazio
        if not text:
            return

        # Emite o sinal diretamente para garantir que o texto seja atualizado
        self.signals.update_result.emit(text)

        # Usa uma abordagem mais segura para o efeito visual
        # Aplica o efeito diretamente no thread principal
        self.signals.update_result.connect(lambda: self._apply_highlight_effect())

    def _apply_highlight_effect(self):
        """Aplica o efeito de destaque no thread principal."""
        # Aplica um efeito de destaque temporário
        self.result_text.setStyleSheet("""
            background-color: rgba(40, 40, 40, 200);  /* Fundo ligeiramente mais claro */
            color: rgba(255, 255, 255, 1.0);  /* Texto branco */
            border: 1px solid #4a90e2;  /* Borda azul para destacar */
            border-radius: 5px;
            font-size: 12pt;
            line-height: 1.4;
            padding: 10px;
        """)

        # Cria um timer no thread principal
        self.highlight_timer = QTimer()
        self.highlight_timer.setSingleShot(True)
        self.highlight_timer.timeout.connect(self._restore_normal_style)
        self.highlight_timer.start(300)

    def _restore_normal_style(self):
        """Restaura o estilo normal após o efeito de destaque."""
        self.result_text.setStyleSheet("""
            background-color: rgba(30, 30, 30, 200);
            color: white;
            border: 1px solid gray;
            border-radius: 5px;
            font-size: 12pt;
            line-height: 1.4;
            padding: 10px;
        """)

    def update_status(self, text):
        """Update the status text from any thread."""
        self.signals.update_status.emit(text)

    def create_capture_buttons(self, full_screen_callback, area_callback):
        """Create buttons for different capture methods."""
        # Full screen button
        self.full_screen_button = QPushButton("Capture Full Screen")
        self.full_screen_button.setStyleSheet("""
            background-color: rgba(0, 120, 215, 200);
            color: white;
            border-radius: 5px;
            padding: 5px;
        """)
        self.full_screen_button.clicked.connect(full_screen_callback)

        # Area selection button
        self.area_button = QPushButton("Capture Screen Area")
        self.area_button.setStyleSheet("""
            background-color: rgba(0, 120, 215, 200);
            color: white;
            border-radius: 5px;
            padding: 5px;
        """)
        self.area_button.clicked.connect(area_callback)

        # Add buttons to layout
        self.buttons_layout.addWidget(self.full_screen_button)
        self.buttons_layout.addWidget(self.area_button)

    def create_single_capture_button(self, capture_callback):
        """Add window capture option to dropdown menu."""
        # Add option to dropdown
        self.action_menu.addItem("Capturar Janela")

        # Store callback
        self._capture_callback = capture_callback

        # Connect dropdown to handler if not already connected
        if not hasattr(self, '_dropdown_connected'):
            self.action_menu.currentIndexChanged.connect(self._handle_dropdown_selection)
            self._dropdown_connected = True

    def create_select_area_button(self, select_area_callback):
        """Add area selection option to dropdown menu."""
        # Add option to dropdown
        self.action_menu.addItem("Selecionar Área")

        # Store callback
        self._select_area_callback = select_area_callback

    def create_direct_vision_button(self, direct_vision_callback):
        """Add direct vision option to dropdown menu."""
        # Add option to dropdown
        self.action_menu.addItem("Visão Direta")

        # Store callback
        self._direct_vision_callback = direct_vision_callback

    def create_model_selector_button(self, model_selector_callback):
        """Add model selector button for direct access."""
        # Create a separate model selector button
        self.model_selector_button = QPushButton("Selecionar Modelo")
        self.model_selector_button.setStyleSheet("""
            background-color: rgba(100, 100, 200, 200);
            color: white;
            border-radius: 5px;
            padding: 8px;
            font-size: 14px;
            font-weight: bold;
        """)
        self.model_selector_button.setToolTip("Selecionar Modelo LLM")
        self.model_selector_button.clicked.connect(model_selector_callback)

        # Add button to layout
        self.buttons_layout.addWidget(self.model_selector_button)

    def create_auto_monitor_button(self, monitor_callback, test_callback=None):
        """Add monitoring options to dropdown and create a separate monitor button."""
        # Create a separate monitor button (this one we'll keep as a button for quick access)
        self.monitor_button = QPushButton("Iniciar Monitoramento")
        self.monitor_button.setStyleSheet("""
            background-color: rgba(0, 180, 100, 200);
            color: white;
            border-radius: 5px;
            padding: 8px;
            font-size: 14px;
            font-weight: bold;
            min-width: 200px;
        """)
        self.monitor_button.clicked.connect(monitor_callback)

        # Add button to layout
        self.buttons_layout.addWidget(self.monitor_button)

        # Add test option to dropdown if provided
        if test_callback:
            self.action_menu.addItem("Testar Detecção")
            self._test_callback = test_callback

    def update_auto_monitor_button_text(self, text):
        """Update the text of the auto monitor button."""
        if hasattr(self, 'monitor_button') and self.monitor_button:
            self.monitor_button.setText(text)

            # Also update the color based on state
            if text == "Iniciar Monitoramento":
                self.monitor_button.setStyleSheet("""
                    background-color: rgba(0, 180, 100, 200);
                    color: white;
                    border-radius: 5px;
                    padding: 8px;
                    font-size: 14px;
                    font-weight: bold;
                """)
            else:
                self.monitor_button.setStyleSheet("""
                    background-color: rgba(215, 50, 50, 200);
                    color: white;
                    border-radius: 5px;
                    padding: 8px;
                    font-size: 14px;
                    font-weight: bold;
                """)

    def disable_capture_buttons(self):
        """Disable all capture buttons."""
        # Disable the dropdown menu
        if hasattr(self, 'action_menu'):
            self.action_menu.setEnabled(False)

        # Don't disable the monitor button to allow toggling monitoring even during processing

    def enable_capture_buttons(self):
        """Enable all capture buttons."""
        # Enable the dropdown menu
        if hasattr(self, 'action_menu'):
            self.action_menu.setEnabled(True)

        # Enable the monitor button
        if hasattr(self, 'monitor_button'):
            self.monitor_button.setEnabled(True)

    # Método _handle_dropdown_selection removido pois não é mais necessário

# For testing
if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = OverlayUI()
    window.show()
    window.update_result("This is a test result.\nIt appears in the overlay window.")
    window.update_status("Testing overlay...")
    sys.exit(app.exec_())
