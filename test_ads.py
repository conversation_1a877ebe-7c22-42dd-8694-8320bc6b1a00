"""
Test script to troubleshoot the Google AdSense integration
"""
import sys
import traceback
from PyQt5.QtWidgets import <PERSON>App<PERSON>, Q<PERSON>ainWindow, QVBoxLayout, QWidget, QLabel
from PyQt5.QtCore import Qt

# Test if PyQtWebEngine is installed
print("Checking for PyQtWebEngineWidgets...")
try:
    from PyQt5.QtWebEngineWidgets import QWebEngineView, QWebEnginePage, QWebEngineProfile, QWebEngineSettings
    print("PyQtWebEngineWidgets is available")
except ImportError as e:
    print(f"ERROR: PyQtWebEngineWidgets is NOT available: {e}")
    print("This is required for Google AdSense functionality")
    print("Try installing it with: pip install PyQtWebEngineWidgets")
    sys.exit(1)

# Import config and AdManager
print("\nImporting configuration...")
try:
    from config import DEBUG, ADS_CLIENT_ID, ADS_BANNER_SLOT, ADS_RECTANGLE_SLOT, ADS_MULTIPLEX_SLOT
    print(f"Configuration loaded successfully:")
    print(f"- DEBUG: {DEBUG}")
    print(f"- ADS_CLIENT_ID: {ADS_CLIENT_ID}")
    print(f"- ADS_BANNER_SLOT: {ADS_BANNER_SLOT}")
    print(f"- ADS_RECTANGLE_SLOT: {ADS_RECTANGLE_SLOT}")
except ImportError as e:
    print(f"ERROR: Could not import configuration: {e}")
    sys.exit(1)

print("\nImporting AdManager...")
try:
    from ad_manager import AdManager, AdBanner
    print("AdManager imported successfully")
except ImportError as e:
    print(f"ERROR: Could not import AdManager: {e}")
    sys.exit(1)

class TestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        
        self.setWindowTitle("AdSense Test Window")
        self.setGeometry(100, 100, 800, 600)
        
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Create layout
        layout = QVBoxLayout(central_widget)
        
        # Add a label
        label = QLabel("Testing Google AdSense Integration")
        label.setAlignment(Qt.AlignCenter)
        layout.addWidget(label)
        
        print("\nCreating AdManager instance...")
        try:
            ad_manager = AdManager(ad_client=ADS_CLIENT_ID)
            print("AdManager instance created successfully")
            
            print("\nTesting different ad types:")
            ad_types = ["banner", "rectangle", "multiplex"]
            
            for ad_type in ad_types:
                print(f"\nCreating {ad_type} ad...")
                try:
                    ad_widget = ad_manager.create_banner(self, ad_type=ad_type)
                    print(f"{ad_type} ad created successfully")
                    layout.addWidget(ad_widget)
                    
                    # Check if the WebView was created properly
                    if hasattr(ad_widget, 'web_view'):
                        print(f"- WebView created for {ad_type} ad")
                        print(f"- WebView size: {ad_widget.web_view.width()}x{ad_widget.web_view.height()}")
                        print(f"- WebView visible: {ad_widget.web_view.isVisible()}")
                    else:
                        print(f"- WARNING: WebView not created for {ad_type} ad, using debug placeholder instead")
                        
                except Exception as e:
                    print(f"ERROR: Failed to create {ad_type} ad: {e}")
                    traceback.print_exc()
        except Exception as e:
            print(f"ERROR: Failed to create AdManager: {e}")
            traceback.print_exc()

def main():
    app = QApplication(sys.argv)
    
    print("Starting AdSense test application...")
    window = TestWindow()
    window.show()
    
    print("\nTest window is now visible. Check for errors in the output above.")
    print("The window should display test ads if everything is working correctly.")
    print("Close the window to exit the test.")
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
