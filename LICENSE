PYTHON QUESTION HELPER - COMMERCIAL LICENSE

Copyright (c) 2025 BoZolinO. All rights reserved.

COMMERCIAL SOFTWARE LICENSE AGREEMENT

This Commercial Software License Agreement ("Agreement") is entered into between
BoZolinO ("Licensor") and the individual or entity using this software ("Licensee").

1. GRANT OF LICENSE

Subject to the terms and conditions of this Agreement, Licensor hereby grants to
Licensee a non-exclusive, non-transferable license to use the Python Question Helper
software ("Software") for commercial purposes.

2. PERMITTED USES

Licensee may:

- Use the Software for personal, educational, or commercial purposes
- Install the Software on multiple devices owned or controlled by Licensee
- Create backups of the Software for archival purposes
- Modify configuration files for customization

3. RESTRICTIONS

Licensee may NOT:

- Redistribute, sell, lease, or sublicense the Software
- Reverse engineer, decompile, or disassemble the Software
- Remove or modify any copyright notices or license information
- Use the Software to create competing products
- Share API keys or access credentials with third parties

4. INTELLECTUAL PROPERTY

The Software and all intellectual property rights therein are and shall remain
the exclusive property of Licensor. This Agreement does not grant Licensee any
rights to trademarks, service marks, or trade names of Licensor.

5. THIRD-PARTY COMPONENTS

The Software may include third-party components subject to separate license terms:

- OpenAI API: Subject to OpenAI Terms of Service
- Google Gemini API: Subject to Google Cloud Terms of Service
- Hugging Face API: Subject to Hugging Face Terms of Service
- Tesseract OCR: Apache License 2.0
- PyQt5: GPL v3 (Commercial license available separately)
- OpenCV: Apache License 2.0
- Other dependencies: See requirements.txt for individual licenses

6. MONETIZATION

The Software includes advertising functionality powered by Google AdSense.
By using the Software, Licensee acknowledges that:

- Advertisements may be displayed during normal operation
- Ad revenue is retained by Licensor
- Licensee may not block, modify, or interfere with advertisement display

7. DATA COLLECTION AND PRIVACY

The Software may collect anonymous usage statistics for improvement purposes:

- Question processing metrics (without content)
- Error logs and performance data
- Feature usage analytics
- No personal information or question content is transmitted

8. WARRANTY DISCLAIMER

THE SOFTWARE IS PROVIDED "AS IS" WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.

9. LIMITATION OF LIABILITY

IN NO EVENT SHALL LICENSOR BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR
IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

10. TERMINATION

This Agreement is effective until terminated. Licensee may terminate this
Agreement at any time by destroying all copies of the Software. This Agreement
will terminate immediately without notice if Licensee fails to comply with any
provision of this Agreement.

11. UPDATES AND SUPPORT

Licensor may, but is not obligated to, provide updates, upgrades, or support
for the Software. Any updates provided shall be subject to this Agreement
unless accompanied by a separate license agreement.

12. GOVERNING LAW

This Agreement shall be governed by and construed in accordance with the laws
of Brazil, without regard to its conflict of law provisions.

13. ENTIRE AGREEMENT

This Agreement constitutes the entire agreement between the parties and
supersedes all prior or contemporaneous understandings regarding such subject matter.

14. CONTACT INFORMATION

For licensing inquiries, support, or other questions:

- Email: <EMAIL>
- GitHub: https://github.com/hiagodrigo/Python_Question_Helper
- Website: [To be determined]

By using the Software, Licensee acknowledges that they have read this Agreement,
understand it, and agree to be bound by its terms and conditions.

---

ACKNOWLEDGMENTS

This software uses the following open-source libraries and services:

- Tesseract OCR by Google (Apache License 2.0)
- OpenCV (Apache License 2.0)
- NumPy (BSD License)
- Pillow (PIL License)
- Requests (Apache License 2.0)
- Python-dotenv (BSD License)

Special thanks to the open-source community for making this project possible.

---

Python Question Helper v1.0.0
Copyright (c) 2025 BoZolinO
All rights reserved.
