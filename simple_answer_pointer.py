"""
<PERSON><PERSON><PERSON><PERSON> para criar um apontador visual simplificado que destaca a resposta correta na tela.
Esta versão é mais robusta e menos propensa a crashes.
"""
import re
import numpy as np
from PyQt5.QtWidgets import QWidget, <PERSON><PERSON>abel
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QColor, QPalette, QFont
from config import DEBUG

class SimpleAnswerPointer:
    """
    Classe para criar um apontador visual simplificado que destaca a resposta correta na tela.
    """
    def __init__(self):
        """
        Inicializa o apontador visual.
        """
        self.is_active = False
        self.overlay = None
        self.answer_letter = None
        
    def extract_answer_letter(self, answer_text):
        """
        Extrai a letra da resposta correta do texto da LLM.
        
        Args:
            answer_text: Texto da resposta da LLM
            
        Returns:
            Letra da resposta correta ou None se não encontrada
        """
        if not answer_text:
            return None
            
        # Imprime o texto da resposta para debug
        if DEBUG:
            print(f"Texto da resposta: {answer_text[:100]}...")
            
        # Padrões para encontrar a letra da resposta
        patterns = [
            r'Resposta correta:\s*([A-Ea-e])\)',  # Resposta correta: A)
            r'Resposta correta:\s*\(([A-Ea-e])\)',  # Resposta correta: (A)
            r'Resposta correta:\s*([A-Ea-e])[\.:\)]',  # Resposta correta: A. ou A: ou A)
            r'Resposta correta:\s*([A-Ea-e])',  # Resposta correta: A (sem pontuação)
            r'Resposta:\s*([A-Ea-e])[\.:\)]',  # Resposta: A. ou A: ou A)
            r'Alternativa\s*([A-Ea-e])[\.:\)]',  # Alternativa A. ou A: ou A)
            r'letra\s*([A-Ea-e])[\.:\)]',  # letra A. ou A: ou A)
            r'opção\s*([A-Ea-e])[\.:\)]',  # opção A. ou A: ou A)
            r'[^\w]([A-Ea-e])\)',  # Qualquer ocorrência de A) isolada
            r'correta[:\s]+([A-Ea-e])',  # correta: A ou correta A
            r'resposta[:\s]+([A-Ea-e])',  # resposta: A ou resposta A
        ]
        
        for pattern in patterns:
            matches = re.search(pattern, answer_text, re.IGNORECASE)
            if matches:
                letter = matches.group(1).upper()
                if DEBUG:
                    print(f"Letra da resposta encontrada: {letter}")
                return letter
                
        # Se não encontrou com os padrões específicos, procura por qualquer letra isolada
        # que pareça ser uma alternativa
        words = answer_text.split()
        for word in words[:20]:  # Verifica mais palavras
            if re.match(r'^[A-Ea-e][\.:\)]$', word):
                letter = word[0].upper()
                if DEBUG:
                    print(f"Letra da resposta encontrada (formato simples): {letter}")
                return letter
                
        # Se ainda não encontrou, tenta um método mais agressivo
        # Procura por qualquer letra maiúscula isolada que possa ser uma alternativa
        for letter in ['A', 'B', 'C', 'D', 'E']:
            if f" {letter} " in answer_text or f" {letter}." in answer_text or f" {letter})" in answer_text:
                if DEBUG:
                    print(f"Letra da resposta encontrada (método agressivo): {letter}")
                return letter
                
        # Se tudo falhar, assume a primeira letra que aparece após "Resposta correta"
        if "Resposta correta" in answer_text:
            pos = answer_text.find("Resposta correta")
            for i in range(pos + 16, min(pos + 30, len(answer_text))):
                if answer_text[i].upper() in ['A', 'B', 'C', 'D', 'E']:
                    letter = answer_text[i].upper()
                    if DEBUG:
                        print(f"Letra da resposta encontrada (último recurso): {letter}")
                    return letter
                
        if DEBUG:
            print("Nenhuma letra de resposta encontrada")
        return None
        
    def show_pointer(self, answer_text):
        """
        Mostra o apontador visual para a resposta correta.
        
        Args:
            answer_text: Texto da resposta da LLM
            
        Returns:
            True se o apontador foi mostrado com sucesso, False caso contrário
        """
        try:
            # Extrai a letra da resposta
            self.answer_letter = self.extract_answer_letter(answer_text)
            if not self.answer_letter:
                if DEBUG:
                    print("Não foi possível extrair a letra da resposta")
                return False
                
            # Cria e mostra o overlay
            self.create_overlay()
            
            return True
            
        except Exception as e:
            if DEBUG:
                print(f"Erro ao mostrar apontador: {str(e)}")
            return False
            
    def create_overlay(self):
        """
        Cria um overlay para destacar a resposta correta.
        """
        try:
            # Cria um widget de overlay
            self.overlay = SimpleAnswerLabel(self.answer_letter)
            self.overlay.show()
            
            # Define um timer para remover o overlay após alguns segundos
            self.is_active = True
            QTimer.singleShot(5000, self.hide_pointer)
            
        except Exception as e:
            if DEBUG:
                print(f"Erro ao criar overlay: {str(e)}")
            
    def hide_pointer(self):
        """
        Esconde o apontador visual.
        """
        try:
            if self.overlay:
                self.overlay.close()
                self.overlay = None
                
            self.is_active = False
            
        except Exception as e:
            if DEBUG:
                print(f"Erro ao esconder apontador: {str(e)}")
            
    def is_pointer_active(self):
        """
        Verifica se o apontador está ativo.
        
        Returns:
            True se o apontador estiver ativo, False caso contrário
        """
        return self.is_active

class SimpleAnswerLabel(QWidget):
    """
    Widget de overlay simplificado para destacar a resposta correta.
    """
    def __init__(self, letter):
        """
        Inicializa o widget de overlay.
        
        Args:
            letter: Letra da alternativa (A, B, C, D, E)
        """
        super().__init__()
        
        self.letter = letter
        
        # Configura a janela
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.WindowStaysOnTopHint | Qt.Tool)
        self.setAttribute(Qt.WA_TranslucentBackground)
        
        # Posiciona no canto superior direito da tela
        self.setGeometry(100, 100, 200, 100)
        
        # Cria o label com a resposta
        self.label = QLabel(f"Resposta: {letter}", self)
        self.label.setGeometry(0, 0, 200, 100)
        
        # Estiliza o label
        self.label.setAlignment(Qt.AlignCenter)
        font = QFont()
        font.setPointSize(24)
        font.setBold(True)
        self.label.setFont(font)
        
        # Define a cor de fundo e do texto
        palette = QPalette()
        palette.setColor(QPalette.Window, QColor(0, 0, 0, 180))
        palette.setColor(QPalette.WindowText, QColor(255, 0, 0))
        self.label.setAutoFillBackground(True)
        self.label.setPalette(palette)
        
        # Cria um timer para piscar o texto
        self.blink_timer = QTimer(self)
        self.blink_timer.timeout.connect(self._blink)
        self.blink_timer.start(500)
        self.blink_state = True
        
    def _blink(self):
        """
        Faz o texto piscar.
        """
        try:
            palette = self.label.palette()
            if self.blink_state:
                palette.setColor(QPalette.WindowText, QColor(255, 255, 0))  # Amarelo
            else:
                palette.setColor(QPalette.WindowText, QColor(255, 0, 0))  # Vermelho
                
            self.label.setPalette(palette)
            self.blink_state = not self.blink_state
            
        except Exception as e:
            if DEBUG:
                print(f"Erro ao piscar texto: {str(e)}")
