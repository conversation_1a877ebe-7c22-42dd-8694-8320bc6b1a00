"""
Module for interacting with LLM APIs.
"""
import os
import json
import requests
from openai import OpenAI
from ..core.config import LLM_API_KEY, LLM_MODEL, LLM_BASE_URL, LLM_SITE_URL, LLM_SITE_NAME, DEBUG
from ..core.context_prompts import get_context_prompt, detect_context

class LLMClient:
    def __init__(self):
        self.api_key = LLM_API_KEY
        self.model = LLM_MODEL
        self.base_url = LLM_BASE_URL
        self.site_url = LLM_SITE_URL
        self.site_name = LLM_SITE_NAME

        if not self.api_key:
            if DEBUG:
                print("Warning: LLM API key not set. Set LLM_API_KEY in .env file.")

        # Initialize OpenAI client with OpenRouter configuration
        self.client = OpenAI(
            base_url=self.base_url,
            api_key=self.api_key
        )

    def process_question(self, question_text, context_text=None):
        """
        Process a question using the LLM with context-aware prompts.

        Args:
            question_text (str): The question to process
            context_text (str, optional): Additional context from the page

        Returns:
            str: The LLM's response
        """
        if not self.api_key:
            return "Error: LLM API key not configured. Please set LLM_API_KEY in .env file."

        try:
            # Verificar se o texto está vazio ou muito curto
            if not question_text or len(question_text.strip()) < 5:
                question_text = "Por favor, analise o texto fornecido e extraia as informações mais relevantes."

            if context_text and len(context_text.strip()) < 5:
                context_text = None

            # Log para depuração
            print(f"\n=== ENVIANDO PARA LLM ===")
            print(f"Pergunta ({len(question_text)} caracteres): {question_text[:100]}...")
            if context_text:
                print(f"Contexto ({len(context_text)} caracteres): {context_text[:100]}...")
            else:
                print("Sem contexto adicional")

            # Combine question and context for context detection
            full_text = question_text
            if context_text:
                full_text = context_text + "\n\n" + question_text

            # Detect the context and get the appropriate prompt
            detected_context = detect_context(full_text)
            context_prompt = get_context_prompt(full_text)

            if DEBUG:
                print(f"Detected context: {detected_context}")

            # Prepare the prompt with context-specific instructions
            # Note: The text might be in English due to OCR, but we want Portuguese responses
            if context_text:
                prompt = f"""
                {context_prompt}

                Preciso de ajuda para responder uma pergunta. Aqui está o contexto da página
                (pode estar em inglês devido ao OCR, mas preciso de resposta em português):

                {context_text}

                E aqui está a pergunta (pode estar em inglês devido ao OCR):

                {question_text}

                Por favor, forneça uma resposta clara e concisa baseada no contexto.
                Seja direto e objetivo, sem repetir todo o conteúdo da pergunta.
                Mesmo que o texto esteja em inglês, RESPONDA SEMPRE em português (PT-BR).
                """
            else:
                prompt = f"""
                {context_prompt}

                Preciso de ajuda para responder esta pergunta (pode estar em inglês devido ao OCR):

                {question_text}

                Por favor, forneça uma resposta clara e concisa.
                Seja direto e objetivo, sem repetir todo o conteúdo da pergunta.
                Mesmo que o texto esteja em inglês, RESPONDA SEMPRE em português (PT-BR).
                """

            # Call the OpenRouter API via OpenAI client
            response = self.client.chat.completions.create(
                extra_headers={
                    "HTTP-Referer": self.site_url,  # Site URL for rankings on openrouter.ai
                    "X-Title": self.site_name,      # Site title for rankings on openrouter.ai
                },
                model=self.model,
                messages=[
                    {"role": "system", "content": f"""Você é um professor especialista em {detected_context} analisando uma questão de múltipla escolha.

MODO DE RACIOCÍNIO ATIVADO:
1. Identifique o assunto específico da questão dentro do contexto de {detected_context}.
2. Analise recursivamente a lógica por trás de cada alternativa, questionando seu próprio raciocínio.
3. Para questões que envolvem cálculos, mostre TODOS os passos intermediários detalhadamente.
4. Após chegar a uma conclusão inicial, reavalie-a usando uma abordagem diferente para verificação.
5. Avalie cada alternativa metodicamente, explicando por que as incorretas devem ser descartadas.
6. Implemente um sistema de votação interna: gere três análises independentes e escolha a resposta mais frequente.

FORMATO OBRIGATÓRIO DA RESPOSTA:
- Comece SEMPRE com "Resposta correta: [LETRA]) [TEXTO DA ALTERNATIVA]"
- Responda SEMPRE em português (PT-BR), mesmo que a pergunta esteja em inglês
- Destaque claramente por que as outras alternativas estão incorretas
- Seja direto e objetivo, priorizando a resposta correta

Lembre-se que o usuário precisa de uma resposta precisa e confiável, então use todas as técnicas de verificação e análise para garantir a maior taxa de acerto possível."""},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=1200,  # Aumentado para permitir análise mais detalhada
                temperature=0.1,  # Reduzido ainda mais para respostas mais determinísticas
                timeout=25,  # Timeout aumentado para permitir mais tempo de processamento
                top_p=0.95,  # Controle de diversidade para respostas mais precisas
                frequency_penalty=0.0,  # Sem penalidade de frequência
                presence_penalty=0.0  # Sem penalidade de presença
            )

            # Extract and return the response text
            answer = response.choices[0].message.content.strip()
            return answer

        except Exception as e:
            error_msg = f"Error calling LLM API: {str(e)}"
            if DEBUG:
                print(error_msg)
            return error_msg

# For testing
if __name__ == "__main__":
    client = LLMClient()
    response = client.process_question("What is the capital of France?")
    print("LLM Response:")
    print(response)
