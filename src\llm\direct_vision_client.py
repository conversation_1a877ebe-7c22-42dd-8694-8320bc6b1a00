"""
Módulo para processar imagens diretamente com a LLM, sem usar OCR.
"""
import os
import base64
import requests
from openai import OpenAI
from ..core.config import LLM_API_KEY, LLM_MODEL, LLM_BASE_URL, LLM_SITE_URL, LLM_SITE_NAME, DEBUG
from ..core.context_prompts import get_context_prompt, detect_context
from ..core.enhanced_prompt_manager import EnhancedPromptManager

class DirectVisionClient:
    # Lista de modelos gratuitos alternativos para usar em caso de limite de taxa
    FREE_MODELS = [
        "google/gemini-2.0-flash-exp:free",
        "meta-llama/llama-3.2-11b-vision-instruct:free",
        "qwen/qwen2.5-vl-32b-instruct:free",
        "anthropic/claude-3-5-sonnet-20240620:free",
        "google/gemini-2.5-pro-exp-03-25:free"
    ]

    def get_available_models(self):
        """
        Retorna a lista de modelos disponíveis no OpenRouter.

        Returns:
            Lista de dicionários com informações dos modelos
        """
        # Retorna a lista de modelos gratuitos
        models = []
        for model_id in self.FREE_MODELS:
            name = model_id.split("/")[-1].split(":")[0].replace("-", " ").title()
            models.append({
                "id": model_id,
                "name": name,
                "description": "Modelo com suporte a visão do OpenRouter",
                "supports_vision": True,
                "context_length": 4096,
                "category": "vision"
            })
        return models

    def set_model(self, model_id):
        """
        Define o modelo a ser usado.

        Args:
            model_id: ID do modelo

        Returns:
            True se o modelo foi definido com sucesso, False caso contrário
        """
        if model_id in self.FREE_MODELS:
            self.model = model_id
            return True
        else:
            # Se o modelo não estiver na lista, usa o primeiro
            self.model = self.FREE_MODELS[0]
            return True

    def get_provider_name(self):
        """
        Retorna o nome do provedor de LLM.

        Returns:
            Nome do provedor
        """
        return "OpenRouter"

    def __init__(self):
        self.api_key = LLM_API_KEY
        self.model = LLM_MODEL
        self.base_url = LLM_BASE_URL
        self.site_url = LLM_SITE_URL
        self.site_name = LLM_SITE_NAME

        # Índice para rotação de modelos
        self.current_model_index = 0

        # Inicializa o gerenciador de prompts com validação
        self.prompt_manager = EnhancedPromptManager()

        if not self.api_key:
            if DEBUG:
                print("Warning: LLM API key not set. Set LLM_API_KEY in .env file.")

        # Initialize OpenAI client with OpenRouter configuration
        self.client = OpenAI(
            base_url=self.base_url,
            api_key=self.api_key
        )

    def get_next_free_model(self):
        """
        Retorna o próximo modelo gratuito da lista de rotação.
        """
        # Verifica se o modelo atual está na lista
        try:
            current_index = self.FREE_MODELS.index(self.model)
            next_index = (current_index + 1) % len(self.FREE_MODELS)
        except ValueError:
            # Se o modelo atual não estiver na lista, começa do início
            next_index = 0

        # Atualiza o índice atual
        self.current_model_index = next_index

        # Retorna o próximo modelo
        next_model = self.FREE_MODELS[next_index]

        if DEBUG:
            print(f"Alternando para o próximo modelo gratuito: {next_model}")

        return next_model

    def encode_image_to_base64(self, image_path):
        """
        Codifica uma imagem para base64.

        Args:
            image_path: Caminho para a imagem

        Returns:
            String base64 da imagem
        """
        with open(image_path, "rb") as image_file:
            return base64.b64encode(image_file.read()).decode('utf-8')

    def process_image_with_validation(self, image_path, question_text="", retry_count=0, max_retries=2):
        """
        Processa uma imagem com sistema de validação integrado.

        Args:
            image_path: Caminho para a imagem
            question_text: Texto da questão (se disponível via OCR)
            retry_count: Contador de tentativas para evitar recursão infinita
            max_retries: Número máximo de tentativas de reprocessamento

        Returns:
            Resposta validada da LLM
        """
        if DEBUG:
            print(f"Processando imagem com validação (tentativa {retry_count + 1})")

        # Processa a imagem normalmente
        llm_response = self.process_image(image_path, retry_count)

        # Se não temos o texto da questão, usa análise visual direta
        if not question_text:
            if DEBUG:
                print("Texto da questão não disponível, usando análise visual direta")

            # Usa prompt específico para análise visual de questões
            visual_prompt = """
            Analise esta imagem de uma questão de múltipla escolha e responda seguindo EXATAMENTE este formato:

            **Resposta correta: [LETRA]) [TEXTO EXATO DA ALTERNATIVA]**

            [Explicação em 1-2 frases sobre por que esta é a resposta correta]

            IMPORTANTE:
            - Leia TODAS as alternativas visíveis na imagem
            - Identifique qual é a resposta correta baseada no conhecimento
            - Use o texto EXATO da alternativa escolhida
            - Seja conciso e direto
            - Responda em português
            """

            llm_response = self.process_image(image_path, visual_prompt)

            # Tenta extrair a resposta do formato padrão
            import re
            pattern = r'\*\*Resposta correta:\s*([A-E])\)\s*(.+?)\*\*'
            match = re.search(pattern, llm_response)

            if match and hasattr(self, 'prompt_manager') and self.prompt_manager:
                letter = match.group(1)
                text = match.group(2).strip()

                # Cria validação baseada na resposta extraída
                visual_validation = {
                    'is_valid': True,
                    'selected_letter': letter,
                    'selected_text': text,
                    'domain': 'transito',  # Assume trânsito por padrão
                    'confidence': 80,
                    'response_type': 'visual_analysis',
                    'has_calculations': False,
                    'needs_reprocessing': False
                }
                try:
                    formatted_response = self.prompt_manager.format_final_response(visual_validation, llm_response)
                    return formatted_response
                except:
                    pass

            return llm_response

        # Valida a resposta
        validation_result = self.prompt_manager.validate_response(question_text, llm_response)

        if DEBUG:
            print(f"Resultado da validação: {validation_result}")

        # Se a validação falhou e ainda temos tentativas, reprocessa
        if not validation_result.get('is_valid', True) and retry_count < max_retries:
            if DEBUG:
                print(f"Validação falhou: {validation_result.get('error', 'Erro desconhecido')}")
                print("Reprocessando com prompt de validação...")

            # Usa o prompt com validação para reprocessar
            return self.process_image_with_validation_prompt(image_path, question_text, retry_count + 1)

        # Se a validação passou ou esgotamos as tentativas, formata a resposta
        if validation_result.get('is_valid', True):
            formatted_response = self.prompt_manager.format_final_response(validation_result, llm_response)
            return formatted_response
        else:
            # Se ainda assim falhou, retorna a resposta original com aviso
            return f"⚠️ Resposta com validação limitada:\n\n{llm_response}\n\n(Erro de validação: {validation_result.get('error', 'Desconhecido')})"

    def process_image_with_validation_prompt(self, image_path, question_text, retry_count=0):
        """
        Processa uma imagem usando prompt com validação integrada.

        Args:
            image_path: Caminho para a imagem
            question_text: Texto da questão
            retry_count: Contador de tentativas

        Returns:
            Resposta da LLM com prompt de validação
        """
        # Limita o número de tentativas para evitar recursão infinita
        if retry_count >= len(self.FREE_MODELS):
            return "Erro: Todos os modelos gratuitos foram tentados e falharam. Por favor, tente novamente mais tarde."

        if not self.api_key:
            return "Erro: Chave da API LLM não configurada. Por favor, configure LLM_API_KEY no arquivo .env."

        if not os.path.exists(image_path):
            return f"Erro: Arquivo de imagem não encontrado em {image_path}"

        try:
            # Codifica a imagem para base64
            base64_image = self.encode_image_to_base64(image_path)

            # Cria prompt com validação
            validated_prompt = self.prompt_manager.create_validated_prompt(question_text)

            if DEBUG:
                print("Usando prompt com sistema de validação integrado")

            # Tenta usar um modelo que sabemos que funciona se o atual não tiver a tag :free
            model_to_use = self.model
            if not model_to_use.endswith(":free"):
                model_to_use = self.FREE_MODELS[0]

            # Chama a API com o prompt validado
            response = self.client.chat.completions.create(
                extra_headers={
                    "HTTP-Referer": self.site_url,
                    "X-Title": self.site_name,
                },
                model=model_to_use,
                messages=[
                    {
                        "role": "system",
                        "content": "Você é um sistema especialista em análise de questões de múltipla escolha com validação integrada."
                    },
                    {
                        "role": "user",
                        "content": [
                            {"type": "text", "text": validated_prompt},
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/jpeg;base64,{base64_image}",
                                    "detail": "high"
                                }
                            }
                        ]
                    }
                ],
                max_tokens=1500,  # Aumentado para permitir validação completa
                temperature=0.05,
                timeout=35,  # Aumentado para dar tempo para validação
                top_p=0.95,
                frequency_penalty=0.0,
                presence_penalty=0.0,
                seed=42
            )

            # Processa a resposta
            if hasattr(response, 'choices') and response.choices and len(response.choices) > 0:
                if hasattr(response.choices[0], 'message') and hasattr(response.choices[0].message, 'content'):
                    return response.choices[0].message.content.strip()

            return "Erro: Não foi possível obter resposta válida da LLM."

        except Exception as e:
            if DEBUG:
                print(f"Erro no processamento com validação: {str(e)}")

            # Em caso de erro, tenta com o próximo modelo
            if retry_count < len(self.FREE_MODELS) - 1:
                next_model = self.get_next_free_model()
                self.model = next_model
                return self.process_image_with_validation_prompt(image_path, question_text, retry_count + 1)

            return f"Erro no processamento com validação: {str(e)}"

    def process_image(self, image_path, retry_count=0):
        """
        Processa uma imagem diretamente com a LLM (método original).

        Args:
            image_path: Caminho para a imagem
            retry_count: Contador de tentativas para evitar recursão infinita

        Returns:
            Resposta da LLM
        """
        # Limita o número de tentativas para evitar recursão infinita
        if retry_count >= len(self.FREE_MODELS):
            return "Erro: Todos os modelos gratuitos foram tentados e falharam. Por favor, tente novamente mais tarde."
        if not self.api_key:
            return "Erro: Chave da API LLM não configurada. Por favor, configure LLM_API_KEY no arquivo .env."

        if not os.path.exists(image_path):
            return f"Erro: Arquivo de imagem não encontrado em {image_path}"

        # Verifica se o modelo selecionado é gratuito (tem a tag :free)
        if not self.model.endswith(":free"):
            if DEBUG:
                print(f"Aviso: O modelo selecionado ({self.model}) pode não ser gratuito. Recomenda-se usar modelos com a tag ':free'.")
            # Não bloqueamos o uso, apenas avisamos no log

        try:
            # Verifica se a API key está configurada
            if not self.api_key:
                return "Erro: Chave da API LLM não configurada. Por favor, configure LLM_API_KEY no arquivo .env."

            # Verifica se o modelo está configurado
            if not self.model:
                return "Erro: Modelo LLM não configurado. Por favor, configure LLM_MODEL no arquivo .env."

            # Verifica se a imagem existe e não está vazia
            if not os.path.exists(image_path):
                return f"Erro: Arquivo de imagem não encontrado em {image_path}"

            if os.path.getsize(image_path) < 1000:
                return "Erro: A imagem capturada está vazia ou corrompida (tamanho muito pequeno). Por favor, tente novamente."

            # Verifica se o arquivo é realmente uma imagem
            try:
                import imghdr
                image_type = imghdr.what(image_path)
                if not image_type:
                    return "Erro: O arquivo não parece ser uma imagem válida. Por favor, tente novamente."

                if DEBUG:
                    print(f"Tipo de imagem detectado: {image_type}")
            except ImportError:
                # Se imghdr não estiver disponível, continua sem essa verificação
                pass

            # Codifica a imagem para base64
            try:
                base64_image = self.encode_image_to_base64(image_path)

                if len(base64_image) < 100:
                    return "Erro: A imagem codificada está vazia ou corrompida. Por favor, tente novamente."
            except Exception as e:
                return f"Erro ao codificar a imagem: {str(e)}"

            # Prepara o prompt com instruções específicas e análise recursiva
            prompt = """
            Você é um professor especialista analisando uma questão de múltipla escolha.

            MODO DE RACIOCÍNIO ATIVADO:
            1. Identifique o assunto específico da questão na imagem.
            2. Analise recursivamente a lógica por trás de cada alternativa, questionando seu próprio raciocínio.
            3. Para questões que envolvem cálculos, mostre TODOS os passos intermediários detalhadamente.
            4. Após chegar a uma conclusão inicial, reavalie-a usando uma abordagem diferente para verificação.
            5. Avalie cada alternativa metodicamente, explicando por que as incorretas devem ser descartadas.
            6. Implemente um sistema de votação interna: gere três análises independentes e escolha a resposta mais frequente.

            FORMATO OBRIGATÓRIO DA RESPOSTA:
            - Comece SEMPRE com "Resposta correta: [LETRA]) [TEXTO DA ALTERNATIVA]"
            - Responda SEMPRE em português (PT-BR), mesmo que a pergunta esteja em inglês
            - Destaque claramente por que as outras alternativas estão incorretas
            - Seja direto e objetivo, priorizando a resposta correta

            Se você não conseguir ver a imagem ou a imagem não estiver clara, informe isso na sua resposta.
            """

            if DEBUG:
                print(f"Enviando imagem para LLM: {image_path}")
                print(f"Tamanho do arquivo: {os.path.getsize(image_path)} bytes")
                print(f"Usando modelo: {self.model}")
                print(f"Base URL: {self.base_url}")
                print(f"Tamanho da imagem em base64: {len(base64_image)} caracteres")

            # Chama a API com a imagem
            if DEBUG:
                print(f"Enviando requisição para a API com o modelo {self.model}...")
                print(f"Tamanho da imagem em base64: {len(base64_image)} caracteres")

            # Tenta usar um modelo que sabemos que funciona se o atual não tiver a tag :free
            model_to_use = self.model
            if not model_to_use.endswith(":free"):
                if DEBUG:
                    print(f"Modelo {model_to_use} não tem a tag :free, usando modelo alternativo")
                model_to_use = self.FREE_MODELS[0]  # Usa o primeiro modelo da lista de modelos gratuitos

            if DEBUG:
                print(f"Usando modelo: {model_to_use}")

            # Chama a API com a imagem
            response = self.client.chat.completions.create(
                extra_headers={
                    "HTTP-Referer": self.site_url,  # Site URL for rankings on openrouter.ai
                    "X-Title": self.site_name,      # Site title for rankings on openrouter.ai
                },
                model=model_to_use,
                messages=[
                    {
                        "role": "system",
                        "content": """Você é um professor especialista analisando uma questão de múltipla escolha.

MODO DE RACIOCÍNIO ATIVADO:
1. Identifique o assunto específico da questão na imagem.
2. Analise recursivamente a lógica por trás de cada alternativa, questionando seu próprio raciocínio.
3. Para questões que envolvem cálculos, mostre TODOS os passos intermediários detalhadamente.
4. Após chegar a uma conclusão inicial, reavalie-a usando uma abordagem diferente para verificação.
5. Avalie cada alternativa metodicamente, explicando por que as incorretas devem ser descartadas.
6. Implemente um sistema de votação interna: gere três análises independentes e escolha a resposta mais frequente.

FORMATO OBRIGATÓRIO DA RESPOSTA:
- Comece SEMPRE com "Resposta correta: [LETRA]) [TEXTO DA ALTERNATIVA]"
- Responda SEMPRE em português (PT-BR), mesmo que a pergunta esteja em inglês
- Destaque claramente por que as outras alternativas estão incorretas
- Seja direto e objetivo, priorizando a resposta correta"""
                    },
                    {
                        "role": "user",
                        "content": [
                            {"type": "text", "text": prompt},
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/jpeg;base64,{base64_image}",
                                    "detail": "high"
                                }
                            }
                        ]
                    }
                ],
                max_tokens=1200,  # Aumentado para permitir análise mais detalhada
                temperature=0.05,  # Temperatura muito baixa para respostas mais determinísticas
                timeout=30,  # Mantido para dar tempo suficiente para processar a imagem
                top_p=0.95,  # Controle de diversidade para respostas mais precisas
                frequency_penalty=0.0,  # Sem penalidade de frequência
                presence_penalty=0.0,  # Sem penalidade de presença
                seed=42  # Seed fixo para maior consistência nas respostas
            )
            # Verifica se ocorreu algum erro na chamada da API
            if hasattr(response, 'error') and response.error is not None:
                error_message = response.error.get('message', '') if isinstance(response.error, dict) else str(response.error)

                if DEBUG:
                    print(f"Erro retornado pela API: {error_message}")
                    print(f"Modelo usado: {model_to_use}")
                    print(f"API Key configurada: {'Sim' if self.api_key else 'Não'}")
                    print(f"Base URL: {self.base_url}")

                # Verifica se é um erro de limite de taxa ou cota
                if "insufficient_quota" in error_message.lower() or "billing" in error_message.lower() or "rate limit" in error_message.lower() or "429" in str(response.error):
                    # Tenta com outro modelo gratuito
                    next_model = self.get_next_free_model()
                    if DEBUG:
                        print(f"Erro de cota ou limite de taxa. Tentando com outro modelo: {next_model}")

                    # Atualiza o modelo atual
                    self.model = next_model

                    # Tenta novamente com o novo modelo, incrementando o contador de tentativas
                    return self.process_image(image_path, retry_count + 1)

                return f"Erro da API: {error_message}"

            # Verifica se a resposta é válida
            if response is None:
                return "Erro: Não foi possível obter resposta da LLM. Verifique se o modelo selecionado está disponível e é gratuito."

            # A verificação de erro já foi feita acima

            # Extrai e retorna o texto da resposta
            try:
                # Verifica se a resposta tem o formato esperado
                if hasattr(response, 'choices') and response.choices is not None and len(response.choices) > 0:
                    if hasattr(response.choices[0], 'message') and hasattr(response.choices[0].message, 'content'):
                        answer = response.choices[0].message.content.strip()
                        if answer:
                            return answer

                # Se chegou aqui, a resposta não tem o formato esperado
                if DEBUG:
                    print("Resposta não tem o formato esperado")
                    print(f"Resposta: {response}")

                # Tenta extrair a resposta de outras formas
                if hasattr(response, 'choices') and response.choices is not None:
                    if len(response.choices) > 0:
                        choice = response.choices[0]
                        if hasattr(choice, 'text'):
                            return choice.text.strip()
                        elif isinstance(choice, dict) and 'text' in choice:
                            return choice['text'].strip()
                        elif isinstance(choice, dict) and 'message' in choice:
                            if isinstance(choice['message'], dict) and 'content' in choice['message']:
                                return choice['message']['content'].strip()

                # Se ainda não conseguiu extrair, tenta converter para string
                return f"Resposta da LLM (formato não reconhecido): {str(response)}"

            except (AttributeError, IndexError, TypeError) as e:
                if DEBUG:
                    print(f"Erro ao extrair resposta: {str(e)}")
                    print(f"Resposta recebida: {response}")

                # Verifica se é um erro de limite de taxa
                if 'rate limit' in str(e).lower() or 'ratelimit' in str(e).lower():
                    return "Erro: Limite de requisições gratuitas excedido. Por favor, tente novamente mais tarde ou use outro modelo gratuito."

                return f"Erro ao processar resposta da LLM: {str(e)}"

        except Exception as e:
            error_msg = f"Error processing image with LLM: {str(e)}"
            if DEBUG:
                print(error_msg)
            return error_msg

# Para testes
if __name__ == "__main__":
    client = DirectVisionClient()
    response = client.process_image("current_capture.png")
    print("LLM Response:")
    print(response)
