"""
Módulo para o diálogo de configurações da aplicação.
"""
import os
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
                           QComboBox, QCheckBox, QGroupBox, QFormLayout, QLineEdit,
                           QTabWidget, QWidget, QSpinBox, QDialogButtonBox, QFileDialog)
from PyQt5.QtCore import Qt, QSettings
from llm_models import get_available_models, get_model_by_id
from config import LLM_API_KEY, LLM_MODEL, TESSERACT_PATH, ADS_ENABLED, ADS_CLIENT_ID

class SettingsDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Configurações")
        self.resize(600, 400)

        # Inicializa as configurações
        self.settings = QSettings("AugmentCode", "PythonQuestionHelper")

        # Cria o layout principal
        main_layout = QVBoxLayout()

        # Cria as abas
        self.tab_widget = QTabWidget()

        # Cria as abas individuais
        self.create_llm_tab()
        self.create_ocr_tab()
        self.create_monitor_tab()
        self.create_ads_tab()

        # Adiciona as abas ao widget de abas
        main_layout.addWidget(self.tab_widget)

        # Adiciona os botões de OK e Cancelar
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        main_layout.addWidget(button_box)

        self.setLayout(main_layout)

        # Carrega as configurações salvas
        self.load_settings()

    def create_llm_tab(self):
        """Cria a aba de configurações de LLM."""
        llm_tab = QWidget()
        layout = QVBoxLayout()

        # Grupo de configurações de API
        api_group = QGroupBox("Configurações de API")
        api_layout = QFormLayout()

        # Campo de API Key
        self.api_key_input = QLineEdit()
        self.api_key_input.setEchoMode(QLineEdit.Password)
        self.api_key_input.setPlaceholderText("Insira sua chave de API do OpenRouter")
        api_layout.addRow("API Key:", self.api_key_input)

        api_group.setLayout(api_layout)
        layout.addWidget(api_group)

        # Grupo de seleção de modelo
        model_group = QGroupBox("Seleção de Modelo")
        model_layout = QVBoxLayout()

        # Dropdown de modelos
        model_form = QFormLayout()
        self.model_combo = QComboBox()

        # Preenche o dropdown com os modelos disponíveis
        models = get_available_models()
        for model in models:
            self.model_combo.addItem(f"{model['name']} ({model['supports_vision'] and 'Suporta Visão' or 'Sem Visão'})", model['id'])

        model_form.addRow("Modelo:", self.model_combo)

        # Informações do modelo selecionado
        self.model_info_label = QLabel()
        self.model_info_label.setWordWrap(True)
        self.model_combo.currentIndexChanged.connect(self.update_model_info)

        model_layout.addLayout(model_form)
        model_layout.addWidget(self.model_info_label)

        model_group.setLayout(model_layout)
        layout.addWidget(model_group)

        # Grupo de configurações de resposta
        response_group = QGroupBox("Configurações de Resposta")
        response_layout = QFormLayout()

        # Temperatura
        self.temperature_spin = QSpinBox()
        self.temperature_spin.setRange(0, 100)
        self.temperature_spin.setValue(20)  # 0.2 por padrão
        self.temperature_spin.setSingleStep(5)
        self.temperature_spin.setPrefix("0.")
        response_layout.addRow("Temperatura:", self.temperature_spin)

        # Tamanho máximo da resposta
        self.max_tokens_spin = QSpinBox()
        self.max_tokens_spin.setRange(100, 2000)
        self.max_tokens_spin.setValue(800)
        self.max_tokens_spin.setSingleStep(100)
        response_layout.addRow("Tamanho máximo da resposta:", self.max_tokens_spin)

        response_group.setLayout(response_layout)
        layout.addWidget(response_group)

        # Adiciona espaço em branco para preencher o resto da aba
        layout.addStretch()

        llm_tab.setLayout(layout)
        self.tab_widget.addTab(llm_tab, "Modelo LLM")

    def create_ocr_tab(self):
        """Cria a aba de configurações de OCR."""
        ocr_tab = QWidget()
        layout = QVBoxLayout()

        # Grupo de configurações do Tesseract
        tesseract_group = QGroupBox("Configurações do Tesseract OCR")
        tesseract_layout = QFormLayout()

        # Caminho do Tesseract
        tesseract_path_layout = QHBoxLayout()
        self.tesseract_path_input = QLineEdit()
        self.tesseract_path_input.setPlaceholderText("Caminho para o executável do Tesseract")
        tesseract_path_layout.addWidget(self.tesseract_path_input)

        # Botão para selecionar o caminho
        browse_button = QPushButton("Procurar...")
        browse_button.clicked.connect(self.browse_tesseract_path)
        tesseract_path_layout.addWidget(browse_button)

        tesseract_layout.addRow("Caminho do Tesseract:", tesseract_path_layout)

        # Idiomas para OCR
        self.ocr_lang_combo = QComboBox()
        self.ocr_lang_combo.addItem("Português", "por")
        self.ocr_lang_combo.addItem("Inglês", "eng")
        self.ocr_lang_combo.addItem("Português + Inglês", "por+eng")
        tesseract_layout.addRow("Idioma principal:", self.ocr_lang_combo)

        tesseract_group.setLayout(tesseract_layout)
        layout.addWidget(tesseract_group)

        # Grupo de configurações de pré-processamento
        preprocess_group = QGroupBox("Pré-processamento de Imagem")
        preprocess_layout = QVBoxLayout()

        # Opções de pré-processamento
        self.auto_crop_check = QCheckBox("Recortar automaticamente a área da questão")
        self.auto_crop_check.setChecked(True)
        preprocess_layout.addWidget(self.auto_crop_check)

        self.enhance_image_check = QCheckBox("Melhorar imagem para OCR")
        self.enhance_image_check.setChecked(True)
        preprocess_layout.addWidget(self.enhance_image_check)

        preprocess_group.setLayout(preprocess_layout)
        layout.addWidget(preprocess_group)

        # Adiciona espaço em branco para preencher o resto da aba
        layout.addStretch()

        ocr_tab.setLayout(layout)
        self.tab_widget.addTab(ocr_tab, "OCR")

    def create_monitor_tab(self):
        """Cria a aba de configurações de monitoramento."""
        monitor_tab = QWidget()
        layout = QVBoxLayout()

        # Grupo de configurações de monitoramento
        monitor_group = QGroupBox("Configurações de Monitoramento")
        monitor_layout = QFormLayout()

        # Intervalo de verificação
        self.check_interval_spin = QSpinBox()
        self.check_interval_spin.setRange(1, 30)
        self.check_interval_spin.setValue(5)
        self.check_interval_spin.setSuffix(" segundos")
        monitor_layout.addRow("Intervalo de verificação:", self.check_interval_spin)

        # Limiar de mudança
        self.change_threshold_spin = QSpinBox()
        self.change_threshold_spin.setRange(1, 50)
        self.change_threshold_spin.setValue(1)
        self.change_threshold_spin.setPrefix("0.0")
        monitor_layout.addRow("Limiar de mudança:", self.change_threshold_spin)

        monitor_group.setLayout(monitor_layout)
        layout.addWidget(monitor_group)

        # Grupo de opções de processamento
        process_group = QGroupBox("Opções de Processamento")
        process_layout = QVBoxLayout()

        # Opções de processamento
        self.use_vision_check = QCheckBox("Usar visão direta (sem OCR)")
        self.use_vision_check.setChecked(True)
        process_layout.addWidget(self.use_vision_check)

        self.process_first_check = QCheckBox("Processar primeira imagem imediatamente")
        self.process_first_check.setChecked(True)
        process_layout.addWidget(self.process_first_check)

        process_group.setLayout(process_layout)
        layout.addWidget(process_group)

        # Adiciona espaço em branco para preencher o resto da aba
        layout.addStretch()

        monitor_tab.setLayout(layout)
        self.tab_widget.addTab(monitor_tab, "Monitoramento")

    def browse_tesseract_path(self):
        """Abre um diálogo para selecionar o caminho do Tesseract."""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Selecionar Executável do Tesseract", "", "Executáveis (*.exe);;Todos os Arquivos (*)"
        )
        if file_path:
            self.tesseract_path_input.setText(file_path)

    def update_model_info(self):
        """Atualiza as informações do modelo selecionado."""
        model_id = self.model_combo.currentData()
        model = get_model_by_id(model_id)

        if model:
            info_text = f"<b>{model['name']}</b><br>"
            info_text += f"{model['description']}<br>"
            info_text += f"Suporta visão: {'Sim' if model['supports_vision'] else 'Não'}<br>"
            info_text += f"Tamanho do contexto: {model['context_length']} tokens"

            self.model_info_label.setText(info_text)

            # Desabilita a opção de visão direta se o modelo não suportar
            self.use_vision_check.setEnabled(model['supports_vision'])
            if not model['supports_vision']:
                self.use_vision_check.setChecked(False)

    def load_settings(self):
        """Carrega as configurações salvas."""
        # Configurações de LLM
        self.api_key_input.setText(self.settings.value("llm/api_key", LLM_API_KEY or ""))

        # Seleciona o modelo atual
        current_model = self.settings.value("llm/model", LLM_MODEL)
        for i in range(self.model_combo.count()):
            if self.model_combo.itemData(i) == current_model:
                self.model_combo.setCurrentIndex(i)
                break

        self.temperature_spin.setValue(int(float(self.settings.value("llm/temperature", 0.2)) * 100))
        self.max_tokens_spin.setValue(int(self.settings.value("llm/max_tokens", 800)))

        # Configurações de OCR
        self.tesseract_path_input.setText(self.settings.value("ocr/tesseract_path", TESSERACT_PATH or ""))

        ocr_lang = self.settings.value("ocr/language", "por+eng")
        for i in range(self.ocr_lang_combo.count()):
            if self.ocr_lang_combo.itemData(i) == ocr_lang:
                self.ocr_lang_combo.setCurrentIndex(i)
                break

        self.auto_crop_check.setChecked(self.settings.value("ocr/auto_crop", True, type=bool))
        self.enhance_image_check.setChecked(self.settings.value("ocr/enhance_image", True, type=bool))

        # Configurações de monitoramento
        self.check_interval_spin.setValue(self.settings.value("monitor/check_interval", 5, type=int))
        self.change_threshold_spin.setValue(int(float(self.settings.value("monitor/change_threshold", 0.01)) * 1000))
        self.use_vision_check.setChecked(self.settings.value("monitor/use_vision", True, type=bool))
        self.process_first_check.setChecked(self.settings.value("monitor/process_first", True, type=bool))

        # Atualiza as informações do modelo
        self.update_model_info()

    def create_ads_tab(self):
        """Cria a aba de configurações de anúncios."""
        ads_tab = QWidget()
        layout = QVBoxLayout()

        # Grupo de configurações de anúncios
        ads_group = QGroupBox("Configurações de Anúncios")
        ads_form = QFormLayout()

        # Checkbox para habilitar/desabilitar anúncios
        self.enable_ads_checkbox = QCheckBox("Habilitar anúncios")
        self.enable_ads_checkbox.setChecked(self.settings.value("ads/enabled", ADS_ENABLED, type=bool))
        ads_form.addRow("Anúncios:", self.enable_ads_checkbox)

        # Campo para o ID do cliente do AdSense
        self.ads_client_id_input = QLineEdit()
        self.ads_client_id_input.setText(self.settings.value("ads/client_id", ADS_CLIENT_ID))
        ads_form.addRow("ID do Cliente AdSense:", self.ads_client_id_input)

        # Adiciona uma nota sobre os anúncios
        ads_note = QLabel(
            "Nota: Os anúncios ajudam a manter este software gratuito. "
            "As alterações nas configurações de anúncios terão efeito após reiniciar o aplicativo."
        )
        ads_note.setWordWrap(True)
        ads_note.setStyleSheet("color: gray; font-size: 10px;")

        # Adiciona uma nota sobre o ID do cliente
        client_note = QLabel(
            "Para obter um ID de cliente do AdSense, você precisa se inscrever no "
            "Google AdSense em https://www.google.com/adsense/"
        )
        client_note.setWordWrap(True)
        client_note.setStyleSheet("color: gray; font-size: 10px;")

        ads_group.setLayout(ads_form)
        layout.addWidget(ads_group)
        layout.addWidget(ads_note)
        layout.addWidget(client_note)
        layout.addStretch()

        ads_tab.setLayout(layout)
        self.tab_widget.addTab(ads_tab, "Anúncios")

    def save_settings(self):
        """Salva as configurações."""
        # Configurações de LLM
        self.settings.setValue("llm/api_key", self.api_key_input.text())
        self.settings.setValue("llm/model", self.model_combo.currentData())
        self.settings.setValue("llm/temperature", self.temperature_spin.value() / 100.0)
        self.settings.setValue("llm/max_tokens", self.max_tokens_spin.value())

        # Configurações de OCR
        self.settings.setValue("ocr/tesseract_path", self.tesseract_path_input.text())
        self.settings.setValue("ocr/language", self.ocr_lang_combo.currentData())
        self.settings.setValue("ocr/auto_crop", self.auto_crop_check.isChecked())
        self.settings.setValue("ocr/enhance_image", self.enhance_image_check.isChecked())

        # Configurações de monitoramento
        self.settings.setValue("monitor/check_interval", self.check_interval_spin.value())
        self.settings.setValue("monitor/change_threshold", self.change_threshold_spin.value() / 1000.0)
        self.settings.setValue("monitor/use_vision", self.use_vision_check.isChecked())
        self.settings.setValue("monitor/process_first", self.process_first_check.isChecked())

        # Configurações de anúncios
        self.settings.setValue("ads/enabled", self.enable_ads_checkbox.isChecked())
        self.settings.setValue("ads/client_id", self.ads_client_id_input.text())

    def accept(self):
        """Chamado quando o usuário clica em OK."""
        self.save_settings()
        super().accept()

# Para testes
if __name__ == "__main__":
    from PyQt5.QtWidgets import QApplication
    import sys

    app = QApplication(sys.argv)
    dialog = SettingsDialog()
    if dialog.exec_() == QDialog.Accepted:
        print("Configurações salvas")
    else:
        print("Configurações canceladas")
