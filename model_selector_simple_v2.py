"""
Módulo para seleção de modelos LLM (versão simplificada v2).
"""
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
                           QComboBox, QGroupBox)
from PyQt5.QtCore import QSettings, Qt
from llm_manager import LLMManager

class ModelSelectorSimpleV2Dialog(QDialog):
    def __init__(self, parent=None, current_model_id=None, current_provider=None, detect_any_change=False):
        super().__init__(parent)
        self.setWindowTitle("Selecionar Modelo")
        self.resize(400, 300)  # Tamanho reduzido

        # Inicializa o gerenciador de LLM
        self.llm_manager = LLMManager(current_provider)

        # Carrega as configurações
        self.settings = QSettings("AugmentCode", "PythonQuestionHelper")
        self.current_model_id = current_model_id
        self.current_provider = current_provider or self.llm_manager.provider
        self.detect_any_change = detect_any_change

        # Cria a interface
        self.create_ui()

    def create_ui(self):
        """Cria a interface do diálogo."""
        # Cria o layout principal
        main_layout = QVBoxLayout()

        # Título
        title = QLabel("Selecione o Provedor e Modelo")
        title.setStyleSheet("font-weight: bold; font-size: 14px;")
        main_layout.addWidget(title)

        # Grupo de provedores
        providers_group = QGroupBox("Provedor")
        providers_layout = QVBoxLayout()

        # Adiciona os provedores disponíveis
        self.provider_combo = QComboBox()
        available_providers = self.llm_manager.get_available_providers()

        # Mapeia os nomes dos provedores para exibição
        provider_names = {
            "openrouter": "OpenRouter",
            "huggingface": "Hugging Face",
            "ollama": "Ollama",
            "gemini": "Google Gemini"
        }

        # Adiciona os provedores ao combo box
        for provider in available_providers:
            display_name = provider_names.get(provider, provider.capitalize())
            self.provider_combo.addItem(display_name, provider)

            # Seleciona o provedor atual
            if provider == self.current_provider:
                self.provider_combo.setCurrentIndex(self.provider_combo.count() - 1)

        # Conecta o sinal de mudança de seleção
        self.provider_combo.currentIndexChanged.connect(self.update_provider)

        providers_layout.addWidget(self.provider_combo)
        providers_group.setLayout(providers_layout)
        main_layout.addWidget(providers_group)

        # Grupo de modelos
        models_group = QGroupBox("Modelo")
        models_layout = QVBoxLayout()

        # ComboBox para seleção de modelos
        self.models_combo = QComboBox()

        # Carrega os modelos do provedor atual
        self.update_models_list()

        models_layout.addWidget(self.models_combo)
        models_group.setLayout(models_layout)
        main_layout.addWidget(models_group)

        # Grupo de opções de monitoramento
        monitor_group = QGroupBox("Opções de Monitoramento")
        monitor_layout = QVBoxLayout()

        # Checkbox para detectar qualquer alteração visual
        from PyQt5.QtWidgets import QCheckBox
        self.detect_any_change_checkbox = QCheckBox("Detectar qualquer alteração visual (não apenas mudanças de questão)")
        self.detect_any_change_checkbox.setChecked(self.detect_any_change)
        self.detect_any_change_checkbox.setToolTip("Ative esta opção para detectar qualquer alteração visual na tela, não apenas mudanças no número da questão.")

        monitor_layout.addWidget(self.detect_any_change_checkbox)
        monitor_group.setLayout(monitor_layout)
        main_layout.addWidget(monitor_group)

        # Botões de ação
        button_layout = QHBoxLayout()

        # Botão OK
        self.ok_button = QPushButton("OK")
        self.ok_button.clicked.connect(self.accept)

        # Botão Cancelar
        self.cancel_button = QPushButton("Cancelar")
        self.cancel_button.clicked.connect(self.reject)

        button_layout.addWidget(self.cancel_button)
        button_layout.addWidget(self.ok_button)

        main_layout.addLayout(button_layout)

        self.setLayout(main_layout)

    def update_provider(self, index):
        """Atualiza o provedor selecionado e recarrega a lista de modelos."""
        provider = self.provider_combo.itemData(index)
        if provider:
            self.current_provider = provider
            self.llm_manager.set_provider(provider)
            self.update_models_list()

    def update_models_list(self):
        """Atualiza a lista de modelos com base no provedor selecionado."""
        # Limpa o combo box
        self.models_combo.clear()

        # Obtém os modelos do provedor atual
        models = self.llm_manager.get_available_models()

        # Ordena os modelos por nome em ordem alfabética
        models.sort(key=lambda x: x['name'])

        selected_index = 0
        for i, model in enumerate(models):
            # Adiciona o modelo
            display_name = f"{model['name']}"
            self.models_combo.addItem(display_name, model['id'])

            # Define o tooltip com informações do modelo
            index = self.models_combo.count() - 1
            tooltip = f"ID: {model['id']}\n"
            tooltip += f"Descrição: {model['description']}"
            self.models_combo.setItemData(index, tooltip, Qt.ToolTipRole)

            # Seleciona o modelo atual
            if model['id'] == self.current_model_id:
                selected_index = index

        # Seleciona o modelo atual
        if selected_index > 0 and selected_index < self.models_combo.count():
            self.models_combo.setCurrentIndex(selected_index)

    def get_selected_model_id(self):
        """Retorna o ID do modelo selecionado."""
        index = self.models_combo.currentIndex()
        return self.models_combo.itemData(index)

    def get_selected_provider(self):
        """Retorna o provedor selecionado."""
        index = self.provider_combo.currentIndex()
        return self.provider_combo.itemData(index)

    def is_detect_any_change_enabled(self):
        """Retorna se a detecção de qualquer alteração visual está ativada."""
        return self.detect_any_change_checkbox.isChecked()
