"""
Script para criar imagens de anúncios de exemplo.
"""
import os
from PIL import Image, ImageDraw, ImageFont

def create_sample_ad(width, height, text, filename, bg_color=(240, 240, 240), text_color=(50, 50, 50)):
    """
    Cria uma imagem de anúncio de exemplo.
    
    Args:
        width: Largura da imagem
        height: Altura da imagem
        text: Texto a ser exibido na imagem
        filename: Nome do arquivo de saída
        bg_color: Co<PERSON> de fundo (RGB)
        text_color: Cor do texto (RGB)
    """
    # Cria uma nova imagem com fundo branco
    image = Image.new('RGB', (width, height), bg_color)
    draw = ImageDraw.Draw(image)
    
    # Desenha uma borda
    draw.rectangle([(0, 0), (width-1, height-1)], outline=(200, 200, 200), width=2)
    
    # Tenta carregar uma fonte, se não conseguir, usa a fonte padrão
    try:
        font = ImageFont.truetype("arial.ttf", 20)
        small_font = ImageFont.truetype("arial.ttf", 12)
    except IOError:
        font = ImageFont.load_default()
        small_font = ImageFont.load_default()
    
    # Adiciona o texto principal
    text_width = draw.textlength(text, font=font)
    text_position = ((width - text_width) // 2, height // 2 - 15)
    draw.text(text_position, text, font=font, fill=text_color)
    
    # Adiciona um texto secundário
    secondary_text = "Clique para mais informações"
    secondary_text_width = draw.textlength(secondary_text, font=small_font)
    secondary_text_position = ((width - secondary_text_width) // 2, height // 2 + 15)
    draw.text(secondary_text_position, secondary_text, font=small_font, fill=(100, 100, 200))
    
    # Salva a imagem
    if not os.path.exists('ads'):
        os.makedirs('ads')
    image.save(os.path.join('ads', filename))
    print(f"Imagem de anúncio criada: {filename}")

def main():
    """Função principal."""
    # Cria anúncios de exemplo para diferentes formatos
    create_sample_ad(320, 50, "Anúncio Banner", "banner_ad.png", bg_color=(230, 240, 255))
    create_sample_ad(300, 250, "Anúncio Retângulo", "rectangle_ad.png", bg_color=(255, 240, 230))
    create_sample_ad(728, 90, "Anúncio Leaderboard", "leaderboard_ad.png", bg_color=(240, 255, 230))
    create_sample_ad(160, 600, "Anúncio Skyscraper", "skyscraper_ad.png", bg_color=(255, 230, 240))
    create_sample_ad(234, 60, "Anúncio Pequeno", "small_ad.png", bg_color=(230, 255, 240))
    create_sample_ad(300, 250, "Anúncio Multiplex", "multiplex_ad.png", bg_color=(240, 230, 255))

if __name__ == "__main__":
    main()
