"""
Cliente para o Google Gemini.
"""
import os
import base64
from config import <PERSON><PERSON><PERSON><PERSON>, GEMINI_API_KEY
from llm_client_base import LLMClientBase

class GeminiClient(LLMClientBase):
    """
    Cliente para o Google Gemini, que oferece modelos com suporte a visão.
    """

    # Lista de modelos Gemini que suportam visão
    VISION_MODELS = [
        {
            "id": "gemini-pro-vision",
            "name": "Gemini Pro Vision",
            "description": "Modelo multimodal do Google com suporte a visão",
            "supports_vision": True,
            "context_length": 32000,
            "category": "vision"
        },
        {
            "id": "gemini-1.5-pro",
            "name": "Gemini 1.5 Pro",
            "description": "Modelo multimodal que suporta até 2 milhões de tokens",
            "supports_vision": True,
            "context_length": 2000000,
            "category": "vision"
        },
        {
            "id": "gemini-1.5-flash",
            "name": "Gemini 1.5 Flash",
            "description": "Modelo rápido e versátil para diversas tarefas",
            "supports_vision": True,
            "context_length": 1000000,
            "category": "vision"
        }
    ]

    def __init__(self, model_id="gemini-1.5-flash"):
        """
        Inicializa o cliente Gemini.

        Args:
            model_id: ID do modelo Gemini a ser usado
        """
        self.api_key = GEMINI_API_KEY
        self.model = model_id

        # Verifica se a API key está configurada
        self.is_available = self.api_key is not None and self.api_key != ""

        if not self.is_available and DEBUG:
            print("Aviso: API key do Gemini não configurada. Configure GEMINI_API_KEY no arquivo .env")

        # Importa a biblioteca do Gemini apenas se estiver disponível
        if self.is_available:
            try:
                import google.generativeai as genai
                self.genai = genai
                self.genai.configure(api_key=self.api_key)
                self.is_available = True
            except ImportError:
                if DEBUG:
                    print("Aviso: Biblioteca google-generativeai não instalada. Execute 'pip install google-generativeai'")
                self.is_available = False

    def process_image(self, image_path, prompt=None):
        """
        Processa uma imagem com o Gemini.

        Args:
            image_path: Caminho para a imagem
            prompt: Prompt opcional para enviar junto com a imagem

        Returns:
            Resposta do Gemini
        """
        if not self.is_available:
            return "Erro: API do Gemini não está disponível. Verifique se a API key está configurada."

        if not os.path.exists(image_path):
            return f"Erro: Arquivo de imagem não encontrado em {image_path}"

        try:
            # Prepara o prompt padrão se não for fornecido
            if not prompt:
                prompt = """
                Você é um professor especialista analisando uma questão de múltipla escolha.

                MODO DE RACIOCÍNIO ATIVADO:
                1. Identifique o assunto específico da questão na imagem.
                2. Analise recursivamente a lógica por trás de cada alternativa, questionando seu próprio raciocínio.
                3. Para questões que envolvem cálculos, mostre TODOS os passos intermediários detalhadamente.
                4. Após chegar a uma conclusão inicial, reavalie-a usando uma abordagem diferente para verificação.
                5. Avalie cada alternativa metodicamente, explicando por que as incorretas devem ser descartadas.
                6. Implemente um sistema de votação interna: gere três análises independentes e escolha a resposta mais frequente.

                FORMATO OBRIGATÓRIO DA RESPOSTA:
                - Comece SEMPRE com "Resposta correta: [LETRA]) [TEXTO DA ALTERNATIVA]"
                - Responda SEMPRE em português (PT-BR), mesmo que a pergunta esteja em inglês
                - Destaque claramente por que as outras alternativas estão incorretas
                - Seja direto e objetivo, priorizando a resposta correta

                Se você não conseguir ver a imagem ou a imagem não estiver clara, informe isso na sua resposta.
                """

            # Carrega a imagem
            with open(image_path, "rb") as image_file:
                image_data = image_file.read()

            if DEBUG:
                print(f"Enviando imagem para Gemini com o modelo {self.model}")

            # Configura o modelo com parâmetros otimizados para precisão
            generation_config = {
                "temperature": 0.05,  # Temperatura muito baixa para respostas mais determinísticas
                "top_p": 0.95,
                "top_k": 40,
                "max_output_tokens": 1500,  # Aumentado para permitir análise mais detalhada
                "candidate_count": 1,
                "stop_sequences": []
            }

            # Cria o modelo
            model = self.genai.GenerativeModel(
                model_name=self.model,
                generation_config=generation_config
            )

            # Prepara a imagem
            image_parts = [
                {"text": prompt},
                {"inline_data": {"mime_type": "image/jpeg", "data": base64.b64encode(image_data).decode("utf-8")}}
            ]

            # Gera a resposta
            response = model.generate_content(image_parts)

            # Extrai o texto da resposta
            answer = response.text

            return answer

        except Exception as e:
            error_msg = f"Erro ao processar imagem com Gemini: {str(e)}"
            if DEBUG:
                print(error_msg)
            return error_msg

    def get_available_models(self):
        """
        Retorna a lista de modelos disponíveis no Gemini.

        Returns:
            Lista de dicionários com informações dos modelos
        """
        return self.VISION_MODELS

    def set_model(self, model_id):
        """
        Define o modelo a ser usado.

        Args:
            model_id: ID do modelo

        Returns:
            True se o modelo foi definido com sucesso, False caso contrário
        """
        # Verifica se o modelo está disponível
        available_models = self.get_available_models()
        model_ids = [model["id"] for model in available_models]

        if model_id in model_ids:
            self.model = model_id
            return True
        else:
            # Se o modelo não estiver disponível, usa o primeiro da lista
            if available_models:
                self.model = available_models[0]["id"]
                if DEBUG:
                    print(f"Modelo {model_id} não disponível. Usando {self.model} como alternativa.")
                return True
            return False

    def get_provider_name(self):
        """
        Retorna o nome do provedor de LLM.

        Returns:
            Nome do provedor
        """
        return "Google Gemini"
