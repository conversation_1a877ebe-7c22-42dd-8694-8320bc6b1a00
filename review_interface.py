"""
Interface de revisão manual para validação de respostas do Python Question Helper.
Permite revisar questões processadas e marcar como corretas/incorretas.
"""

import sys
import os
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout,
                            QWidget, QPushButton, QLabel, QTextEdit, QComboBox,
                            QScrollArea, QFrame, QMessageBox, QProgressBar,
                            QSplitter, QGroupBox, QGridLayout, QLineEdit)
from PyQt5.QtCore import Qt, QThread, pyqtSignal
from PyQt5.QtGui import QPixmap, QFont, QIcon, QImage
import cv2
import numpy as np
from datetime import datetime
from question_logger import QuestionLogger

class ReviewInterface(QMainWindow):
    """Interface principal para revisão manual de questões."""

    def __init__(self):
        super().__init__()
        self.logger = QuestionLogger()
        self.current_questions = []
        self.current_index = 0

        self.init_ui()
        self.load_questions()

    def init_ui(self):
        """Inicializa a interface do usuário."""
        self.setWindowTitle("Python Question Helper - Revisão Manual")
        self.setGeometry(100, 100, 1400, 900)

        # Widget central
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # Layout principal
        main_layout = QHBoxLayout(central_widget)

        # Splitter para dividir a tela
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)

        # Painel esquerdo - Lista de questões
        self.create_questions_panel(splitter)

        # Painel direito - Detalhes da questão
        self.create_details_panel(splitter)

        # Define proporções
        splitter.setSizes([400, 1000])

        # Barra de status
        self.statusBar().showMessage("Pronto para revisão")

    def create_questions_panel(self, parent):
        """Cria o painel de lista de questões."""
        questions_widget = QWidget()
        layout = QVBoxLayout(questions_widget)

        # Título
        title = QLabel("Questões para Revisão")
        title.setFont(QFont("Arial", 14, QFont.Bold))
        layout.addWidget(title)

        # Filtros
        filters_group = QGroupBox("Filtros")
        filters_layout = QGridLayout(filters_group)

        # Filtro por domínio
        filters_layout.addWidget(QLabel("Domínio:"), 0, 0)
        self.domain_filter = QComboBox()
        self.domain_filter.addItems(["Todos", "legislacao", "sinalizacao", "direcao_defensiva",
                                   "primeiros_socorros", "mecanica", "meio_ambiente", "infrações"])
        self.domain_filter.currentTextChanged.connect(self.filter_questions)
        filters_layout.addWidget(self.domain_filter, 0, 1)

        # Filtro por confiança
        filters_layout.addWidget(QLabel("Confiança máxima:"), 1, 0)
        self.confidence_filter = QComboBox()
        self.confidence_filter.addItems(["100", "90", "80", "70", "60", "50"])
        self.confidence_filter.setCurrentText("70")
        self.confidence_filter.currentTextChanged.connect(self.filter_questions)
        filters_layout.addWidget(self.confidence_filter, 1, 1)

        layout.addWidget(filters_group)

        # Lista de questões
        self.questions_scroll = QScrollArea()
        self.questions_widget = QWidget()
        self.questions_layout = QVBoxLayout(self.questions_widget)
        self.questions_scroll.setWidget(self.questions_widget)
        self.questions_scroll.setWidgetResizable(True)
        layout.addWidget(self.questions_scroll)

        # Botões de ação
        buttons_layout = QHBoxLayout()

        refresh_btn = QPushButton("Atualizar")
        refresh_btn.clicked.connect(self.load_questions)
        buttons_layout.addWidget(refresh_btn)

        export_btn = QPushButton("Exportar")
        export_btn.clicked.connect(self.export_data)
        buttons_layout.addWidget(export_btn)

        layout.addLayout(buttons_layout)

        parent.addWidget(questions_widget)

    def create_details_panel(self, parent):
        """Cria o painel de detalhes da questão."""
        details_widget = QWidget()
        layout = QVBoxLayout(details_widget)

        # Título
        self.question_title = QLabel("Selecione uma questão para revisar")
        self.question_title.setFont(QFont("Arial", 16, QFont.Bold))
        layout.addWidget(self.question_title)

        # Informações da questão
        info_group = QGroupBox("Informações")
        info_layout = QGridLayout(info_group)

        self.info_labels = {}
        info_fields = ["Timestamp", "Domínio", "Tipo", "Provedor", "Modelo", "Confiança", "Tempo"]

        for i, field in enumerate(info_fields):
            info_layout.addWidget(QLabel(f"{field}:"), i, 0)
            label = QLabel("-")
            self.info_labels[field.lower()] = label
            info_layout.addWidget(label, i, 1)

        layout.addWidget(info_group)

        # Splitter vertical para texto e imagem
        content_splitter = QSplitter(Qt.Vertical)

        # Texto OCR
        ocr_group = QGroupBox("Texto Extraído (OCR)")
        ocr_layout = QVBoxLayout(ocr_group)
        self.ocr_text = QTextEdit()
        self.ocr_text.setMaximumHeight(150)
        self.ocr_text.setReadOnly(True)
        ocr_layout.addWidget(self.ocr_text)
        content_splitter.addWidget(ocr_group)

        # Resposta do LLM
        response_group = QGroupBox("Resposta do LLM")
        response_layout = QVBoxLayout(response_group)
        self.llm_response = QTextEdit()
        self.llm_response.setMaximumHeight(200)
        self.llm_response.setReadOnly(True)
        response_layout.addWidget(self.llm_response)
        content_splitter.addWidget(response_group)

        # Imagem da questão
        image_group = QGroupBox("Imagem da Questão")
        image_layout = QVBoxLayout(image_group)
        self.image_label = QLabel("Nenhuma imagem disponível")
        self.image_label.setAlignment(Qt.AlignCenter)
        self.image_label.setMinimumHeight(200)
        self.image_label.setStyleSheet("border: 1px solid gray;")
        image_layout.addWidget(self.image_label)
        content_splitter.addWidget(image_group)

        layout.addWidget(content_splitter)

        # Painel de validação
        validation_group = QGroupBox("Validação")
        validation_layout = QVBoxLayout(validation_group)

        # Botões de validação
        buttons_layout = QHBoxLayout()

        self.correct_btn = QPushButton("✓ Correto")
        self.correct_btn.setStyleSheet("background-color: #4CAF50; color: white; font-weight: bold;")
        self.correct_btn.clicked.connect(lambda: self.validate_question(True))
        buttons_layout.addWidget(self.correct_btn)

        self.incorrect_btn = QPushButton("✗ Incorreto")
        self.incorrect_btn.setStyleSheet("background-color: #f44336; color: white; font-weight: bold;")
        self.incorrect_btn.clicked.connect(lambda: self.validate_question(False))
        buttons_layout.addWidget(self.incorrect_btn)

        validation_layout.addLayout(buttons_layout)

        # Tipo de erro (se incorreto)
        error_layout = QHBoxLayout()
        error_layout.addWidget(QLabel("Tipo de erro:"))
        self.error_type = QComboBox()
        self.error_type.addItems([
            "Nenhum", "Alucinação", "Interpretação incorreta",
            "Conhecimento desatualizado", "Erro de cálculo",
            "Resposta incompleta", "Outro"
        ])
        error_layout.addWidget(self.error_type)
        validation_layout.addLayout(error_layout)

        # Notas do revisor
        validation_layout.addWidget(QLabel("Notas do revisor:"))
        self.reviewer_notes = QTextEdit()
        self.reviewer_notes.setMaximumHeight(80)
        validation_layout.addWidget(self.reviewer_notes)

        layout.addWidget(validation_group)

        # Navegação
        nav_layout = QHBoxLayout()

        self.prev_btn = QPushButton("← Anterior")
        self.prev_btn.clicked.connect(self.previous_question)
        nav_layout.addWidget(self.prev_btn)

        self.question_counter = QLabel("0 / 0")
        self.question_counter.setAlignment(Qt.AlignCenter)
        nav_layout.addWidget(self.question_counter)

        self.next_btn = QPushButton("Próximo →")
        self.next_btn.clicked.connect(self.next_question)
        nav_layout.addWidget(self.next_btn)

        layout.addLayout(nav_layout)

        parent.addWidget(details_widget)

    def load_questions(self):
        """Carrega questões que precisam de revisão."""
        try:
            confidence_threshold = int(self.confidence_filter.currentText())
            self.current_questions = self.logger.get_questions_for_review(
                limit=50, confidence_threshold=confidence_threshold
            )

            self.update_questions_list()
            self.current_index = 0
            self.display_current_question()

            self.statusBar().showMessage(f"Carregadas {len(self.current_questions)} questões para revisão")

        except Exception as e:
            QMessageBox.critical(self, "Erro", f"Erro ao carregar questões: {str(e)}")

    def filter_questions(self):
        """Aplica filtros às questões."""
        self.load_questions()

    def update_questions_list(self):
        """Atualiza a lista visual de questões."""
        # Limpa lista atual
        for i in reversed(range(self.questions_layout.count())):
            self.questions_layout.itemAt(i).widget().setParent(None)

        # Adiciona questões filtradas
        domain_filter = self.domain_filter.currentText()

        for i, question in enumerate(self.current_questions):
            if domain_filter != "Todos" and question['domain'] != domain_filter:
                continue

            item_widget = self.create_question_item(question, i)
            self.questions_layout.addWidget(item_widget)

        # Adiciona espaçador
        self.questions_layout.addStretch()

    def create_question_item(self, question, index):
        """Cria um item visual para uma questão."""
        frame = QFrame()
        frame.setFrameStyle(QFrame.Box)
        frame.setStyleSheet("QFrame { border: 1px solid #ccc; margin: 2px; }")

        layout = QVBoxLayout(frame)

        # Título
        title = f"#{index + 1} - {question['domain'].title()}"
        title_label = QLabel(title)
        title_label.setFont(QFont("Arial", 10, QFont.Bold))
        layout.addWidget(title_label)

        # Informações resumidas
        info = f"Confiança: {question['confidence'] or 'N/A'}% | {question['provider']}"
        info_label = QLabel(info)
        info_label.setStyleSheet("color: #666;")
        layout.addWidget(info_label)

        # Texto resumido
        text_preview = question['ocr_text'][:100] + "..." if len(question['ocr_text']) > 100 else question['ocr_text']
        text_label = QLabel(text_preview)
        text_label.setWordWrap(True)
        text_label.setStyleSheet("font-size: 9px;")
        layout.addWidget(text_label)

        # Torna clicável
        frame.mousePressEvent = lambda event, idx=index: self.select_question(idx)

        return frame

    def select_question(self, index):
        """Seleciona uma questão específica."""
        self.current_index = index
        self.display_current_question()

    def display_current_question(self):
        """Exibe os detalhes da questão atual."""
        if not self.current_questions or self.current_index >= len(self.current_questions):
            self.clear_display()
            return

        question = self.current_questions[self.current_index]

        # Atualiza título
        self.question_title.setText(f"Questão #{self.current_index + 1} - {question['domain'].title()}")

        # Atualiza informações
        self.info_labels['timestamp'].setText(question['timestamp'][:19])
        self.info_labels['domínio'].setText(question['domain'])
        self.info_labels['tipo'].setText(question['question_type'])
        self.info_labels['provedor'].setText(question['provider'])
        self.info_labels['modelo'].setText(question['model'])
        self.info_labels['confiança'].setText(f"{question['confidence'] or 'N/A'}%")
        self.info_labels['tempo'].setText("N/A")  # Não disponível na consulta atual

        # Atualiza textos
        self.ocr_text.setText(question['ocr_text'])
        self.llm_response.setText(question['llm_response'])

        # Carrega imagem se disponível
        self.load_question_image(question['image_path'])

        # Atualiza contador
        self.question_counter.setText(f"{self.current_index + 1} / {len(self.current_questions)}")

        # Atualiza botões de navegação
        self.prev_btn.setEnabled(self.current_index > 0)
        self.next_btn.setEnabled(self.current_index < len(self.current_questions) - 1)

        # Limpa campos de validação
        self.error_type.setCurrentIndex(0)
        self.reviewer_notes.clear()

    def clear_display(self):
        """Limpa a exibição quando não há questões."""
        self.question_title.setText("Nenhuma questão disponível")

        for label in self.info_labels.values():
            label.setText("-")

        self.ocr_text.clear()
        self.llm_response.clear()
        self.image_label.setText("Nenhuma imagem disponível")
        self.image_label.setPixmap(QPixmap())

        self.question_counter.setText("0 / 0")
        self.prev_btn.setEnabled(False)
        self.next_btn.setEnabled(False)

    def load_question_image(self, image_path):
        """Carrega e exibe a imagem da questão."""
        if not image_path or not os.path.exists(image_path):
            self.image_label.setText("Imagem não disponível")
            self.image_label.setPixmap(QPixmap())
            return

        try:
            # Carrega imagem com OpenCV
            image = cv2.imread(image_path)
            if image is None:
                self.image_label.setText("Erro ao carregar imagem")
                return

            # Converte para RGB
            image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

            # Redimensiona para caber na interface
            height, width, channel = image_rgb.shape
            max_height = 300
            if height > max_height:
                scale = max_height / height
                new_width = int(width * scale)
                image_rgb = cv2.resize(image_rgb, (new_width, max_height))

            # Converte para QPixmap
            height, width, channel = image_rgb.shape
            bytes_per_line = 3 * width
            q_image = QImage(image_rgb.data, width, height, bytes_per_line, QImage.Format_RGB888)
            pixmap = QPixmap.fromImage(q_image)

            self.image_label.setPixmap(pixmap)
            self.image_label.setText("")

        except Exception as e:
            self.image_label.setText(f"Erro ao carregar imagem: {str(e)}")

    def previous_question(self):
        """Navega para a questão anterior."""
        if self.current_index > 0:
            self.current_index -= 1
            self.display_current_question()

    def next_question(self):
        """Navega para a próxima questão."""
        if self.current_index < len(self.current_questions) - 1:
            self.current_index += 1
            self.display_current_question()

    def validate_question(self, is_correct):
        """Valida a questão atual."""
        if not self.current_questions or self.current_index >= len(self.current_questions):
            return

        question = self.current_questions[self.current_index]

        # Coleta dados de validação
        error_type = self.error_type.currentText() if not is_correct and self.error_type.currentIndex() > 0 else None
        reviewer_notes = self.reviewer_notes.toPlainText().strip() or None

        # Salva validação
        success = self.logger.mark_validation(
            question['hash'],
            is_correct,
            error_type,
            reviewer_notes
        )

        if success:
            # Feedback visual
            status_msg = "✓ Marcado como correto" if is_correct else "✗ Marcado como incorreto"
            self.statusBar().showMessage(status_msg, 3000)

            # Remove da lista atual
            self.current_questions.pop(self.current_index)

            # Ajusta índice se necessário
            if self.current_index >= len(self.current_questions):
                self.current_index = max(0, len(self.current_questions) - 1)

            # Atualiza exibição
            self.update_questions_list()
            self.display_current_question()

        else:
            QMessageBox.warning(self, "Erro", "Erro ao salvar validação")

    def export_data(self):
        """Exporta dados para análise."""
        try:
            from PyQt5.QtWidgets import QFileDialog

            filename, _ = QFileDialog.getSaveFileName(
                self,
                "Exportar Dados",
                f"question_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                "CSV Files (*.csv);;JSON Files (*.json)"
            )

            if filename:
                format_type = 'json' if filename.endswith('.json') else 'csv'
                success = self.logger.export_data(filename, format_type)

                if success:
                    QMessageBox.information(self, "Sucesso", f"Dados exportados para {filename}")
                else:
                    QMessageBox.warning(self, "Erro", "Erro ao exportar dados")

        except Exception as e:
            QMessageBox.critical(self, "Erro", f"Erro na exportação: {str(e)}")

def main():
    """Função principal para executar a interface de revisão."""
    app = QApplication(sys.argv)

    # Adiciona ícone se disponível
    try:
        app.setWindowIcon(QIcon("icon.png"))
    except:
        pass

    window = ReviewInterface()
    window.show()

    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
