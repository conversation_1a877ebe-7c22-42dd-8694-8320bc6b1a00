"""
Module for capturing screen content using simple screen capture methods.
"""
import time
import numpy as np
import cv2
import pyautogui
import tkinter as tk
from tkinter import messagebox, ttk
from PIL import Image, ImageTk
import screeninfo
from config import DEBUG

class ScreenCapture:
    def __init__(self):
        self.last_capture = None
        self.last_capture_time = 0
        self.selection_coords = None
        self.monitors = self.get_monitors()
        self.selected_monitor = None

    def get_monitors(self):
        """Get a list of all monitors."""
        try:
            monitors = screeninfo.get_monitors()
            return monitors
        except Exception as e:
            if DEBUG:
                print(f"Error getting monitors: {e}")
            return []

    def select_monitor(self):
        """Show a dialog to select a monitor."""
        if not self.monitors:
            messagebox.showerror("Error", "No monitors detected")
            return None

        # If only one monitor, select it automatically
        if len(self.monitors) == 1:
            self.selected_monitor = self.monitors[0]
            return self.selected_monitor

        # Create a dialog to select a monitor
        root = tk.Tk()
        root.withdraw()

        dialog = tk.Toplevel(root)
        dialog.title("Select Monitor")
        dialog.geometry("400x300")
        dialog.lift()
        dialog.attributes("-topmost", True)

        # Add a label
        tk.Label(dialog, text="Select a monitor to capture:").pack(pady=10)

        # Create a listbox with scrollbar
        frame = tk.Frame(dialog)
        scrollbar = tk.Scrollbar(frame)
        listbox = tk.Listbox(frame, width=60, height=10, yscrollcommand=scrollbar.set)
        scrollbar.config(command=listbox.yview)

        # Add monitors to the listbox
        for i, monitor in enumerate(self.monitors):
            listbox.insert(i, f"Monitor {i+1}: {monitor.width}x{monitor.height} at ({monitor.x}, {monitor.y})")

        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        frame.pack(pady=10, padx=10, fill=tk.BOTH, expand=True)

        # Variable to store the selected monitor
        selected_index = [None]

        # Function to handle selection
        def on_select():
            selection = listbox.curselection()
            if selection:
                selected_index[0] = selection[0]
                dialog.destroy()

        # Add a button to confirm selection
        tk.Button(dialog, text="Select", command=on_select).pack(pady=10)

        # Wait for the dialog to close
        dialog.wait_window()
        root.destroy()

        # Set the selected monitor
        if selected_index[0] is not None:
            self.selected_monitor = self.monitors[selected_index[0]]
            return self.selected_monitor

        return None

    def capture_full_screen(self):
        """Capture the entire screen or a specific monitor."""
        try:
            # Select a monitor if not already selected
            if not self.selected_monitor:
                if not self.select_monitor():
                    return None

            # Capture the selected monitor
            monitor = self.selected_monitor
            screenshot = pyautogui.screenshot(region=(monitor.x, monitor.y, monitor.width, monitor.height))
            img = np.array(screenshot)
            img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)

            self.last_capture = img
            self.last_capture_time = time.time()
            return img
        except Exception as e:
            if DEBUG:
                print(f"Error capturing full screen: {e}")
            return None

    def capture_screen_area(self):
        """Allow the user to select and capture a specific area of the screen."""
        try:
            # Select a monitor if not already selected
            if not self.selected_monitor:
                if not self.select_monitor():
                    return None

            # First, minimize our application window to see the screen
            root = tk.Tk()
            root.withdraw()

            # Create a transparent window for the selected monitor
            selection_window = tk.Toplevel(root)
            monitor = self.selected_monitor

            # Position the window on the selected monitor
            selection_window.geometry(f"{monitor.width}x{monitor.height}+{monitor.x}+{monitor.y}")
            selection_window.attributes('-alpha', 0.3)  # Semi-transparent
            selection_window.configure(background='grey')

            # Take a screenshot of the selected monitor
            monitor = self.selected_monitor
            screenshot = pyautogui.screenshot(region=(monitor.x, monitor.y, monitor.width, monitor.height))
            photo = ImageTk.PhotoImage(screenshot)

            # Create a canvas to display the screenshot and handle drawing
            canvas = tk.Canvas(selection_window, cursor="cross", highlightthickness=0)
            canvas.pack(fill=tk.BOTH, expand=True)
            canvas.create_image(0, 0, image=photo, anchor=tk.NW)

            # Variables to track selection
            start_x = start_y = end_x = end_y = 0
            selection_rect = None
            is_selecting = False

            # Instructions label
            instructions = tk.Label(
                selection_window,
                text="Click and drag to select an area. Press Enter to capture, Esc to cancel.",
                font=("Arial", 14),
                bg="black",
                fg="white",
                padx=10,
                pady=5
            )
            instructions.place(x=10, y=10)

            # Mouse event handlers
            def on_mouse_down(event):
                nonlocal start_x, start_y, selection_rect, is_selecting
                start_x, start_y = event.x, event.y
                if selection_rect:
                    canvas.delete(selection_rect)
                selection_rect = canvas.create_rectangle(
                    start_x, start_y, start_x, start_y,
                    outline="red", width=2
                )
                is_selecting = True

            def on_mouse_move(event):
                nonlocal end_x, end_y, selection_rect, is_selecting
                if is_selecting:
                    end_x, end_y = event.x, event.y
                    canvas.coords(selection_rect, start_x, start_y, end_x, end_y)

            def on_mouse_up(event):
                nonlocal end_x, end_y, is_selecting
                end_x, end_y = event.x, event.y
                is_selecting = False

            # Key event handlers
            def on_key_press(event):
                nonlocal start_x, start_y, end_x, end_y

                # Enter key - confirm selection
                if event.keysym == 'Return' and start_x != end_x and start_y != end_y:
                    # Store the selection coordinates
                    x1, y1 = min(start_x, end_x), min(start_y, end_y)
                    x2, y2 = max(start_x, end_x), max(start_y, end_y)
                    self.selection_coords = (x1, y1, x2, y2)
                    selection_window.destroy()

                # Escape key - cancel
                elif event.keysym == 'Escape':
                    self.selection_coords = None
                    selection_window.destroy()

            # Bind events
            canvas.bind("<ButtonPress-1>", on_mouse_down)
            canvas.bind("<B1-Motion>", on_mouse_move)
            canvas.bind("<ButtonRelease-1>", on_mouse_up)
            selection_window.bind("<Key>", on_key_press)

            # Wait for the window to close
            selection_window.focus_force()
            selection_window.wait_window()
            root.destroy()

            # Process the selection
            if self.selection_coords:
                x1, y1, x2, y2 = self.selection_coords
                # Adjust coordinates to account for monitor position
                monitor = self.selected_monitor
                abs_x1 = monitor.x + x1
                abs_y1 = monitor.y + y1
                width = x2 - x1
                height = y2 - y1
                # Capture the selected area
                screenshot = pyautogui.screenshot(region=(abs_x1, abs_y1, width, height))
                img = np.array(screenshot)
                img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)

                self.last_capture = img
                self.last_capture_time = time.time()
                return img

            return None

        except Exception as e:
            if DEBUG:
                print(f"Error capturing screen area: {e}")
            return None

    def save_capture(self, filename="capture.png"):
        """Save the last capture to a file."""
        if self.last_capture is not None:
            cv2.imwrite(filename, cv2.cvtColor(self.last_capture, cv2.COLOR_RGB2BGR))
            if DEBUG:
                print(f"Saved capture to {filename}")
            return True
        return False

# For testing
if __name__ == "__main__":
    capture = ScreenCapture()
    img = capture.capture_screen_area()
    if img is not None:
        capture.save_capture()
        print("Capture successful")
    else:
        print("Capture failed")
