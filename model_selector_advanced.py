"""
Módulo para seleção de modelos LLM com suporte a múltiplos provedores.
"""
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
                           QComboBox, QGroupBox, QCheckBox, QRadioButton, QButtonGroup)
from PyQt5.QtCore import QSettings, Qt
from llm_manager import LLMManager
from config import DEBUG

class ModelSelectorAdvancedDialog(QDialog):
    def __init__(self, parent=None, current_model_id=None, current_provider=None, detect_any_change=False):
        super().__init__(parent)
        self.setWindowTitle("Configurações Avançadas")
        self.resize(500, 500)  # Tamanho aumentado para acomodar mais opções

        # Inicializa o gerenciador de LLM
        self.llm_manager = LLMManager(current_provider)

        # Carrega as configurações
        self.settings = QSettings("AugmentCode", "PythonQuestionHelper")
        self.current_model_id = current_model_id or self.settings.value("llm_model", "")
        self.current_provider = current_provider or self.settings.value("llm_provider", "openrouter")
        self.detect_any_change = detect_any_change or self.settings.value("detect_any_change", False, type=bool)

        # Cria a interface
        self.create_ui()

    def create_ui(self):
        """Cria a interface do diálogo."""
        # Cria o layout principal
        main_layout = QVBoxLayout()

        # Título
        title = QLabel("Configurações Avançadas de LLM")
        title.setStyleSheet("font-weight: bold; font-size: 14px;")
        main_layout.addWidget(title)

        # Descrição
        desc = QLabel("Configure o provedor e o modelo de LLM para processar as questões:")
        desc.setWordWrap(True)
        main_layout.addWidget(desc)

        # Grupo de provedores
        providers_group = QGroupBox("Provedores de LLM")
        providers_layout = QVBoxLayout()

        # Adiciona os provedores disponíveis
        self.provider_combo = QComboBox()
        available_providers = self.llm_manager.get_available_providers()

        # Mapeia os nomes dos provedores para exibição
        provider_names = {
            "openrouter": "OpenRouter (Online)",
            "huggingface": "Hugging Face (Online)",
            "ollama": "Ollama (Local)",
            "gemini": "Google Gemini (Online)"
        }

        # Adiciona os provedores ao combo box
        for provider in available_providers:
            display_name = provider_names.get(provider, provider.capitalize())
            self.provider_combo.addItem(display_name, provider)

            # Seleciona o provedor atual
            if provider == self.current_provider:
                self.provider_combo.setCurrentIndex(self.provider_combo.count() - 1)

        # Conecta o sinal de mudança de seleção
        self.provider_combo.currentIndexChanged.connect(self.update_provider)

        providers_layout.addWidget(QLabel("Selecione o provedor de LLM:"))
        providers_layout.addWidget(self.provider_combo)

        # Adiciona informações sobre cada provedor
        provider_info = QLabel(
            "<b>Hugging Face:</b> Plataforma de IA que oferece acesso a milhares de modelos. Requer API key (já configurada).<br>"
            "<b>OpenRouter:</b> Serviço online que oferece acesso a vários modelos LLM. Requer API key.<br>"
            "<b>Ollama:</b> Executa modelos LLM localmente no seu computador. Não requer API key, mas precisa ser instalado.<br>"
            "<b>Google Gemini:</b> Serviço da Google que oferece modelos com suporte a visão. Requer API key."
        )
        provider_info.setWordWrap(True)
        provider_info.setStyleSheet("font-size: 10pt; color: #333333; background-color: #f0f0f0; padding: 5px; border-radius: 3px;")
        providers_layout.addWidget(provider_info)

        providers_group.setLayout(providers_layout)
        main_layout.addWidget(providers_group)

        # Grupo de modelos
        provider_name = self.llm_manager.get_provider_name()
        self.models_group = QGroupBox(f"Modelos Disponíveis ({provider_name})")
        self.models_layout = QVBoxLayout()

        # Adiciona um rótulo para mostrar o provedor atual
        provider_label = QLabel(f"<b>Provedor atual:</b> {provider_name}")
        provider_label.setStyleSheet("font-size: 11pt; color: #0066cc;")
        self.models_layout.addWidget(provider_label)

        # ComboBox para seleção de modelos
        self.models_combo = QComboBox()

        # Carrega os modelos do provedor atual
        self.update_models_list()

        # Adiciona um rótulo para mostrar informações do modelo selecionado
        self.model_info_label = QLabel("Selecione um modelo para ver suas informações")
        self.model_info_label.setWordWrap(True)
        self.model_info_label.setStyleSheet("font-size: 10pt; color: #333333; background-color: #f0f0f0; padding: 5px; border-radius: 3px;")

        # Conecta o sinal de mudança de seleção
        self.models_combo.currentIndexChanged.connect(self.update_model_info)

        self.models_layout.addWidget(QLabel("Selecione o modelo LLM:"))
        self.models_layout.addWidget(self.models_combo)
        self.models_layout.addWidget(self.model_info_label)
        self.models_group.setLayout(self.models_layout)
        main_layout.addWidget(self.models_group)

        # Opções de monitoramento
        self.monitor_group = QGroupBox("Opções de Monitoramento")
        monitor_layout = QVBoxLayout()

        # Checkbox para detectar qualquer alteração visual
        self.detect_any_change_check = QCheckBox("Detectar qualquer alteração visual (não apenas mudança de questão)")
        self.detect_any_change_check.setChecked(self.detect_any_change)
        self.detect_any_change_check.setToolTip("Quando ativado, o sistema detectará qualquer alteração visual na tela, não apenas mudanças de questão")

        # Explicação sobre a detecção de alterações
        detect_label = QLabel("Por padrão, o sistema detecta apenas mudanças de questão (ex: Questão 7 → Questão 8). Ative esta opção se quiser detectar qualquer alteração visual.")
        detect_label.setWordWrap(True)

        monitor_layout.addWidget(self.detect_any_change_check)
        monitor_layout.addWidget(detect_label)

        self.monitor_group.setLayout(monitor_layout)
        main_layout.addWidget(self.monitor_group)

        # Botões de ação
        button_layout = QHBoxLayout()

        # Botão OK
        self.ok_button = QPushButton("OK")
        self.ok_button.clicked.connect(self.accept)

        # Botão Cancelar
        self.cancel_button = QPushButton("Cancelar")
        self.cancel_button.clicked.connect(self.reject)

        button_layout.addWidget(self.cancel_button)
        button_layout.addWidget(self.ok_button)

        main_layout.addLayout(button_layout)

        self.setLayout(main_layout)

    def update_provider(self, index):
        """Atualiza o provedor selecionado e recarrega a lista de modelos."""
        provider = self.provider_combo.itemData(index)
        if provider:
            self.current_provider = provider
            self.llm_manager.set_provider(provider)
            self.models_group.setTitle(f"Modelos Disponíveis ({self.llm_manager.get_provider_name()})")
            self.update_models_list()

    def update_models_list(self):
        """Atualiza a lista de modelos com base no provedor selecionado."""
        # Limpa o combo box
        self.models_combo.clear()

        # Obtém os modelos do provedor atual
        models = self.llm_manager.get_available_models()

        # Ordena os modelos por nome em ordem alfabética
        models.sort(key=lambda x: x['name'])

        # Adiciona os modelos ao combo box
        self.models_combo.addItem(f"--- Selecione um Modelo ({len(models)} disponíveis) ---", None)

        selected_index = 0
        for i, model in enumerate(models):
            # Adiciona o modelo
            display_name = f"{model['name']}"
            if ":free" in model.get('id', ''):
                display_name += " (Gratuito)"
            self.models_combo.addItem(display_name, model['id'])

            # Define o tooltip com informações do modelo
            index = self.models_combo.count() - 1
            tooltip = f"ID: {model['id']}\n"
            tooltip += f"Descrição: {model['description']}\n"
            tooltip += f"Tamanho do contexto: {model['context_length']} tokens\n"
            tooltip += f"Provedor: {model.get('provider', self.llm_manager.get_provider_name())}"
            self.models_combo.setItemData(index, tooltip, Qt.ToolTipRole)

            # Seleciona o modelo atual
            if model['id'] == self.current_model_id:
                selected_index = index

        # Seleciona o modelo atual
        if selected_index > 0:
            self.models_combo.setCurrentIndex(selected_index)

    def update_model_info(self, index):
        """Atualiza as informações do modelo selecionado."""
        # Obtém o ID do modelo selecionado
        model_id = self.models_combo.itemData(index)

        if not model_id:
            # Se for um item de cabeçalho, limpa as informações
            self.model_info_label.setText("Selecione um modelo para ver suas informações")
            return

        # Obtém o tooltip do item selecionado
        tooltip = self.models_combo.itemData(index, Qt.ToolTipRole)

        if tooltip:
            # Formata o texto para exibição
            info_text = tooltip.replace("\n", "<br>")
            self.model_info_label.setText(f"<b>Informações do modelo:</b><br>{info_text}")
        else:
            self.model_info_label.setText("Informações não disponíveis para este modelo")

    def get_selected_model_id(self):
        """Retorna o ID do modelo selecionado."""
        index = self.models_combo.currentIndex()
        return self.models_combo.itemData(index)

    def get_selected_provider(self):
        """Retorna o provedor selecionado."""
        index = self.provider_combo.currentIndex()
        return self.provider_combo.itemData(index)

    def is_detect_any_change_enabled(self):
        """Retorna se a detecção de qualquer alteração visual está ativada."""
        return self.detect_any_change_check.isChecked()
