"""
Módulo para monitorar mudanças na tela e detectar quando o usuário muda de questão.
"""
import time
import threading
import cv2
import numpy as np
import win32gui
from screen_capture_simple import ScreenCapture
from config import DEBUG

class ScreenMonitor:
    def __init__(self, callback_on_change=None):
        """
        Inicializa o monitor de tela.

        Args:
            callback_on_change: Função a ser chamada quando uma mudança significativa for detectada
        """
        self.screen_capture = ScreenCapture()
        self.callback_on_change = callback_on_change
        self.monitoring = False
        self.monitor_thread = None
        self.last_image = None
        self.last_image_hash = None
        self.window_handle = None
        self.change_threshold = 0.001  # Reduzido ainda mais para ser extremamente sensível a mudanças
        self.check_interval = 3.0  # Reduzido para verificar com mais frequência
        self.last_question_number = None  # Armazena o último número de questão detectado
        self.paused = False  # Flag para pausar temporariamente o monitoramento
        self.detect_any_change = False  # Flag para detectar qualquer alteração visual, não apenas mudanças de questão

    def start_monitoring(self, window_handle=None):
        """
        Inicia o monitoramento da tela.

        Args:
            window_handle: Handle da janela a ser monitorada (opcional)
        """
        try:
            if self.monitoring:
                if DEBUG:
                    print("Monitoramento já está ativo")
                return False

            print(f"Iniciando monitoramento com handle: {window_handle}")

            self.window_handle = window_handle
            if window_handle:
                self.screen_capture.window_handle = window_handle
                try:
                    title = self.screen_capture.get_window_title(window_handle)
                    self.screen_capture.window_title = title
                    print(f"Título da janela: {title}")
                except Exception as e:
                    print(f"Erro ao obter título da janela: {e}")

            # Captura a imagem inicial
            print("Tentando capturar imagem inicial...")
            initial_image = self.capture_current_screen()
            if initial_image is None:
                print("Falha ao capturar imagem inicial")
                return False

            print(f"Imagem inicial capturada: {initial_image.shape}")
            self.last_image = initial_image

            print("Calculando hash da imagem...")
            try:
                self.last_image_hash = self.compute_image_hash(initial_image)
                print("Hash calculado com sucesso")
            except Exception as e:
                print(f"Erro ao calcular hash: {e}")
                return False

            # Inicia o thread de monitoramento
            print("Iniciando thread de monitoramento...")
            self.monitoring = True
            self.monitor_thread = threading.Thread(target=self._monitor_loop)
            self.monitor_thread.daemon = True
            self.monitor_thread.start()

            print("Monitoramento iniciado com sucesso")
            return True

        except Exception as e:
            print(f"Erro ao iniciar monitoramento: {e}")
            import traceback
            traceback.print_exc()
            return False

    def stop_monitoring(self):
        """
        Para o monitoramento da tela.
        """
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=1.0)
            self.monitor_thread = None

        if DEBUG:
            print("Monitoramento parado")

    def capture_current_screen(self):
        """
        Captura a tela atual com tratamento de erros robusto.

        Returns:
            A imagem capturada ou None se falhar
        """
        try:
            if not self.window_handle:
                print("Nenhum handle de janela definido, selecionando janela...")
                if not self.screen_capture.select_window():
                    print("Falha ao selecionar janela")
                    return None
                self.window_handle = self.screen_capture.window_handle
                print(f"Janela selecionada: {self.screen_capture.window_title}")

            # Verifica se a janela ainda existe
            try:
                if not win32gui.IsWindow(self.window_handle):
                    print("A janela não existe mais, selecionando nova janela...")
                    if not self.screen_capture.select_window():
                        print("Falha ao selecionar nova janela")
                        return None
                    self.window_handle = self.screen_capture.window_handle
            except Exception as e:
                print(f"Erro ao verificar janela: {e}")
                # Tenta selecionar uma nova janela
                if not self.screen_capture.select_window():
                    print("Falha ao selecionar nova janela após erro")
                    return None
                self.window_handle = self.screen_capture.window_handle

            # Atualiza o handle da janela no screen_capture
            self.screen_capture.window_handle = self.window_handle

            # Tenta capturar a janela
            print(f"Tentando capturar janela com handle {self.window_handle}...")
            image = self.screen_capture.capture_window()

            if image is None:
                print("Falha na captura, tentando novamente com seleção de janela...")
                # Se falhar, tenta selecionar a janela novamente
                if not self.screen_capture.select_window():
                    print("Falha ao selecionar janela após falha na captura")
                    return None
                self.window_handle = self.screen_capture.window_handle
                image = self.screen_capture.capture_window()

            if image is not None:
                print(f"Captura bem-sucedida: {image.shape}")
            else:
                print("Todas as tentativas de captura falharam")

            return image

        except Exception as e:
            print(f"Erro ao capturar tela: {e}")
            import traceback
            traceback.print_exc()
            return None

    def compute_image_hash(self, image):
        """
        Calcula um hash simples da imagem para comparação rápida.

        Args:
            image: A imagem para calcular o hash

        Returns:
            Um hash da imagem
        """
        try:
            # Redimensiona para uma resolução menor para comparação mais rápida
            small = cv2.resize(image, (32, 32))
            # Converte para escala de cinza
            if len(small.shape) == 3:  # Se a imagem for colorida
                gray = cv2.cvtColor(small, cv2.COLOR_BGR2GRAY)
            else:  # Se a imagem já estiver em escala de cinza
                gray = small

            # Método alternativo de hash (mais simples e robusto)
            # Calcula a média dos pixels
            avg = gray.mean()
            # Cria um hash binário baseado em pixels acima ou abaixo da média
            hash_value = np.zeros((8, 8), dtype=np.uint8)
            for i in range(8):
                for j in range(8):
                    y = i * 4
                    x = j * 4
                    block = gray[y:y+4, x:x+4]
                    hash_value[i, j] = 1 if block.mean() >= avg else 0

            return hash_value

        except Exception as e:
            print(f"Erro no cálculo do hash: {e}")
            # Retorna um hash vazio em caso de erro
            return np.zeros((8, 8), dtype=np.uint8)

    def compute_image_difference(self, image1, image2):
        """
        Calcula a diferença entre duas imagens.

        Args:
            image1: Primeira imagem
            image2: Segunda imagem

        Returns:
            Porcentagem de diferença entre as imagens (0.0 a 1.0)
        """
        try:
            # Calcula os hashes das imagens
            hash1 = self.compute_image_hash(image1)
            hash2 = self.compute_image_hash(image2)

            # Calcula a distância de Hamming (número de bits diferentes)
            # Convertendo para cálculo manual já que não estamos mais usando cv2.img_hash
            distance = np.sum(hash1 != hash2)

            # Normaliza para 0.0-1.0
            max_distance = hash1.size  # Número total de bits no hash (8x8 = 64)
            difference = distance / max_distance

            print(f"Diferença de imagem: {difference:.4f} (distância: {distance}/{max_distance})")

            return difference

        except Exception as e:
            print(f"Erro ao calcular diferença entre imagens: {e}")
            # Retorna um valor alto para forçar uma nova captura em caso de erro
            return 1.0

    def detect_question_number(self, image):
        """
        Detecta o número da questão na imagem usando OCR.

        Args:
            image: A imagem capturada

        Returns:
            O número da questão detectado ou None se não for encontrado
        """
        try:
            import pytesseract
            from config import TESSERACT_PATH

            # Configura o caminho do Tesseract
            if TESSERACT_PATH:
                pytesseract.pytesseract.tesseract_cmd = TESSERACT_PATH

            # Converte para escala de cinza
            gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)

            # Aplica threshold para melhorar o OCR
            _, thresh1 = cv2.threshold(gray, 150, 255, cv2.THRESH_BINARY_INV)

            # Tenta outro threshold para aumentar as chances de detecção
            _, thresh2 = cv2.threshold(gray, 127, 255, cv2.THRESH_BINARY)

            # Extrai o texto de ambas as imagens processadas
            text1 = pytesseract.image_to_string(thresh1, lang='por', config='--psm 6')
            text2 = pytesseract.image_to_string(thresh2, lang='por', config='--psm 6')
            text3 = pytesseract.image_to_string(gray, lang='por', config='--psm 6')

            # Combina os textos para aumentar as chances de encontrar o número da questão
            text = text1 + " " + text2 + " " + text3

            # Procura por padrões de número de questão
            import re
            patterns = [
                r'[Qq]uest[aãoõ]+\s*(\d+)',  # Questão X
                r'[Qq]uest[aãoõ]+\s*[nN][oº°]\s*(\d+)',  # Questão Nº X
                r'[Qq]uest[aãoõ]+\s*[nN][uú]mero\s*(\d+)',  # Questão Número X
                r'[Qq]\s*(\d+)',  # Q X
                r'[Qq][uU][eE][sS][tT][aãoõ]+\s*(\d+)',  # QUESTAO X (maiúsculas)
                r'(\d+)\s*[\.:\-\)]\s*[A-Za-z]',  # X. Texto (formato de lista numerada)
                r'[Pp]ergunta\s*(\d+)',  # Pergunta X
                r'[Pp]roblema\s*(\d+)',  # Problema X
                r'[Ee]xercise\s*(\d+)',  # Exercise X (inglês)
                r'[Qq]uestion\s*(\d+)',  # Question X (inglês)
                r'(\d+)[\/\.]',  # Números seguidos de / ou . (comum em numeração de questões)
                r'[^\d](\d+)[^\d]',  # Números isolados (cercados por não-dígitos)
            ]

            for pattern in patterns:
                matches = re.findall(pattern, text)
                if matches:
                    question_number = int(matches[0])
                    print(f"Número de questão detectado: {question_number}")
                    return question_number

            return None

        except Exception as e:
            print(f"Erro ao detectar número de questão: {e}")
            return None

    def _monitor_loop(self):
        """
        Loop principal de monitoramento.
        """
        print("Iniciando loop de monitoramento...")

        # Contador para verificações periódicas
        check_count = 0

        while self.monitoring:
            try:
                # Aguarda o intervalo de verificação
                time.sleep(self.check_interval)

                # Verifica se o monitoramento está pausado
                if self.paused:
                    print("Monitoramento em pausa, aguardando retomada...")
                    time.sleep(1)  # Espera um pouco antes de verificar novamente
                    continue

                # Incrementa o contador
                check_count += 1

                # Log periódico para confirmar que o monitoramento está ativo
                if check_count % 3 == 0:  # A cada 3 verificações (15 segundos com intervalo de 5s)
                    print(f"Monitoramento ativo - verificação #{check_count}")

                # Captura a tela atual
                print("Capturando tela atual...")
                current_image = self.capture_current_screen()
                if current_image is None:
                    print("Falha ao capturar tela atual durante monitoramento")
                    continue

                print(f"Tela capturada: {current_image.shape}")

                # Detecta o número da questão
                current_question_number = self.detect_question_number(current_image)

                # Verifica se o número da questão mudou
                question_changed = False
                if current_question_number is not None and self.last_question_number is not None:
                    if current_question_number != self.last_question_number:
                        print(f"MUDANÇA DE QUESTÃO DETECTADA: {self.last_question_number} -> {current_question_number}")
                        question_changed = True
                        self.last_question_number = current_question_number
                elif current_question_number is not None:
                    self.last_question_number = current_question_number

                # Calcula a diferença de imagem
                print("Calculando diferença entre imagens...")
                difference = self.compute_image_difference(self.last_image, current_image)

                # Simplificando a lógica de detecção de mudanças
                should_trigger_change = False

                # Verifica se o número da questão mudou (prioridade máxima)
                if question_changed:
                    print(f"MUDANÇA DE QUESTÃO DETECTADA")
                    should_trigger_change = True
                # Caso contrário, verifica a diferença visual apenas se a detecção de qualquer mudança estiver ativada
                elif self.detect_any_change and difference > self.change_threshold:
                    print(f"MUDANÇA SIGNIFICATIVA DETECTADA: {difference:.4f} (limite: {self.change_threshold})")
                    should_trigger_change = True

                # Se deve acionar a mudança
                if should_trigger_change:
                    # Salva as imagens para depuração
                    cv2.imwrite("last_image.png", cv2.cvtColor(self.last_image, cv2.COLOR_RGB2BGR))
                    cv2.imwrite("current_image.png", cv2.cvtColor(current_image, cv2.COLOR_RGB2BGR))

                    # Atualiza a última imagem
                    self.last_image = current_image.copy()
                    self.last_image_hash = self.compute_image_hash(current_image)

                    # Chama o callback se existir
                    if self.callback_on_change:
                        print("Chamando callback de mudança...")
                        # Pausa o monitoramento enquanto processa a mudança
                        self.pause_monitoring()
                        self.callback_on_change(current_image)
                    else:
                        print("Nenhum callback definido para mudanças")
                else:
                    print(f"Nenhuma mudança significativa detectada: {difference:.4f} (limite: {self.change_threshold})")
                    if current_question_number is not None:
                        print(f"Número da questão atual: {current_question_number}")

            except Exception as e:
                print(f"Erro no loop de monitoramento: {e}")
                import traceback
                traceback.print_exc()

        print("Loop de monitoramento encerrado")

    def pause_monitoring(self):
        """
        Pausa temporariamente o monitoramento sem interromper o thread.
        """
        self.paused = True
        print("Monitoramento pausado temporariamente")

    def resume_monitoring(self):
        """
        Retoma o monitoramento após uma pausa.
        """
        self.paused = False
        print("Monitoramento retomado")

    def is_monitoring(self):
        """
        Verifica se o monitoramento está ativo.

        Returns:
            True se o monitoramento estiver ativo, False caso contrário
        """
        return self.monitoring

    def is_paused(self):
        """
        Verifica se o monitoramento está pausado.

        Returns:
            True se o monitoramento estiver pausado, False caso contrário
        """
        return self.paused

    def set_detect_any_change(self, detect_any_change):
        """
        Define se o monitor deve detectar qualquer alteração visual ou apenas mudanças de questão.

        Args:
            detect_any_change: True para detectar qualquer alteração, False para detectar apenas mudanças de questão
        """
        self.detect_any_change = detect_any_change
        print(f"Modo de detecção alterado: {'Qualquer alteração visual' if detect_any_change else 'Apenas mudanças de questão'}")

    def is_detect_any_change_enabled(self):
        """
        Verifica se a detecção de qualquer alteração visual está ativada.

        Returns:
            True se a detecção de qualquer alteração estiver ativada, False caso contrário
        """
        return self.detect_any_change

# Para testes
if __name__ == "__main__":
    def on_screen_change(image):
        print("Mudança de tela detectada!")
        cv2.imwrite("changed_screen.png", cv2.cvtColor(image, cv2.COLOR_RGB2BGR))

    monitor = ScreenMonitor(callback_on_change=on_screen_change)
    monitor.start_monitoring()

    try:
        print("Monitorando mudanças na tela. Pressione Ctrl+C para sair.")
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("Parando monitoramento...")
    finally:
        monitor.stop_monitoring()
