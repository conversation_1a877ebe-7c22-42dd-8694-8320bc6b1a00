# Python Question Helper

Um assistente inteligente para análise de questões usando visão computacional e inteligência artificial.

## 📋 Descrição

O Python Question Helper é uma ferramenta educacional que utiliza tecnologias de visão computacional e inteligência artificial para auxiliar no estudo e compreensão de questões. A aplicação captura a tela, extrai o texto usando OCR (Reconhecimento Óptico de Caracteres) e utiliza modelos de linguagem avançados para fornecer explicações detalhadas.

### Principais Funcionalidades

- **Captura de Tela Inteligente**: Captura automaticamente a janela selecionada
- **Monitoramento Automático**: Detecta mudanças na tela e processa novas questões automaticamente
- **Extração de Texto**: Utiliza OCR para extrair texto das imagens capturadas
- **Análise por IA**: Processa o conteúdo usando modelos de linguagem avançados
- **Múltiplos Provedores de IA**: Suporte para Google Gemini, OpenRouter, Hugging Face e mais
- **Fallback Automático**: Alterna automaticamente entre modelos e provedores em caso de erros

## ⚠️ Aviso Importante

**Este software é fornecido APENAS para fins educacionais e de estudo.**

- **NÃO** utilize esta ferramenta para trapacear em exames, provas ou qualquer tipo de avaliação
- **NÃO** utilize em ambientes onde seu uso não é permitido
- O desenvolvedor **NÃO** se responsabiliza por qualquer uso indevido, bloqueio de conta, banimento ou outras consequências resultantes do uso inadequado desta ferramenta
- Esta ferramenta foi criada para auxiliar no processo de aprendizagem, permitindo uma melhor compreensão das questões e conceitos
- Respeite sempre os termos de uso das plataformas e instituições educacionais

## 🚀 Instalação

### Pré-requisitos

- Python 3.8 ou superior
- Tesseract OCR instalado no sistema
- Chaves de API para os serviços de IA (opcional, mas recomendado)

### Passos para Instalação

1. Clone o repositório:

   ```bash
   git clone https://github.com/seu-usuario/python-question-helper.git
   cd python-question-helper
   ```

2. Instale as dependências:

   ```bash
   pip install -r requirements.txt
   ```

3. Instale o Tesseract OCR:

   - **Windows**: Baixe o instalador em [https://github.com/UB-Mannheim/tesseract/wiki](https://github.com/UB-Mannheim/tesseract/wiki)
   - **Linux**: `sudo apt install tesseract-ocr`
   - **macOS**: `brew install tesseract`

4. Configure as chaves de API (opcional):
   - Crie um arquivo `.env` na raiz do projeto
   - Adicione suas chaves de API no formato:
     ```
     OPENROUTER_API_KEY=sua_chave_aqui
     GEMINI_API_KEY=sua_chave_aqui
     HUGGINGFACE_API_KEY=sua_chave_aqui
     ```

## 🔧 Configuração

Edite o arquivo `config.py` para configurar:

- Caminho para o Tesseract OCR
- Provedor de IA padrão
- Outras configurações personalizadas

## 💻 Uso

1. Execute o programa:

   ```bash
   python main.py
   ```

2. Na interface:
   - Clique em "Selecionar Modelo" para escolher um modelo de IA
   - Clique em "Iniciar Monitoramento" e selecione a janela com as questões
   - O sistema processará automaticamente a primeira imagem
   - Quando a questão mudar, o sistema detectará e processará automaticamente

## 🧩 Recursos Adicionais

- **Detecção de Contexto**: Identifica automaticamente o tipo de questão (matemática, física, etc.)
- **Prompts Especializados**: Adapta o prompt para cada tipo específico de questão
- **Alternância Automática**: Muda automaticamente entre modelos e provedores em caso de erros
- **Formatação de Respostas**: Garante que as respostas sigam um formato consistente e claro

## 🔄 Provedores de IA Suportados

- Google Gemini
- OpenRouter (acesso a vários modelos como GPT-4, Claude, etc.)
- Hugging Face
- Ollama (para modelos locais)

## 🤝 Contribuição

Contribuições são bem-vindas! Sinta-se à vontade para abrir issues ou enviar pull requests.

## 📜 Licença

Este projeto está licenciado sob a licença MIT - veja o arquivo [LICENSE](LICENSE) para detalhes.

## 📞 Contato

Para dúvidas ou sugestões, entre em contato através de [<EMAIL>](mailto:<EMAIL>).

---

**Desenvolvido por BoZolinO**

_Lembre-se: Use esta ferramenta com responsabilidade e ética._
