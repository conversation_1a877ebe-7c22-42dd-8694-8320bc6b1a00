# Python Question Helper

<div align="center">

![Python Question Helper Logo](https://img.shields.io/badge/Python-Question%20Helper-blue?style=for-the-badge&logo=python)

**Sistema Inteligente de Assistência para Questões**

[![Version](https://img.shields.io/badge/version-1.0.0-green.svg)](https://github.com/hiagodrigo/Python_Question_Helper)
[![License](https://img.shields.io/badge/license-Commercial-red.svg)](LICENSE)
[![Python](https://img.shields.io/badge/python-3.8+-blue.svg)](https://python.org)
[![Platform](https://img.shields.io/badge/platform-Windows%20%7C%20Linux%20%7C%20macOS-lightgrey.svg)](https://github.com/hiagodrigo/Python_Question_Helper)

_Desenvolvido por **BoZolinO** para a comunidade educacional brasileira_

[🚀 Instalação](#-instalação) • [📖 Documentação](#-documentação) • [🎯 Funcionalidades](#-funcionalidades) • [🤝 Suporte](#-suporte)

</div>

---

## 📋 **Visão Geral**

O **Python Question Helper** é um sistema avançado de assistência inteligente que combina **OCR (Reconhecimento Óptico de Caracteres)**, **Inteligência Artificial** e **Análise de Padrões** para auxiliar na resolução de questões educacionais.

### 🎯 **Principais Diferenciais**

- **🔍 OCR Avançado**: Múltiplas abordagens para máxima precisão na extração de texto
- **🤖 IA Multimodal**: Suporte a modelos com visão computacional (GPT-4V, Gemini, LLaVA)
- **📊 Sistema de Análise**: Logging estruturado com detecção de alucinação e melhoria contínua
- **🔄 Fallback Inteligente**: Troca automática entre provedores para máxima disponibilidade
- **⚡ Monitoramento Automático**: Detecção de mudanças na tela com processamento automático
- **🇧🇷 Otimizado para Brasil**: Interface e respostas em português brasileiro

### ⚠️ **Aviso Importante**

**Este software é fornecido APENAS para fins educacionais e de estudo.**

- **NÃO** utilize esta ferramenta para trapacear em exames, provas ou qualquer tipo de avaliação
- **NÃO** utilize em ambientes onde seu uso não é permitido
- O desenvolvedor **NÃO** se responsabiliza por qualquer uso indevido
- Esta ferramenta foi criada para auxiliar no processo de aprendizagem
- Respeite sempre os termos de uso das plataformas e instituições educacionais

## 🚀 **Instalação**

### **Pré-requisitos**

| Componente              | Versão Mínima                           | Descrição                         |
| ----------------------- | --------------------------------------- | --------------------------------- |
| **Python**              | 3.8+                                    | Linguagem de programação          |
| **Tesseract OCR**       | 4.0+                                    | Engine de reconhecimento de texto |
| **Sistema Operacional** | Windows 10+ / Ubuntu 18+ / macOS 10.14+ | Plataforma suportada              |

### **1. Instalação do Tesseract OCR**

#### Windows

```bash
# Baixe o instalador oficial
https://github.com/UB-Mannheim/tesseract/wiki

# Durante a instalação, certifique-se de incluir:
# - Português (por)
# - Inglês (eng)
```

#### Linux (Ubuntu/Debian)

```bash
sudo apt update
sudo apt install tesseract-ocr tesseract-ocr-por tesseract-ocr-eng
```

#### macOS

```bash
brew install tesseract tesseract-lang
```

### **2. Instalação do Python Question Helper**

#### Método 1: Instalação Básica

```bash
# Clone o repositório
git clone https://github.com/hiagodrigo/Python_Question_Helper.git
cd Python_Question_Helper

# Instale dependências básicas
pip install -r requirements.txt
```

#### Método 2: Instalação Completa (Recomendada)

```bash
# Instale com todas as funcionalidades
pip install -r requirements.txt pandas matplotlib seaborn

# Verifique a instalação
python system_health_check.py
```

### **3. Configuração de API Keys**

Crie um arquivo `.env` na raiz do projeto:

```env
# OpenRouter (Recomendado - Acesso a múltiplos modelos)
LLM_API_KEY=sua_chave_openrouter_aqui
LLM_PROVIDER=openrouter
LLM_MODEL=google/gemini-2.0-flash-exp:free

# Google Gemini (Alternativo - Gratuito)
GEMINI_API_KEY=sua_chave_gemini_aqui

# Hugging Face (Alternativo - Open Source)
HUGGINGFACE_API_KEY=sua_chave_huggingface_aqui

# Configurações opcionais
DEBUG=False
ADS_ENABLED=false
```

### **4. Verificação da Instalação**

```bash
# Verifique se tudo está funcionando
python system_health_check.py

# Execute os testes
python test_logging_system.py

# Inicie o aplicativo
python main.py
```

---

## 🎯 **Funcionalidades**

### **🔍 Captura e Processamento**

| Funcionalidade              | Descrição                                          | Status |
| --------------------------- | -------------------------------------------------- | ------ |
| **Captura Inteligente**     | Seleção de área específica com melhoria automática | ✅     |
| **OCR Multiabordagem**      | Tesseract + processamento otimizado                | ✅     |
| **Detecção de Mudanças**    | Monitoramento automático de novas questões         | ✅     |
| **Processamento de Imagem** | Melhoria automática para OCR                       | ✅     |

### **🤖 Inteligência Artificial**

| Provedor          | Modelos Suportados           | Características                     |
| ----------------- | ---------------------------- | ----------------------------------- |
| **OpenRouter**    | GPT-4V, Gemini 2.0, Claude 3 | Melhor qualidade, múltiplos modelos |
| **Google Gemini** | Gemini Pro Vision, 1.5 Flash | Gratuito, rápido                    |
| **Hugging Face**  | LLaVA, BLIP-2                | Open source, sem custos             |
| **Ollama**        | Modelos locais               | Privacidade total                   |

### **📊 Sistema de Análise**

| Componente                 | Funcionalidade                  | Benefício             |
| -------------------------- | ------------------------------- | --------------------- |
| **Logging Estruturado**    | Registro de todas as questões   | Melhoria contínua     |
| **Detecção de Alucinação** | Identifica respostas incorretas | Maior confiabilidade  |
| **Análise de Padrões**     | Insights automáticos            | Otimização de prompts |
| **Interface de Revisão**   | Validação manual                | Controle de qualidade |

---

## 🎮 **Como Usar**

### **1. Execução Básica**

```bash
# Inicie o aplicativo
python main.py

# Ou use o comando instalado
question-helper
```

### **2. Interface Principal**

1. **🔴 Botão "Iniciar Monitoramento"**: Ativa/desativa o monitoramento automático
2. **📋 Dropdown de Modelos**: Seleciona o modelo LLM
3. **📝 Área de Resposta**: Exibe respostas com tempo de processamento
4. **⚙️ Menu Configurações**: Acesso a configurações avançadas

### **3. Fluxo de Trabalho**

```
[Iniciar Monitoramento] → [Selecionar Área] → [Detectar Mudanças] →
[Capturar Imagem] → [Extrair Texto] → [Processar com IA] → [Exibir Resposta]
```

### **4. Ferramentas Auxiliares**

```bash
# Interface de revisão de questões
python review_interface.py

# Análise de dados e relatórios
python analytics_module.py

# Verificação de saúde do sistema
python system_health_check.py

# Backup e migração
python backup_migration.py backup --name meu_backup
```

---

## 📖 **Documentação**

### **📚 Guias de Usuário**

- [📊 Sistema de Logging](LOGGING_DOCUMENTATION.md)
- [🧪 Resultados de Testes](TEST_RESULTS_SUMMARY.md)
- [🔄 Changelog](CHANGELOG.md)

### **🔧 Documentação Técnica**

- [🏗️ Arquitetura do Sistema](src/)
- [⚙️ Configurações](src/core/config.py)
- [🤖 Sistema de LLM](src/llm/)
- [📊 Analytics](src/analytics/)

---

## 🤝 **Suporte**

### **Canais de Suporte**

- **📧 Email**: <EMAIL>
- **🐛 Issues**: [GitHub Issues](https://github.com/hiagodrigo/Python_Question_Helper/issues)
- **💬 Discussões**: [GitHub Discussions](https://github.com/hiagodrigo/Python_Question_Helper/discussions)

### **Contribuição**

Contribuições são bem-vindas! Veja nosso guia de contribuição.

---

## 📜 **Licença**

Este projeto está licenciado sob **Licença Comercial**. Veja o arquivo [LICENSE](LICENSE) para detalhes completos.

### **Resumo da Licença**

- ✅ Uso pessoal, educacional e comercial
- ✅ Instalação em múltiplos dispositivos
- ❌ Redistribuição ou revenda
- ❌ Engenharia reversa
- ❌ Remoção de direitos autorais

---

<div align="center">

**Desenvolvido com ❤️ por BoZolinO**

_Python Question Helper v1.0.0 - Sistema Inteligente de Assistência para Questões_

[![GitHub](https://img.shields.io/badge/GitHub-hiagodrigo-blue?logo=github)](https://github.com/hiagodrigo)
[![Email](https://img.shields.io/badge/<EMAIL>-red?logo=gmail)](mailto:<EMAIL>)

</div>
