"""
Sistema de logging estruturado para análise de respostas do Python Question Helper.
Coleta dados detalhados sobre questões processadas para identificar padrões e melhorar precisão.
"""

import os
import sqlite3
import json
import hashlib
import shutil
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import cv2
import re
from config import DEBUG

class QuestionLogger:
    """
    Sistema de logging estruturado para questões e respostas.
    Armazena dados detalhados para análise posterior e melhoria do sistema.
    """

    def __init__(self, db_path: str = "question_logs.db", images_dir: str = "logged_images"):
        """
        Inicializa o sistema de logging.

        Args:
            db_path: Caminho para o banco de dados SQLite
            images_dir: Diretório para armazenar imagens das questões
        """
        self.db_path = db_path
        self.images_dir = images_dir
        self.session_id = self._generate_session_id()

        # Cria diretório de imagens se não existir
        os.makedirs(images_dir, exist_ok=True)

        # Inicializa banco de dados
        self._init_database()

        if DEBUG:
            print(f"QuestionLogger inicializado - Sessão: {self.session_id}")

    def _generate_session_id(self) -> str:
        """Gera ID único para a sessão atual."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        return f"session_{timestamp}"

    def _init_database(self):
        """Inicializa as tabelas do banco de dados."""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()

            # Tabela principal de questões
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS questions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    session_id TEXT NOT NULL,
                    timestamp DATETIME NOT NULL,
                    question_hash TEXT UNIQUE NOT NULL,
                    question_number INTEGER,
                    domain TEXT,
                    question_type TEXT,
                    has_images BOOLEAN,
                    ocr_text TEXT,
                    ocr_confidence REAL,
                    llm_provider TEXT,
                    llm_model TEXT,
                    llm_response TEXT,
                    llm_confidence INTEGER,
                    selected_answer TEXT,
                    processing_time REAL,
                    image_path TEXT,
                    metadata TEXT,
                    is_validated BOOLEAN DEFAULT FALSE,
                    is_correct BOOLEAN,
                    error_type TEXT,
                    reviewer_notes TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)

            # Tabela de análise de padrões
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS pattern_analysis (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    analysis_date DATETIME NOT NULL,
                    category TEXT NOT NULL,
                    total_questions INTEGER,
                    correct_answers INTEGER,
                    accuracy_rate REAL,
                    avg_confidence REAL,
                    common_errors TEXT,
                    recommendations TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)

            # Tabela de configurações e métricas
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS system_metrics (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    metric_date DATETIME NOT NULL,
                    total_processed INTEGER,
                    total_validated INTEGER,
                    overall_accuracy REAL,
                    avg_processing_time REAL,
                    top_provider TEXT,
                    top_model TEXT,
                    problematic_domains TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)

            conn.commit()

    def _calculate_question_hash(self, ocr_text: str, image_path: Optional[str] = None) -> str:
        """
        Calcula hash único para a questão baseado no texto OCR e imagem.

        Args:
            ocr_text: Texto extraído via OCR
            image_path: Caminho da imagem (opcional)

        Returns:
            Hash MD5 da questão
        """
        # Normaliza o texto para o hash
        normalized_text = re.sub(r'\s+', ' ', ocr_text.strip().lower())

        # Inclui hash da imagem se disponível
        content = normalized_text
        if image_path and os.path.exists(image_path):
            with open(image_path, 'rb') as f:
                image_hash = hashlib.md5(f.read()).hexdigest()[:8]
                content += f"_img_{image_hash}"

        return hashlib.md5(content.encode()).hexdigest()

    def _detect_domain(self, ocr_text: str) -> str:
        """
        Detecta o domínio da questão baseado no texto OCR.

        Args:
            ocr_text: Texto extraído via OCR

        Returns:
            Domínio identificado
        """
        text_lower = ocr_text.lower()

        # Palavras-chave por domínio
        domain_keywords = {
            'legislacao': ['lei', 'artigo', 'código', 'ctb', 'constituição', 'decreto', 'resolução'],
            'sinalizacao': ['placa', 'sinal', 'semáforo', 'sinalização', 'amarela', 'vermelha', 'verde'],
            'direcao_defensiva': ['direção defensiva', 'defensiva', 'segurança', 'prevenção', 'cuidado'],
            'primeiros_socorros': ['primeiros socorros', 'socorro', 'ferimento', 'acidente', 'vítima'],
            'mecanica': ['motor', 'freio', 'pneu', 'óleo', 'combustível', 'mecânica', 'manutenção'],
            'meio_ambiente': ['meio ambiente', 'poluição', 'emissão', 'combustível', 'natureza'],
            'infrações': ['infração', 'multa', 'penalidade', 'pontos', 'suspensão', 'cassação'],
            'distancias': ['distância', 'metros', 'seguimento', 'frenagem', 'reação', 'parada']
        }

        # Conta ocorrências por domínio
        domain_scores = {}
        for domain, keywords in domain_keywords.items():
            score = sum(1 for keyword in keywords if keyword in text_lower)
            if score > 0:
                domain_scores[domain] = score

        # Retorna domínio com maior score ou 'geral'
        if domain_scores:
            return max(domain_scores.keys(), key=lambda k: domain_scores[k])
        return 'geral'

    def _detect_question_type(self, ocr_text: str, llm_response: str) -> str:
        """
        Detecta o tipo da questão baseado no conteúdo.

        Args:
            ocr_text: Texto extraído via OCR
            llm_response: Resposta do LLM

        Returns:
            Tipo da questão
        """
        text_combined = (ocr_text + " " + llm_response).lower()

        # Indicadores de cálculo
        if any(indicator in text_combined for indicator in ['metros', 'km/h', 'velocidade', 'distância', 'tempo', 'cálculo']):
            return 'calculo'

        # Indicadores de interpretação
        if any(indicator in text_combined for indicator in ['situação', 'caso', 'exemplo', 'cenário', 'contexto']):
            return 'interpretacao'

        # Indicadores de conhecimento factual
        if any(indicator in text_combined for indicator in ['lei', 'artigo', 'código', 'norma', 'regra']):
            return 'factual'

        # Indicadores de aplicação prática
        if any(indicator in text_combined for indicator in ['deve', 'deveria', 'correto', 'adequado', 'procedimento']):
            return 'aplicacao'

        return 'geral'

    def _detect_has_images(self, ocr_text: str) -> bool:
        """
        Detecta se a questão contém imagens/diagramas relevantes.

        Args:
            ocr_text: Texto extraído via OCR

        Returns:
            True se detectar presença de imagens importantes
        """
        # Indicadores de presença de imagens
        image_indicators = [
            'figura', 'imagem', 'diagrama', 'esquema', 'ilustração',
            'placa', 'sinal', 'semáforo', 'desenho', 'gráfico'
        ]

        text_lower = ocr_text.lower()
        return any(indicator in text_lower for indicator in image_indicators)

    def _save_question_image(self, image_path: str, question_hash: str) -> Optional[str]:
        """
        Salva cópia da imagem da questão no diretório de logs.

        Args:
            image_path: Caminho da imagem original
            question_hash: Hash único da questão

        Returns:
            Caminho da imagem salva
        """
        if not os.path.exists(image_path):
            return None

        # Organiza por data
        date_dir = os.path.join(self.images_dir, datetime.now().strftime("%Y-%m"))
        os.makedirs(date_dir, exist_ok=True)

        # Nome do arquivo com hash
        filename = f"{question_hash}_{datetime.now().strftime('%H%M%S')}.png"
        saved_path = os.path.join(date_dir, filename)

        try:
            shutil.copy2(image_path, saved_path)
            return saved_path
        except Exception as e:
            if DEBUG:
                print(f"Erro ao salvar imagem: {e}")
            return None

    def log_question(self,
                    ocr_text: str,
                    llm_response: str,
                    llm_provider: str,
                    llm_model: str,
                    llm_confidence: int,
                    processing_time: float,
                    image_path: Optional[str] = None,
                    question_number: Optional[int] = None,
                    ocr_confidence: Optional[float] = None,
                    additional_metadata: Optional[Dict] = None) -> Optional[str]:
        """
        Registra uma questão processada no sistema de logging.

        Args:
            ocr_text: Texto extraído via OCR
            llm_response: Resposta completa do LLM
            llm_provider: Provedor LLM utilizado
            llm_model: Modelo LLM utilizado
            llm_confidence: Nível de confiança (0-100)
            processing_time: Tempo de processamento em segundos
            image_path: Caminho da imagem original
            question_number: Número da questão (se detectado)
            ocr_confidence: Confiança do OCR (0-100)
            additional_metadata: Metadados adicionais

        Returns:
            Hash único da questão registrada
        """
        try:
            # Calcula hash único da questão
            question_hash = self._calculate_question_hash(ocr_text, image_path)

            # Detecta características da questão
            domain = self._detect_domain(ocr_text)
            question_type = self._detect_question_type(ocr_text, llm_response)
            has_images = self._detect_has_images(ocr_text)

            # Extrai resposta selecionada
            selected_answer = self._extract_selected_answer(llm_response)

            # Salva imagem se fornecida
            saved_image_path = None
            if image_path:
                saved_image_path = self._save_question_image(image_path, question_hash)

            # Prepara metadados
            metadata = {
                'session_id': self.session_id,
                'original_image_path': image_path,
                'text_length': len(ocr_text),
                'response_length': len(llm_response),
                'has_calculation': 'cálculo' in question_type,
                'confidence_category': self._categorize_confidence(llm_confidence),
                'additional': additional_metadata or {}
            }

            # Insere no banco de dados
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                cursor.execute("""
                    INSERT OR REPLACE INTO questions (
                        session_id, timestamp, question_hash, question_number,
                        domain, question_type, has_images, ocr_text, ocr_confidence,
                        llm_provider, llm_model, llm_response, llm_confidence,
                        selected_answer, processing_time, image_path, metadata
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    self.session_id,
                    datetime.now().isoformat(),
                    question_hash,
                    question_number,
                    domain,
                    question_type,
                    has_images,
                    ocr_text,
                    ocr_confidence,
                    llm_provider,
                    llm_model,
                    llm_response,
                    llm_confidence,
                    selected_answer,
                    processing_time,
                    saved_image_path,
                    json.dumps(metadata)
                ))

                conn.commit()

            if DEBUG:
                print(f"Questão registrada: {question_hash[:8]} - {domain} - {question_type}")

            return question_hash

        except Exception as e:
            if DEBUG:
                print(f"Erro ao registrar questão: {e}")
            return None

    def _extract_selected_answer(self, llm_response: str) -> Optional[str]:
        """
        Extrai a resposta selecionada da resposta do LLM.

        Args:
            llm_response: Resposta completa do LLM

        Returns:
            Letra da alternativa selecionada ou None
        """
        # Padrões para detectar resposta
        patterns = [
            r'resposta correta:\s*([A-E])\)',
            r'alternativa\s*([A-E])',
            r'letra\s*([A-E])',
            r'^([A-E])\)',
            r'opção\s*([A-E])'
        ]

        for pattern in patterns:
            match = re.search(pattern, llm_response, re.IGNORECASE)
            if match:
                return match.group(1).upper()

        return None

    def _categorize_confidence(self, confidence: int) -> str:
        """
        Categoriza o nível de confiança.

        Args:
            confidence: Nível de confiança (0-100)

        Returns:
            Categoria de confiança
        """
        if confidence >= 90:
            return 'muito_alta'
        elif confidence >= 80:
            return 'alta'
        elif confidence >= 70:
            return 'media'
        elif confidence >= 60:
            return 'baixa'
        else:
            return 'muito_baixa'

    def mark_validation(self, question_hash: str, is_correct: bool,
                       error_type: Optional[str] = None, reviewer_notes: Optional[str] = None) -> bool:
        """
        Marca uma questão como validada manualmente.

        Args:
            question_hash: Hash único da questão
            is_correct: Se a resposta está correta
            error_type: Tipo de erro (se incorreta)
            reviewer_notes: Notas do revisor

        Returns:
            True se a validação foi salva com sucesso
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                cursor.execute("""
                    UPDATE questions
                    SET is_validated = TRUE, is_correct = ?, error_type = ?, reviewer_notes = ?
                    WHERE question_hash = ?
                """, (is_correct, error_type, reviewer_notes, question_hash))

                conn.commit()

                if cursor.rowcount > 0:
                    if DEBUG:
                        print(f"Validação registrada para {question_hash[:8]}: {'✓' if is_correct else '✗'}")
                    return True

        except Exception as e:
            if DEBUG:
                print(f"Erro ao marcar validação: {e}")

        return False

    def get_questions_for_review(self, limit: int = 10,
                                confidence_threshold: int = 70) -> List[Dict]:
        """
        Retorna questões que precisam de revisão manual.

        Args:
            limit: Número máximo de questões a retornar
            confidence_threshold: Limite de confiança para incluir na revisão

        Returns:
            Lista de questões para revisão
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                cursor.execute("""
                    SELECT question_hash, timestamp, domain, question_type,
                           ocr_text, llm_response, llm_confidence, selected_answer,
                           image_path, llm_provider, llm_model
                    FROM questions
                    WHERE is_validated = FALSE
                    AND (llm_confidence < ? OR llm_confidence IS NULL)
                    ORDER BY timestamp DESC
                    LIMIT ?
                """, (confidence_threshold, limit))

                questions = []
                for row in cursor.fetchall():
                    questions.append({
                        'hash': row[0],
                        'timestamp': row[1],
                        'domain': row[2],
                        'question_type': row[3],
                        'ocr_text': row[4],
                        'llm_response': row[5],
                        'confidence': row[6],
                        'selected_answer': row[7],
                        'image_path': row[8],
                        'provider': row[9],
                        'model': row[10]
                    })

                return questions

        except Exception as e:
            if DEBUG:
                print(f"Erro ao buscar questões para revisão: {e}")
            return []

    def get_statistics(self) -> Dict:
        """
        Retorna estatísticas gerais do sistema.

        Returns:
            Dicionário com estatísticas
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # Estatísticas básicas
                cursor.execute("SELECT COUNT(*) FROM questions")
                total_questions = cursor.fetchone()[0]

                cursor.execute("SELECT COUNT(*) FROM questions WHERE is_validated = TRUE")
                validated_questions = cursor.fetchone()[0]

                cursor.execute("SELECT COUNT(*) FROM questions WHERE is_correct = TRUE")
                correct_answers = cursor.fetchone()[0]

                # Estatísticas por domínio
                cursor.execute("""
                    SELECT domain, COUNT(*),
                           AVG(CASE WHEN is_correct = TRUE THEN 1.0 ELSE 0.0 END) as accuracy
                    FROM questions
                    WHERE is_validated = TRUE
                    GROUP BY domain
                """)
                domain_stats = cursor.fetchall()

                # Estatísticas por provedor
                cursor.execute("""
                    SELECT llm_provider, COUNT(*),
                           AVG(CASE WHEN is_correct = TRUE THEN 1.0 ELSE 0.0 END) as accuracy,
                           AVG(llm_confidence) as avg_confidence
                    FROM questions
                    WHERE is_validated = TRUE
                    GROUP BY llm_provider
                """)
                provider_stats = cursor.fetchall()

                # Tempo médio de processamento
                cursor.execute("SELECT AVG(processing_time) FROM questions WHERE processing_time IS NOT NULL")
                avg_processing_time = cursor.fetchone()[0] or 0

                return {
                    'total_questions': total_questions,
                    'validated_questions': validated_questions,
                    'correct_answers': correct_answers,
                    'overall_accuracy': correct_answers / validated_questions if validated_questions > 0 else 0,
                    'validation_rate': validated_questions / total_questions if total_questions > 0 else 0,
                    'avg_processing_time': avg_processing_time,
                    'domain_stats': [
                        {'domain': row[0], 'count': row[1], 'accuracy': row[2] or 0}
                        for row in domain_stats
                    ],
                    'provider_stats': [
                        {
                            'provider': row[0],
                            'count': row[1],
                            'accuracy': row[2] or 0,
                            'avg_confidence': row[3] or 0
                        }
                        for row in provider_stats
                    ]
                }

        except Exception as e:
            if DEBUG:
                print(f"Erro ao calcular estatísticas: {e}")
            return {}

    def export_data(self, output_path: str, format: str = 'csv') -> bool:
        """
        Exporta dados para análise externa.

        Args:
            output_path: Caminho do arquivo de saída
            format: Formato de exportação ('csv' ou 'json')

        Returns:
            True se a exportação foi bem-sucedida
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                cursor.execute("""
                    SELECT * FROM questions
                    ORDER BY timestamp DESC
                """)

                columns = [description[0] for description in cursor.description]
                data = cursor.fetchall()

                if format.lower() == 'csv':
                    import csv
                    with open(output_path, 'w', newline='', encoding='utf-8') as f:
                        writer = csv.writer(f)
                        writer.writerow(columns)
                        writer.writerows(data)

                elif format.lower() == 'json':
                    import json
                    json_data = []
                    for row in data:
                        json_data.append(dict(zip(columns, row)))

                    with open(output_path, 'w', encoding='utf-8') as f:
                        json.dump(json_data, f, indent=2, ensure_ascii=False, default=str)

                if DEBUG:
                    print(f"Dados exportados para {output_path} ({len(data)} registros)")

                return True

        except Exception as e:
            if DEBUG:
                print(f"Erro ao exportar dados: {e}")
            return False

    def cleanup_old_data(self, days_to_keep: int = 30) -> int:
        """
        Remove dados antigos para manter o banco otimizado.

        Args:
            days_to_keep: Número de dias de dados para manter

        Returns:
            Número de registros removidos
        """
        try:
            cutoff_date = datetime.now() - timedelta(days=days_to_keep)

            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # Remove questões antigas não validadas
                cursor.execute("""
                    DELETE FROM questions
                    WHERE timestamp < ? AND is_validated = FALSE
                """, (cutoff_date.isoformat(),))

                removed_count = cursor.rowcount
                conn.commit()

                if DEBUG and removed_count > 0:
                    print(f"Removidos {removed_count} registros antigos")

                return removed_count

        except Exception as e:
            if DEBUG:
                print(f"Erro ao limpar dados antigos: {e}")
            return 0
