"""
Gerenciador de prompts para melhorar a qualidade das respostas do LLM.
"""
import re
from config import DEBUG

class PromptManager:
    """
    Gerencia os prompts enviados para os modelos LLM para melhorar a qualidade das respostas.
    """

    # Categorias de questões que podemos identificar
    CATEGORIES = {
        "matemática": ["matemática", "equação", "cálculo", "geometria", "álgebra", "trigonometria", "estatística", "probabilidade"],
        "física": ["física", "mecânica", "eletricidade", "magnetismo", "termodinâmica", "óptica", "aceleração", "velocidade", "força"],
        "química": ["química", "reação", "elemento", "composto", "ácido", "base", "orgânica", "inorgânica", "mol", "átomo", "molécula"],
        "biologia": ["biologia", "célula", "organismo", "genética", "evolução", "ecologia", "sistema", "tecido", "órgão"],
        "história": ["história", "século", "guerra", "revolução", "império", "civilização", "período", "era", "idade média", "antiguidade"],
        "geografia": ["geografia", "clima", "relevo", "população", "país", "continente", "oceano", "rio", "montanha"],
        "português": ["português", "gramática", "verbo", "substantivo", "adjetivo", "sintaxe", "morfologia", "literatura", "texto"],
        "inglês": ["inglês", "verb", "noun", "adjective", "grammar", "vocabulary", "idiom", "phrasal"],
        "informática": ["informática", "computador", "software", "hardware", "programação", "algoritmo", "internet", "rede", "dados"]
    }

    # Prompts específicos para cada categoria
    CATEGORY_PROMPTS = {
        "matemática": """
        Você é um professor especialista em matemática analisando uma questão de prova.
        Analise cuidadosamente todos os detalhes do problema, incluindo fórmulas, equações e valores.
        Resolva o problema passo a passo, mostrando claramente seu raciocínio.
        Verifique seus cálculos duas vezes antes de fornecer a resposta final.
        """,

        "física": """
        Você é um professor especialista em física analisando uma questão de prova.
        Identifique os princípios físicos relevantes e as equações aplicáveis.
        Resolva o problema passo a passo, convertendo unidades se necessário.
        Verifique se a resposta é dimensionalmente consistente e fisicamente plausível.
        """,

        "química": """
        Você é um professor especialista em química analisando uma questão de prova.
        Identifique os conceitos químicos, reações ou propriedades relevantes.
        Considere a estequiometria, equilíbrio, estrutura molecular ou outros fatores relevantes.
        Verifique se a resposta é consistente com os princípios químicos estabelecidos.
        """,

        "biologia": """
        Você é um professor especialista em biologia analisando uma questão de prova.
        Identifique os conceitos biológicos, processos ou estruturas relevantes.
        Considere as relações entre diferentes níveis de organização biológica.
        Verifique se a resposta é consistente com o conhecimento biológico atual.
        """,

        "história": """
        Você é um professor especialista em história analisando uma questão de prova.
        Identifique o período histórico, eventos, figuras ou processos relevantes.
        Considere o contexto histórico, causas e consequências dos eventos mencionados.
        Verifique se a resposta é historicamente precisa e bem fundamentada.
        """,

        "geografia": """
        Você é um professor especialista em geografia analisando uma questão de prova.
        Identifique os conceitos geográficos, regiões, processos ou fenômenos relevantes.
        Considere as relações entre geografia física, humana e econômica quando aplicável.
        Verifique se a resposta é geograficamente precisa e atualizada.
        """,

        "português": """
        Você é um professor especialista em língua portuguesa analisando uma questão de prova.
        Identifique os conceitos linguísticos, regras gramaticais ou elementos literários relevantes.
        Analise cuidadosamente o texto, considerando contexto, conotação e denotação.
        Verifique se a resposta está de acordo com as normas da língua portuguesa.
        """,

        "inglês": """
        Você é um professor especialista em língua inglesa analisando uma questão de prova.
        Identifique os conceitos linguísticos, regras gramaticais ou elementos literários relevantes.
        Analise cuidadosamente o texto, considerando contexto, conotação e denotação.
        Verifique se a resposta está de acordo com as normas da língua inglesa.
        """,

        "informática": """
        Você é um professor especialista em informática analisando uma questão de prova.
        Identifique os conceitos de computação, algoritmos, hardware ou software relevantes.
        Considere as implicações práticas e teóricas dos conceitos mencionados.
        Verifique se a resposta é tecnicamente precisa e atualizada.
        """
    }

    # Prompt padrão para questões que não se encaixam em nenhuma categoria específica
    DEFAULT_PROMPT = """
    Você é um professor especialista analisando uma questão de prova.
    Analise cuidadosamente todos os detalhes da questão.
    Considere todas as alternativas e elimine as incorretas.
    Verifique sua resposta duas vezes antes de fornecer a resposta final.
    """

    # Instruções para o formato da resposta
    RESPONSE_FORMAT = """
    FORMATO OBRIGATÓRIO DA RESPOSTA:

    Primeiro, pense passo a passo sobre a questão (esta parte é apenas para seu raciocínio interno).

    VOCÊ DEVE INICIAR SUA RESPOSTA EXATAMENTE COM ESTA FRASE:
    "Resposta correta: [LETRA]) [TEXTO DA ALTERNATIVA]"

    Exemplo exato: "Resposta correta: A) A força gravitacional"

    Esta frase DEVE ser a primeira linha da sua resposta, sem nenhum texto antes dela.

    Depois da resposta, adicione uma breve explicação que justifique por que esta é a alternativa correta e por que as outras estão erradas.

    IMPORTANTE:
    - Responda SEMPRE em português (PT-BR)
    - Seja direto e objetivo, priorizando a resposta correta
    - Tenha 100% de certeza na sua resposta, verificando-a cuidadosamente
    - NÃO coloque nenhum texto antes da frase "Resposta correta:"
    """

    # Instruções para verificação adicional
    VERIFICATION_INSTRUCTIONS = """
    Antes de finalizar, verifique:
    1. Você considerou TODAS as informações da questão?
    2. Você analisou TODAS as alternativas?
    3. Você tem CERTEZA ABSOLUTA da resposta? Se não, indique seu nível de confiança.
    4. Existe alguma ambiguidade ou exceção que poderia afetar a resposta?

    Se você não tiver 100% de certeza, indique claramente qual é a alternativa mais provável e por quê.
    """

    @staticmethod
    def detect_category(text):
        """
        Detecta a categoria da questão com base no texto.

        Args:
            text: Texto da questão

        Returns:
            Categoria detectada ou None
        """
        text = text.lower()

        # Pontuação para cada categoria
        category_scores = {category: 0 for category in PromptManager.CATEGORIES}

        # Calcula a pontuação para cada categoria
        for category, keywords in PromptManager.CATEGORIES.items():
            for keyword in keywords:
                if keyword in text:
                    # Aumenta a pontuação baseada no número de ocorrências e na especificidade da palavra-chave
                    occurrences = len(re.findall(r'\b' + re.escape(keyword) + r'\b', text))
                    specificity = len(keyword) / 5  # Palavras mais longas são geralmente mais específicas
                    category_scores[category] += occurrences * specificity

        # Encontra a categoria com maior pontuação
        max_score = max(category_scores.values())
        if max_score > 0:
            # Retorna a categoria com maior pontuação
            for category, score in category_scores.items():
                if score == max_score:
                    if DEBUG:
                        print(f"Categoria detectada: {category} (pontuação: {score})")
                    return category

        # Se nenhuma categoria foi detectada com confiança suficiente
        if DEBUG:
            print("Usando prompt padrão (sem categoria específica)")
        return None

    @staticmethod
    def create_enhanced_prompt(text):
        """
        Cria um prompt aprimorado com base no texto da questão.

        Args:
            text: Texto da questão

        Returns:
            Prompt aprimorado
        """
        # Detecta a categoria da questão
        category = PromptManager.detect_category(text)

        # Seleciona o prompt específico para a categoria ou o prompt padrão
        if category and category in PromptManager.CATEGORY_PROMPTS:
            category_prompt = PromptManager.CATEGORY_PROMPTS[category]
        else:
            category_prompt = PromptManager.DEFAULT_PROMPT

        # Combina os prompts
        enhanced_prompt = f"""
        {category_prompt}

        {PromptManager.RESPONSE_FORMAT}

        {PromptManager.VERIFICATION_INSTRUCTIONS}

        LEMBRE-SE: Sua resposta DEVE começar com "Resposta correta: [LETRA]) [TEXTO]" sem nenhum texto antes disso.

        Analise a seguinte questão:

        {text}

        IMPORTANTE: Comece sua resposta DIRETAMENTE com "Resposta correta: [LETRA]) [TEXTO]"
        """

        return enhanced_prompt
