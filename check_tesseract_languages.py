"""
Script para verificar os idiomas disponíveis no Tesseract OCR.
"""
import os
import sys
import subprocess
import pytesseract
from config import TESSERACT_PATH

def check_tesseract_languages():
    """Verifica os idiomas disponíveis no Tesseract OCR."""
    print("Verificando idiomas disponíveis no Tesseract OCR...")
    
    # Definir o caminho do Tesseract
    if os.path.exists(TESSERACT_PATH):
        pytesseract.pytesseract.tesseract_cmd = TESSERACT_PATH
        print(f"Usando Tesseract em: {TESSERACT_PATH}")
    else:
        print(f"Tesseract não encontrado em: {TESSERACT_PATH}")
        print("Tentando usar o Tesseract do PATH do sistema...")
        pytesseract.pytesseract.tesseract_cmd = 'tesseract'
    
    try:
        # Verificar a versão do Tesseract
        version_info = pytesseract.get_tesseract_version()
        print(f"Versão do Tesseract: {version_info}")
        
        # Obter a lista de idiomas disponíveis
        try:
            result = subprocess.run([pytesseract.pytesseract.tesseract_cmd, '--list-langs'], 
                                  stdout=subprocess.PIPE, 
                                  stderr=subprocess.PIPE,
                                  text=True,
                                  timeout=5)
            
            if result.returncode == 0:
                # A lista de idiomas geralmente é retornada no stderr
                langs = result.stderr.strip().split('\n')
                # Remover a primeira linha que é apenas um cabeçalho
                if len(langs) > 1:
                    langs = langs[1:]
                
                print("\nIdiomas disponíveis:")
                for lang in langs:
                    print(f"- {lang}")
                
                # Verificar se o português está disponível
                if 'por' in langs:
                    print("\n✅ Idioma português (por) está disponível!")
                else:
                    print("\n❌ Idioma português (por) NÃO está disponível!")
                    print("\nPara instalar o pacote de idioma português:")
                    print("1. Reinstale o Tesseract OCR com a opção 'Additional language data'")
                    print("2. Selecione 'Portuguese' na lista de idiomas durante a instalação")
            else:
                print(f"Erro ao listar idiomas: {result.stderr}")
        except Exception as e:
            print(f"Erro ao executar o comando: {e}")
    except Exception as e:
        print(f"Erro ao verificar o Tesseract: {e}")
    
    print("\nSugestões para melhorar o reconhecimento de texto em português:")
    print("1. Certifique-se de que o pacote de idioma português (por) está instalado")
    print("2. Use 'por' como parâmetro de idioma nas funções de OCR")
    print("3. Para melhor reconhecimento, use uma combinação de idiomas: 'por+eng'")

if __name__ == "__main__":
    check_tesseract_languages()
    
    # Aguardar entrada do usuário antes de fechar
    input("\nPressione Enter para sair...")
