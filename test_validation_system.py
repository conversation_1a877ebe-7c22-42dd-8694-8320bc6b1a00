"""
Teste do sistema de validação integrado.
Verifica se o sistema está funcionando corretamente com diferentes tipos de questões.
"""
from response_validator import ResponseValidator
from enhanced_prompt_manager import EnhancedPromptManager

def test_transit_question():
    """Testa questão de trânsito com ambiguidade."""
    print("=== TESTE: Questão de Trânsito ===")
    
    validator = ResponseValidator()
    
    question = """
    A distância que o veículo percorre, desde o momento que o motorista vê o perigo e aciona os freios, é:
    
    A) Distância de reação.
    B) Distância de frenagem.
    C) Distância de seguimento.
    D) Distância de parada.
    """
    
    # Resposta correta
    correct_response = "Resposta correta: D) Distância de parada.\n\nExplicação: A pergunta descreve todo o processo desde ver o perigo até parar."
    
    # Resposta incorreta (como nosso sistema original faria)
    incorrect_response = "Resposta correta: B) Distância de frenagem.\n\nExplicação: A questão se refere ao período após acionar os freios."
    
    print("\n1. Testando resposta CORRETA:")
    result = validator.validate_and_process_response(question, correct_response)
    formatted = validator.format_validated_response(result, correct_response)
    print(formatted)
    
    print("\n2. Testando resposta INCORRETA:")
    result = validator.validate_and_process_response(question, incorrect_response)
    formatted = validator.format_validated_response(result, incorrect_response)
    print(formatted)

def test_math_question():
    """Testa questão de matemática."""
    print("\n\n=== TESTE: Questão de Matemática ===")
    
    validator = ResponseValidator()
    
    question = """
    Qual é o resultado de 2 + 3 × 4?
    
    A) 20
    B) 14
    C) 10
    D) 24
    """
    
    response = "Resposta correta: B) 14.\n\nExplicação: Seguindo a ordem das operações, primeiro 3 × 4 = 12, depois 2 + 12 = 14."
    
    result = validator.validate_and_process_response(question, response)
    formatted = validator.format_validated_response(result, response)
    print(formatted)

def test_prompt_generation():
    """Testa geração de prompts com validação."""
    print("\n\n=== TESTE: Geração de Prompts ===")
    
    manager = EnhancedPromptManager()
    
    transit_question = "A distância que o veículo percorre, desde o momento que o motorista vê o perigo e aciona os freios, é:"
    
    print("1. Prompt com validação para questão de trânsito:")
    validated_prompt = manager.create_validated_prompt(transit_question)
    print(validated_prompt[:500] + "..." if len(validated_prompt) > 500 else validated_prompt)

def test_ambiguity_detection():
    """Testa detecção de ambiguidades."""
    print("\n\n=== TESTE: Detecção de Ambiguidades ===")
    
    validator = ResponseValidator()
    
    transit_question = "A distância que o veículo percorre, desde o momento que o motorista vê o perigo e aciona os freios, é:"
    
    domain = validator.identify_knowledge_domain(transit_question)
    ambiguity = validator.analyze_question_ambiguity(transit_question, domain)
    
    print(f"Domínio identificado: {domain}")
    print(f"Tem ambiguidade: {ambiguity['has_ambiguity']}")
    if ambiguity['has_ambiguity']:
        print(f"Termos ambíguos: {ambiguity['ambiguous_terms']}")
        print(f"Clarificações: {ambiguity['clarifications']}")
        print(f"Notas: {ambiguity['interpretation_notes']}")

if __name__ == "__main__":
    print("🔍 TESTANDO SISTEMA DE VALIDAÇÃO E VERIFICAÇÃO DE RESPOSTAS")
    print("=" * 60)
    
    test_transit_question()
    test_math_question()
    test_prompt_generation()
    test_ambiguity_detection()
    
    print("\n" + "=" * 60)
    print("✅ TESTES CONCLUÍDOS")
    print("\nO sistema de validação está:")
    print("• Detectando domínios corretamente")
    print("• Identificando ambiguidades")
    print("• Validando respostas")
    print("• Formatando respostas de forma concisa")
    print("• Fornecendo clarificações específicas por área")
