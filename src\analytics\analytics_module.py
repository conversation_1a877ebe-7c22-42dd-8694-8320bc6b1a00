"""
Módulo de análise de padrões e geração de relatórios para o Python Question Helper.
Identifica padrões de erro, gera insights e recomendações para melhorar a precisão.
"""

import os
import sqlite3
import json
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List
from collections import Counter, defaultdict
import re
from ..question_logging.question_logger import QuestionLogger
from ..core.config import DEBUG

# Imports opcionais
try:
    import pandas as pd
    HAS_PANDAS = True
except ImportError:
    HAS_PANDAS = False

try:
    import matplotlib.pyplot as plt
    import seaborn as sns
    HAS_MATPLOTLIB = True
except ImportError:
    HAS_MATPLOTLIB = False

class AnalyticsModule:
    """
    Sistema de análise de padrões e geração de insights.
    Analisa dados coletados para identificar problemas e sugerir melhorias.
    """

    def __init__(self, logger: QuestionLogger = None):
        """
        Inicializa o módulo de análise.

        Args:
            logger: Instância do QuestionLogger (opcional)
        """
        self.logger = logger or QuestionLogger()

        # Configurações de análise
        self.confidence_thresholds = {
            'muito_baixa': 60,
            'baixa': 70,
            'media': 80,
            'alta': 90
        }

        # Padrões de erro conhecidos
        self.error_patterns = {
            'alucinacao': [
                'informação não presente', 'inventou', 'não existe',
                'dados incorretos', 'falsa informação'
            ],
            'interpretacao': [
                'mal interpretado', 'contexto errado', 'entendimento incorreto',
                'leitura errada', 'compreensão falha'
            ],
            'calculo': [
                'erro matemático', 'cálculo errado', 'conta incorreta',
                'resultado falso', 'operação errada'
            ],
            'conhecimento': [
                'lei desatualizada', 'norma antiga', 'informação obsoleta',
                'mudança na legislação', 'atualização necessária'
            ]
        }

        if DEBUG:
            print("AnalyticsModule inicializado")

    def generate_comprehensive_report(self, days_back: int = 30) -> Dict:
        """
        Gera relatório abrangente de análise.

        Args:
            days_back: Número de dias para incluir na análise

        Returns:
            Dicionário com relatório completo
        """
        try:
            # Coleta dados do período
            data = self._get_analysis_data(days_back)

            if not data:
                return {'error': 'Nenhum dado disponível para análise'}

            # Análises principais
            report = {
                'period': {
                    'start_date': (datetime.now() - timedelta(days=days_back)).strftime('%Y-%m-%d'),
                    'end_date': datetime.now().strftime('%Y-%m-%d'),
                    'total_questions': len(data)
                },
                'accuracy_analysis': self._analyze_accuracy(data),
                'confidence_analysis': self._analyze_confidence(data),
                'domain_analysis': self._analyze_domains(data),
                'provider_analysis': self._analyze_providers(data),
                'error_analysis': self._analyze_errors(data),
                'temporal_analysis': self._analyze_temporal_patterns(data),
                'recommendations': self._generate_recommendations(data),
                'quality_metrics': self._calculate_quality_metrics(data),
                'generated_at': datetime.now().isoformat()
            }

            return report

        except Exception as e:
            if DEBUG:
                print(f"Erro ao gerar relatório: {e}")
            return {'error': str(e)}

    def _get_analysis_data(self, days_back: int) -> List[Dict]:
        """
        Coleta dados para análise do período especificado.

        Args:
            days_back: Número de dias para incluir

        Returns:
            Lista de dados das questões
        """
        try:
            cutoff_date = datetime.now() - timedelta(days=days_back)

            with sqlite3.connect(self.logger.db_path) as conn:
                cursor = conn.cursor()

                cursor.execute("""
                    SELECT * FROM questions
                    WHERE timestamp >= ?
                    ORDER BY timestamp DESC
                """, (cutoff_date.isoformat(),))

                columns = [description[0] for description in cursor.description]
                rows = cursor.fetchall()

                data = []
                for row in rows:
                    item = dict(zip(columns, row))
                    # Parse metadata JSON
                    if item['metadata']:
                        try:
                            item['metadata'] = json.loads(item['metadata'])
                        except:
                            item['metadata'] = {}
                    data.append(item)

                return data

        except Exception as e:
            if DEBUG:
                print(f"Erro ao coletar dados: {e}")
            return []

    def _analyze_accuracy(self, data: List[Dict]) -> Dict:
        """
        Analisa precisão das respostas.

        Args:
            data: Dados das questões

        Returns:
            Análise de precisão
        """
        validated_data = [item for item in data if item['is_validated']]

        if not validated_data:
            return {'error': 'Nenhuma questão validada disponível'}

        total_validated = len(validated_data)
        correct_answers = len([item for item in validated_data if item['is_correct']])

        # Análise por categoria de confiança
        confidence_accuracy = {}
        for threshold_name, threshold_value in self.confidence_thresholds.items():
            if threshold_name == 'muito_baixa':
                subset = [item for item in validated_data if (item['llm_confidence'] or 0) < threshold_value]
            else:
                prev_threshold = list(self.confidence_thresholds.values())[
                    list(self.confidence_thresholds.keys()).index(threshold_name) - 1
                ]
                subset = [item for item in validated_data
                         if prev_threshold <= (item['llm_confidence'] or 0) < threshold_value]

            if subset:
                correct_in_subset = len([item for item in subset if item['is_correct']])
                confidence_accuracy[threshold_name] = {
                    'total': len(subset),
                    'correct': correct_in_subset,
                    'accuracy': correct_in_subset / len(subset)
                }

        return {
            'overall_accuracy': correct_answers / total_validated,
            'total_validated': total_validated,
            'correct_answers': correct_answers,
            'accuracy_by_confidence': confidence_accuracy,
            'validation_rate': total_validated / len(data) if data else 0
        }

    def _analyze_confidence(self, data: List[Dict]) -> Dict:
        """
        Analisa distribuição e correlação de confiança.

        Args:
            data: Dados das questões

        Returns:
            Análise de confiança
        """
        confidence_data = [item['llm_confidence'] for item in data if item['llm_confidence'] is not None]

        if not confidence_data:
            return {'error': 'Nenhum dado de confiança disponível'}

        # Estatísticas básicas
        stats = {
            'mean': np.mean(confidence_data),
            'median': np.median(confidence_data),
            'std': np.std(confidence_data),
            'min': np.min(confidence_data),
            'max': np.max(confidence_data)
        }

        # Distribuição por faixas
        distribution = {}
        for name, threshold in self.confidence_thresholds.items():
            if name == 'muito_baixa':
                count = len([c for c in confidence_data if c < threshold])
            elif name == 'alta':
                count = len([c for c in confidence_data if c >= threshold])
            else:
                prev_threshold = list(self.confidence_thresholds.values())[
                    list(self.confidence_thresholds.keys()).index(name) - 1
                ]
                count = len([c for c in confidence_data if prev_threshold <= c < threshold])

            distribution[name] = {
                'count': count,
                'percentage': count / len(confidence_data)
            }

        # Correlação confiança vs precisão (apenas dados validados)
        validated_data = [item for item in data if item['is_validated'] and item['llm_confidence'] is not None]
        correlation = None

        if len(validated_data) > 1:
            confidences = [item['llm_confidence'] for item in validated_data]
            accuracies = [1 if item['is_correct'] else 0 for item in validated_data]
            correlation = np.corrcoef(confidences, accuracies)[0, 1]

        return {
            'statistics': stats,
            'distribution': distribution,
            'confidence_accuracy_correlation': correlation,
            'total_samples': len(confidence_data)
        }

    def _analyze_domains(self, data: List[Dict]) -> Dict:
        """
        Analisa performance por domínio.

        Args:
            data: Dados das questões

        Returns:
            Análise por domínio
        """
        domain_stats = defaultdict(lambda: {
            'total': 0, 'validated': 0, 'correct': 0,
            'avg_confidence': [], 'avg_processing_time': []
        })

        for item in data:
            domain = item['domain'] or 'desconhecido'
            stats = domain_stats[domain]

            stats['total'] += 1

            if item['llm_confidence'] is not None:
                stats['avg_confidence'].append(item['llm_confidence'])

            if item['processing_time'] is not None:
                stats['avg_processing_time'].append(item['processing_time'])

            if item['is_validated']:
                stats['validated'] += 1
                if item['is_correct']:
                    stats['correct'] += 1

        # Calcula métricas finais
        domain_analysis = {}
        for domain, stats in domain_stats.items():
            domain_analysis[domain] = {
                'total_questions': stats['total'],
                'validated_questions': stats['validated'],
                'correct_answers': stats['correct'],
                'accuracy': stats['correct'] / stats['validated'] if stats['validated'] > 0 else None,
                'avg_confidence': np.mean(stats['avg_confidence']) if stats['avg_confidence'] else None,
                'avg_processing_time': np.mean(stats['avg_processing_time']) if stats['avg_processing_time'] else None,
                'validation_rate': stats['validated'] / stats['total'] if stats['total'] > 0 else 0
            }

        # Identifica domínios problemáticos
        problematic_domains = []
        for domain, analysis in domain_analysis.items():
            if analysis['accuracy'] is not None and analysis['accuracy'] < 0.7:
                problematic_domains.append({
                    'domain': domain,
                    'accuracy': analysis['accuracy'],
                    'sample_size': analysis['validated_questions']
                })

        return {
            'domain_statistics': domain_analysis,
            'problematic_domains': sorted(problematic_domains, key=lambda x: x['accuracy']),
            'total_domains': len(domain_analysis)
        }

    def _analyze_providers(self, data: List[Dict]) -> Dict:
        """
        Analisa performance por provedor/modelo LLM.

        Args:
            data: Dados das questões

        Returns:
            Análise por provedor
        """
        provider_stats = defaultdict(lambda: defaultdict(lambda: {
            'total': 0, 'validated': 0, 'correct': 0,
            'avg_confidence': [], 'avg_processing_time': []
        }))

        for item in data:
            provider = item['llm_provider'] or 'desconhecido'
            model = item['llm_model'] or 'desconhecido'
            stats = provider_stats[provider][model]

            stats['total'] += 1

            if item['llm_confidence'] is not None:
                stats['avg_confidence'].append(item['llm_confidence'])

            if item['processing_time'] is not None:
                stats['avg_processing_time'].append(item['processing_time'])

            if item['is_validated']:
                stats['validated'] += 1
                if item['is_correct']:
                    stats['correct'] += 1

        # Calcula métricas finais
        provider_analysis = {}
        for provider, models in provider_stats.items():
            provider_analysis[provider] = {}

            for model, stats in models.items():
                provider_analysis[provider][model] = {
                    'total_questions': stats['total'],
                    'validated_questions': stats['validated'],
                    'correct_answers': stats['correct'],
                    'accuracy': stats['correct'] / stats['validated'] if stats['validated'] > 0 else None,
                    'avg_confidence': np.mean(stats['avg_confidence']) if stats['avg_confidence'] else None,
                    'avg_processing_time': np.mean(stats['avg_processing_time']) if stats['avg_processing_time'] else None
                }

        # Ranking de modelos por precisão
        model_ranking = []
        for provider, models in provider_analysis.items():
            for model, stats in models.items():
                if stats['accuracy'] is not None and stats['validated_questions'] >= 5:  # Mínimo de amostras
                    model_ranking.append({
                        'provider': provider,
                        'model': model,
                        'accuracy': stats['accuracy'],
                        'sample_size': stats['validated_questions'],
                        'avg_confidence': stats['avg_confidence']
                    })

        model_ranking.sort(key=lambda x: x['accuracy'], reverse=True)

        return {
            'provider_statistics': provider_analysis,
            'model_ranking': model_ranking,
            'best_model': model_ranking[0] if model_ranking else None,
            'worst_model': model_ranking[-1] if model_ranking else None
        }

    def _analyze_errors(self, data: List[Dict]) -> Dict:
        """
        Analisa padrões de erro nas respostas incorretas.

        Args:
            data: Dados das questões

        Returns:
            Análise de erros
        """
        incorrect_data = [item for item in data if item['is_validated'] and not item['is_correct']]

        if not incorrect_data:
            return {'error': 'Nenhuma resposta incorreta validada disponível'}

        # Análise por tipo de erro
        error_types = Counter()
        error_details = defaultdict(list)

        for item in incorrect_data:
            error_type = item['error_type'] or 'não_classificado'
            error_types[error_type] += 1

            error_details[error_type].append({
                'domain': item['domain'],
                'confidence': item['llm_confidence'],
                'provider': item['llm_provider'],
                'notes': item['reviewer_notes']
            })

        # Detecta padrões automáticos
        hallucination_indicators = self._detect_hallucination_patterns(incorrect_data)

        # Análise de confiança em respostas incorretas
        incorrect_confidences = [item['llm_confidence'] for item in incorrect_data
                               if item['llm_confidence'] is not None]

        confidence_analysis = {
            'avg_confidence_incorrect': np.mean(incorrect_confidences) if incorrect_confidences else None,
            'high_confidence_errors': len([c for c in incorrect_confidences if c >= 80]),
            'low_confidence_errors': len([c for c in incorrect_confidences if c < 60])
        }

        return {
            'total_errors': len(incorrect_data),
            'error_type_distribution': dict(error_types),
            'error_details': dict(error_details),
            'confidence_analysis': confidence_analysis,
            'hallucination_indicators': hallucination_indicators,
            'error_rate_by_domain': self._calculate_error_rate_by_domain(data)
        }

    def _detect_hallucination_patterns(self, incorrect_data: List[Dict]) -> Dict:
        """
        Detecta padrões que indicam alucinação.

        Args:
            incorrect_data: Dados de respostas incorretas

        Returns:
            Indicadores de alucinação
        """
        patterns = {
            'high_confidence_wrong': 0,
            'specific_numbers': 0,
            'detailed_explanations': 0,
            'contradictory_info': 0
        }

        for item in incorrect_data:
            response = item['llm_response'] or ''
            confidence = item['llm_confidence'] or 0

            # Alta confiança em resposta errada
            if confidence >= 85:
                patterns['high_confidence_wrong'] += 1

            # Números específicos (possível invenção)
            if re.search(r'\d{2,}', response):
                patterns['specific_numbers'] += 1

            # Explicações muito detalhadas
            if len(response) > 500:
                patterns['detailed_explanations'] += 1

            # Informações contraditórias (heurística simples)
            if 'mas' in response.lower() or 'porém' in response.lower():
                patterns['contradictory_info'] += 1

        total_errors = len(incorrect_data)
        return {
            pattern: {
                'count': count,
                'percentage': count / total_errors if total_errors > 0 else 0
            }
            for pattern, count in patterns.items()
        }

    def _calculate_error_rate_by_domain(self, data: List[Dict]) -> Dict:
        """
        Calcula taxa de erro por domínio.

        Args:
            data: Dados das questões

        Returns:
            Taxa de erro por domínio
        """
        domain_errors = defaultdict(lambda: {'total': 0, 'errors': 0})

        for item in data:
            if item['is_validated']:
                domain = item['domain'] or 'desconhecido'
                domain_errors[domain]['total'] += 1
                if not item['is_correct']:
                    domain_errors[domain]['errors'] += 1

        return {
            domain: {
                'error_rate': stats['errors'] / stats['total'] if stats['total'] > 0 else 0,
                'total_validated': stats['total'],
                'total_errors': stats['errors']
            }
            for domain, stats in domain_errors.items()
        }

    def _analyze_temporal_patterns(self, data: List[Dict]) -> Dict:
        """
        Analisa padrões temporais nas respostas.

        Args:
            data: Dados das questões

        Returns:
            Análise temporal
        """
        if not data:
            return {'error': 'Nenhum dado disponível'}

        # Agrupa por dia
        daily_stats = defaultdict(lambda: {'total': 0, 'validated': 0, 'correct': 0})

        for item in data:
            date = item['timestamp'][:10]  # YYYY-MM-DD
            daily_stats[date]['total'] += 1

            if item['is_validated']:
                daily_stats[date]['validated'] += 1
                if item['is_correct']:
                    daily_stats[date]['correct'] += 1

        # Calcula tendências
        dates = sorted(daily_stats.keys())
        accuracies = []

        for date in dates:
            stats = daily_stats[date]
            if stats['validated'] > 0:
                accuracy = stats['correct'] / stats['validated']
                accuracies.append(accuracy)

        # Tendência de melhoria/piora
        trend = None
        if len(accuracies) >= 3:
            recent_avg = np.mean(accuracies[-3:])
            older_avg = np.mean(accuracies[:-3]) if len(accuracies) > 3 else np.mean(accuracies[:3])

            if recent_avg > older_avg + 0.05:
                trend = 'melhorando'
            elif recent_avg < older_avg - 0.05:
                trend = 'piorando'
            else:
                trend = 'estável'

        return {
            'daily_statistics': dict(daily_stats),
            'trend': trend,
            'total_days': len(dates),
            'avg_daily_questions': np.mean([stats['total'] for stats in daily_stats.values()]),
            'best_day': max(dates, key=lambda d: daily_stats[d]['correct'] / max(daily_stats[d]['validated'], 1)) if dates else None,
            'worst_day': min(dates, key=lambda d: daily_stats[d]['correct'] / max(daily_stats[d]['validated'], 1)) if dates else None
        }

    def _calculate_quality_metrics(self, data: List[Dict]) -> Dict:
        """
        Calcula métricas de qualidade do sistema.

        Args:
            data: Dados das questões

        Returns:
            Métricas de qualidade
        """
        validated_data = [item for item in data if item['is_validated']]

        if not validated_data:
            return {'error': 'Nenhum dado validado disponível'}

        # Métricas básicas
        total_validated = len(validated_data)
        correct_answers = len([item for item in validated_data if item['is_correct']])

        # Precisão por faixa de confiança
        precision_by_confidence = {}
        for threshold in [60, 70, 80, 90]:
            high_conf_data = [item for item in validated_data if (item['llm_confidence'] or 0) >= threshold]
            if high_conf_data:
                high_conf_correct = len([item for item in high_conf_data if item['is_correct']])
                precision_by_confidence[f'above_{threshold}'] = high_conf_correct / len(high_conf_data)

        # Taxa de "não sei" vs "chute"
        low_conf_data = [item for item in validated_data if (item['llm_confidence'] or 0) < 60]
        low_conf_correct = len([item for item in low_conf_data if item['is_correct']])

        # Detecção de alucinação
        high_conf_wrong = len([item for item in validated_data
                              if (item['llm_confidence'] or 0) >= 80 and not item['is_correct']])

        return {
            'overall_precision': correct_answers / total_validated,
            'precision_by_confidence': precision_by_confidence,
            'low_confidence_accuracy': low_conf_correct / len(low_conf_data) if low_conf_data else None,
            'hallucination_rate': high_conf_wrong / total_validated,
            'validation_coverage': total_validated / len(data) if data else 0,
            'quality_score': self._calculate_quality_score(validated_data)
        }

    def _calculate_quality_score(self, validated_data: List[Dict]) -> float:
        """
        Calcula score de qualidade geral (0-100).

        Args:
            validated_data: Dados validados

        Returns:
            Score de qualidade
        """
        if not validated_data:
            return 0.0

        # Componentes do score
        accuracy = len([item for item in validated_data if item['is_correct']]) / len(validated_data)

        # Penaliza alucinações (alta confiança + erro)
        high_conf_wrong = len([item for item in validated_data
                              if (item['llm_confidence'] or 0) >= 80 and not item['is_correct']])
        hallucination_penalty = (high_conf_wrong / len(validated_data)) * 0.3

        # Bonifica consistência de confiança
        confidences = [item['llm_confidence'] for item in validated_data if item['llm_confidence'] is not None]
        accuracies = [1 if item['is_correct'] else 0 for item in validated_data if item['llm_confidence'] is not None]

        confidence_bonus = 0
        if len(confidences) > 1:
            correlation = abs(np.corrcoef(confidences, accuracies)[0, 1])
            if not np.isnan(correlation):
                confidence_bonus = correlation * 0.2

        # Score final
        quality_score = (accuracy * 0.7 + confidence_bonus - hallucination_penalty) * 100
        return max(0, min(100, quality_score))

    def _generate_recommendations(self, data: List[Dict]) -> List[Dict]:
        """
        Gera recomendações baseadas na análise.

        Args:
            data: Dados das questões

        Returns:
            Lista de recomendações
        """
        recommendations = []
        validated_data = [item for item in data if item['is_validated']]

        if not validated_data:
            return [{'type': 'warning', 'message': 'Nenhum dado validado disponível para gerar recomendações'}]

        # Análise de precisão geral
        accuracy = len([item for item in validated_data if item['is_correct']]) / len(validated_data)

        if accuracy < 0.7:
            recommendations.append({
                'type': 'critical',
                'category': 'accuracy',
                'message': f'Precisão geral baixa ({accuracy:.1%}). Considere revisar prompts e modelos.',
                'priority': 'alta'
            })

        # Análise de alucinação
        high_conf_wrong = len([item for item in validated_data
                              if (item['llm_confidence'] or 0) >= 80 and not item['is_correct']])
        hallucination_rate = high_conf_wrong / len(validated_data)

        if hallucination_rate > 0.1:
            recommendations.append({
                'type': 'warning',
                'category': 'hallucination',
                'message': f'Taxa de alucinação alta ({hallucination_rate:.1%}). Implemente validação adicional para respostas com alta confiança.',
                'priority': 'alta'
            })

        # Análise por domínio
        domain_stats = defaultdict(lambda: {'total': 0, 'correct': 0})
        for item in validated_data:
            domain = item['domain'] or 'desconhecido'
            domain_stats[domain]['total'] += 1
            if item['is_correct']:
                domain_stats[domain]['correct'] += 1

        for domain, stats in domain_stats.items():
            if stats['total'] >= 5:  # Mínimo de amostras
                domain_accuracy = stats['correct'] / stats['total']
                if domain_accuracy < 0.6:
                    recommendations.append({
                        'type': 'warning',
                        'category': 'domain',
                        'message': f'Domínio "{domain}" com baixa precisão ({domain_accuracy:.1%}). Considere prompt especializado.',
                        'priority': 'média'
                    })

        # Análise de confiança
        confidences = [item['llm_confidence'] for item in validated_data if item['llm_confidence'] is not None]
        if confidences:
            avg_confidence = np.mean(confidences)
            if avg_confidence < 70:
                recommendations.append({
                    'type': 'info',
                    'category': 'confidence',
                    'message': f'Confiança média baixa ({avg_confidence:.1f}%). Considere ajustar thresholds de confiança.',
                    'priority': 'baixa'
                })

        # Recomendações de melhoria
        if len(validated_data) < len(data) * 0.3:
            recommendations.append({
                'type': 'info',
                'category': 'validation',
                'message': 'Baixa taxa de validação manual. Aumente a revisão para melhorar a análise.',
                'priority': 'média'
            })

        return recommendations

    def generate_visual_report(self, output_dir: str = "analytics_output") -> bool:
        """
        Gera relatório visual com gráficos.

        Args:
            output_dir: Diretório de saída

        Returns:
            True se gerado com sucesso
        """
        try:
            import os
            os.makedirs(output_dir, exist_ok=True)

            # Gera relatório completo
            report = self.generate_comprehensive_report()

            if 'error' in report:
                return False

            # Salva relatório JSON
            with open(os.path.join(output_dir, 'analytics_report.json'), 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False, default=str)

            # Gera gráficos se matplotlib disponível
            if HAS_MATPLOTLIB:
                try:
                    self._create_visualizations(report, output_dir)
                except Exception as e:
                    if DEBUG:
                        print(f"Erro ao criar visualizações: {e}")
            else:
                if DEBUG:
                    print("Matplotlib não disponível - pulando visualizações")

            return True

        except Exception as e:
            if DEBUG:
                print(f"Erro ao gerar relatório visual: {e}")
            return False

    def _create_visualizations(self, report: Dict, output_dir: str):
        """
        Cria visualizações dos dados.

        Args:
            report: Relatório de análise
            output_dir: Diretório de saída
        """
        plt.style.use('default')

        # Gráfico de precisão por domínio
        if 'domain_analysis' in report and 'domain_statistics' in report['domain_analysis']:
            domains = []
            accuracies = []

            for domain, stats in report['domain_analysis']['domain_statistics'].items():
                if stats['accuracy'] is not None:
                    domains.append(domain)
                    accuracies.append(stats['accuracy'])

            if domains:
                plt.figure(figsize=(10, 6))
                plt.bar(domains, accuracies)
                plt.title('Precisão por Domínio')
                plt.ylabel('Precisão')
                plt.xticks(rotation=45)
                plt.tight_layout()
                plt.savefig(os.path.join(output_dir, 'accuracy_by_domain.png'))
                plt.close()

        # Gráfico de distribuição de confiança
        if 'confidence_analysis' in report and 'distribution' in report['confidence_analysis']:
            categories = list(report['confidence_analysis']['distribution'].keys())
            percentages = [report['confidence_analysis']['distribution'][cat]['percentage']
                          for cat in categories]

            plt.figure(figsize=(8, 8))
            plt.pie(percentages, labels=categories, autopct='%1.1f%%')
            plt.title('Distribuição de Confiança')
            plt.savefig(os.path.join(output_dir, 'confidence_distribution.png'))
            plt.close()

        if DEBUG:
            print(f"Visualizações salvas em {output_dir}")
