"""
Simplified module for capturing screen content from a specific window.
"""
import time
import numpy as np
import cv2
import pyautogui
import win32gui
import win32con
import win32ui
import win32api
import ctypes
import tkinter as tk
from tkinter import ttk
from PIL import Image, ImageGrab
import mss
import mss.tools
from config import DEBUG

class ScreenCapture:
    def __init__(self):
        self.last_capture = None
        self.last_capture_time = 0
        self.window_handle = None
        self.window_title = None
        self.selected_area = None  # (x, y, width, height)

    def get_window_list(self):
        """Get a list of all visible windows with titles."""
        windows = []

        def callback(hwnd, _):
            if win32gui.IsWindowVisible(hwnd) and win32gui.GetWindowText(hwnd):
                title = win32gui.GetWindowText(hwnd)
                if title and len(title) > 0:  # Only include windows with non-empty titles
                    windows.append((hwnd, title))
            return True

        win32gui.EnumWindows(callback, None)
        return windows

    def select_window(self):
        """Show a dialog to select a window from the list of available windows."""
        windows = self.get_window_list()

        if not windows:
            print("No windows found")
            return None

        # Create a simple dialog to select a window
        root = tk.Tk()
        root.title("Select Window")
        root.geometry("600x400")

        # Add a label
        tk.Label(root, text="Select a window to capture:").pack(pady=10)

        # Create a listbox with scrollbar
        frame = tk.Frame(root)
        scrollbar = tk.Scrollbar(frame)
        listbox = tk.Listbox(frame, width=80, height=15, yscrollcommand=scrollbar.set)
        scrollbar.config(command=listbox.yview)

        # Add windows to the listbox
        for i, (_, title) in enumerate(windows):
            listbox.insert(i, title)

        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        frame.pack(pady=10, padx=10, fill=tk.BOTH, expand=True)

        # Variable to store the selected window
        selected_window = [None]

        # Function to handle selection
        def on_select():
            selection = listbox.curselection()
            if selection:
                index = selection[0]
                selected_window[0] = windows[index]
                root.destroy()

        # Add a button to confirm selection
        tk.Button(root, text="Select", command=on_select).pack(pady=10)

        # Center the window
        root.update_idletasks()
        width = root.winfo_width()
        height = root.winfo_height()
        x = (root.winfo_screenwidth() // 2) - (width // 2)
        y = (root.winfo_screenheight() // 2) - (height // 2)
        root.geometry('{}x{}+{}+{}'.format(width, height, x, y))

        # Wait for the dialog to close
        root.mainloop()

        if selected_window[0]:
            self.window_handle = selected_window[0][0]
            self.window_title = selected_window[0][1]
            return self.window_handle

        return None

    def capture_window_win32(self):
        """Capture a window using Win32 API (more reliable)."""
        if not self.window_handle:
            if not self.select_window():
                return None

        try:
            # Get window dimensions
            left, top, right, bottom = win32gui.GetWindowRect(self.window_handle)
            width = right - left
            height = bottom - top

            # Bring the window to the foreground
            win32gui.SetForegroundWindow(self.window_handle)
            time.sleep(0.2)  # Give it time to come to the foreground

            # Create device context
            hwnd_dc = win32gui.GetWindowDC(self.window_handle)
            mfc_dc = win32ui.CreateDCFromHandle(hwnd_dc)
            save_dc = mfc_dc.CreateCompatibleDC()

            # Create bitmap object
            save_bitmap = win32ui.CreateBitmap()
            save_bitmap.CreateCompatibleBitmap(mfc_dc, width, height)
            save_dc.SelectObject(save_bitmap)

            # Copy screen to bitmap
            save_dc.BitBlt((0, 0), (width, height), mfc_dc, (0, 0), win32con.SRCCOPY)

            # Convert bitmap to numpy array
            bmpinfo = save_bitmap.GetInfo()
            bmpstr = save_bitmap.GetBitmapBits(True)
            img = np.frombuffer(bmpstr, dtype='uint8')
            img.shape = (bmpinfo['bmHeight'], bmpinfo['bmWidth'], 4)

            # Clean up
            win32gui.DeleteObject(save_bitmap.GetHandle())
            save_dc.DeleteDC()
            mfc_dc.DeleteDC()
            win32gui.ReleaseDC(self.window_handle, hwnd_dc)

            # Convert to RGB
            img = cv2.cvtColor(img, cv2.COLOR_BGRA2RGB)

            # Store the capture
            self.last_capture = img
            self.last_capture_time = time.time()

            if DEBUG:
                print(f"Captured window: {self.window_title} ({width}x{height})")

            return img

        except Exception as e:
            if DEBUG:
                print(f"Error capturing window with Win32: {e}")
            return None

    def capture_window_pil(self):
        """Capture a window using PIL (alternative method)."""
        if not self.window_handle:
            if not self.select_window():
                return None

        try:
            # Get window dimensions
            left, top, right, bottom = win32gui.GetWindowRect(self.window_handle)

            # Bring the window to the foreground
            win32gui.SetForegroundWindow(self.window_handle)
            time.sleep(0.2)  # Give it time to come to the foreground

            # Capture the screen area
            screenshot = ImageGrab.grab(bbox=(left, top, right, bottom))
            img = np.array(screenshot)

            # Convert to RGB if needed
            if len(img.shape) == 3 and img.shape[2] == 4:
                img = cv2.cvtColor(img, cv2.COLOR_BGRA2RGB)

            # Store the capture
            self.last_capture = img
            self.last_capture_time = time.time()

            if DEBUG:
                print(f"Captured window with PIL: {self.window_title} ({right-left}x{bottom-top})")

            return img

        except Exception as e:
            if DEBUG:
                print(f"Error capturing window with PIL: {e}")
            return None

    def capture_window_mss(self):
        """Capture a window using MSS library (works with private browsing)."""
        if not self.window_handle:
            if not self.select_window():
                return None

        try:
            # Get window dimensions
            left, top, right, bottom = win32gui.GetWindowRect(self.window_handle)
            width = right - left
            height = bottom - top

            # Bring the window to the foreground
            win32gui.SetForegroundWindow(self.window_handle)
            time.sleep(0.2)  # Give it time to come to the foreground

            # Capture the screen area using MSS
            with mss.mss() as sct:
                monitor = {"top": top, "left": left, "width": width, "height": height}
                screenshot = sct.grab(monitor)

                # Convert to numpy array
                img = np.array(screenshot)

                # Convert BGRA to RGB
                img = cv2.cvtColor(img, cv2.COLOR_BGRA2RGB)

                # Store the capture
                self.last_capture = img
                self.last_capture_time = time.time()

                if DEBUG:
                    print(f"Captured window with MSS: {self.window_title} ({width}x{height})")

                return img

        except Exception as e:
            if DEBUG:
                print(f"Error capturing window with MSS: {e}")
            return None

    def capture_window_printscreen(self):
        """Capture a window using PrintScreen and clipboard (works with private browsing)."""
        if not self.window_handle:
            if not self.select_window():
                return None

        try:
            # Get window dimensions
            left, top, right, bottom = win32gui.GetWindowRect(self.window_handle)

            # Bring the window to the foreground
            win32gui.SetForegroundWindow(self.window_handle)
            time.sleep(0.2)  # Give it time to come to the foreground

            # Use Alt+PrintScreen to capture active window to clipboard
            pyautogui.hotkey('alt', 'printscreen')
            time.sleep(0.5)  # Wait for the clipboard to be updated

            # Get image from clipboard
            screenshot = ImageGrab.grabclipboard()

            if screenshot is None:
                if DEBUG:
                    print("Failed to get image from clipboard")
                return None

            # Convert to numpy array
            img = np.array(screenshot)

            # Convert to RGB if needed
            if len(img.shape) == 3 and img.shape[2] == 4:
                img = cv2.cvtColor(img, cv2.COLOR_BGRA2RGB)
            elif len(img.shape) == 3 and img.shape[2] == 3:
                img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)

            # Store the capture
            self.last_capture = img
            self.last_capture_time = time.time()

            if DEBUG:
                print(f"Captured window with PrintScreen: {self.window_title}")

            return img

        except Exception as e:
            if DEBUG:
                print(f"Error capturing window with PrintScreen: {e}")
            return None

    def select_area(self, image=None):
        """Allow the user to select a specific area of the window."""
        if image is None:
            # Capture the window first
            image = self.capture_window()
            if image is None:
                print("Failed to capture window for area selection")
                return None

        # Make a copy of the image for drawing
        img_copy = image.copy()
        window_name = "Select Area (Drag to select, press Enter to confirm, Esc to cancel)"

        # Variables to store the selection
        selection = [None]  # Using a list to store by reference
        drawing = False
        start_x, start_y = -1, -1

        def mouse_callback(event, x, y, flags, param):
            nonlocal drawing, start_x, start_y, img_copy

            if event == cv2.EVENT_LBUTTONDOWN:
                drawing = True
                start_x, start_y = x, y
                img_copy = image.copy()

            elif event == cv2.EVENT_MOUSEMOVE:
                if drawing:
                    # Draw a rectangle on the copy
                    img_temp = image.copy()
                    cv2.rectangle(img_temp, (start_x, start_y), (x, y), (0, 255, 0), 2)
                    img_copy = img_temp

            elif event == cv2.EVENT_LBUTTONUP:
                drawing = False
                # Save the selection
                if start_x != x and start_y != y:  # Ensure it's not a single point
                    # Ensure coordinates are in the right order (top-left to bottom-right)
                    x1, y1 = min(start_x, x), min(start_y, y)
                    x2, y2 = max(start_x, x), max(start_y, y)
                    selection[0] = (x1, y1, x2 - x1, y2 - y1)  # (x, y, width, height)
                    # Draw the final rectangle
                    cv2.rectangle(img_copy, (x1, y1), (x2, y2), (0, 255, 0), 2)

        # Create a window and set the callback
        cv2.namedWindow(window_name, cv2.WINDOW_NORMAL)
        cv2.setMouseCallback(window_name, mouse_callback)

        # Display instructions
        font = cv2.FONT_HERSHEY_SIMPLEX
        instructions = "Drag to select an area, press Enter to confirm, Esc to cancel"
        cv2.putText(img_copy, instructions, (10, 30), font, 0.7, (0, 255, 0), 2)

        # Show the image and wait for user input
        while True:
            cv2.imshow(window_name, cv2.cvtColor(img_copy, cv2.COLOR_RGB2BGR))
            key = cv2.waitKey(1) & 0xFF

            if key == 13:  # Enter key
                if selection[0]:
                    self.selected_area = selection[0]
                    print(f"Area selected: {self.selected_area}")
                    break
            elif key == 27:  # Esc key
                print("Area selection cancelled")
                self.selected_area = None
                break

        cv2.destroyAllWindows()
        return self.selected_area

    def capture_selected_area(self):
        """Capture only the selected area of the window."""
        if not self.selected_area:
            print("No area selected. Please select an area first.")
            return None

        # Capture the full window
        full_image = self.capture_window()
        if full_image is None:
            print("Failed to capture window")
            return None

        # Extract the selected area
        x, y, width, height = self.selected_area
        try:
            area_image = full_image[y:y+height, x:x+width]

            # Store the capture
            self.last_capture = area_image
            self.last_capture_time = time.time()

            if DEBUG:
                print(f"Captured selected area: ({width}x{height}) from {self.window_title}")
                # Save for debugging
                cv2.imwrite("selected_area.png", cv2.cvtColor(area_image, cv2.COLOR_RGB2BGR))

            return area_image
        except Exception as e:
            print(f"Error extracting selected area: {e}")
            return None

    def capture_window(self):
        """Try multiple methods to capture a window."""
        # First try MSS method (best for private browsing)
        img = self.capture_window_mss()

        # If that fails, try PrintScreen method
        if img is None:
            img = self.capture_window_printscreen()

        # If that fails, try Win32 method
        if img is None:
            img = self.capture_window_win32()

        # If that fails, try PIL method
        if img is None:
            img = self.capture_window_pil()

        # If we have a selected area and successfully captured the window, crop to that area
        if img is not None and self.selected_area is not None:
            x, y, width, height = self.selected_area
            try:
                img = img[y:y+height, x:x+width]
            except Exception as e:
                print(f"Error cropping to selected area: {e}")

        return img

    def save_capture(self, filename="capture.png"):
        """Save the last capture to a file."""
        if self.last_capture is not None:
            cv2.imwrite(filename, cv2.cvtColor(self.last_capture, cv2.COLOR_RGB2BGR))
            if DEBUG:
                print(f"Saved capture to {filename}")
            return True
        return False

    def get_window_title(self, window_handle):
        """Get the title of a window by its handle."""
        try:
            return win32gui.GetWindowText(window_handle)
        except Exception as e:
            if DEBUG:
                print(f"Error getting window title: {e}")
            return "Unknown Window"

# For testing
if __name__ == "__main__":
    capture = ScreenCapture()
    img = capture.capture_window()
    if img is not None:
        capture.save_capture()

        # Show the captured image
        cv2.imshow("Captured Window", cv2.cvtColor(img, cv2.COLOR_RGB2BGR))
        cv2.waitKey(0)
        cv2.destroyAllWindows()

        print("Capture successful")
    else:
        print("Capture failed")
