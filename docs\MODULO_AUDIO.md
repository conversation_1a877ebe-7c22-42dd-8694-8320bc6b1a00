# Módulo de Processamento de Áudio

## 📋 Visão Geral

O módulo de áudio do Python Question Helper adiciona capacidades avançadas de processamento de áudio, permitindo capturar e converter fala em texto para provas que incluem conteúdo narrado ou instruções faladas.

## 🎯 Funcionalidades Principais

### **🎤 Captura de Áudio**
- Captura de áudio do sistema em tempo real
- Suporte a múltiplos dispositivos de entrada
- Configuração de qualidade e duração de captura
- Detecção automática de atividade de voz (VAD)
- Detecção de silêncio para parar gravação automaticamente

### **🗣️ Speech-to-Text (STT)**
- **4 provedores suportados**:
  - **Whisper** (OpenAI) - Recomendado, funciona offline
  - **Google Speech-to-Text** - Alta precisão, requer API key
  - **Azure Speech Services** - Integração com Microsoft Azure
  - **AWS Transcribe** - Serviço da Amazon Web Services

### **🔧 Processamento de Áudio**
- Redução de ruído automática
- Melhoria de voz humana
- Normalização de volume
- Filtros passa-banda para frequências de voz
- Controle de ganho ajustável

### **🎛️ Interface de Controle**
- Toggle para ativar/desativar captura
- Indicadores visuais de status
- Controles de configuração em tempo real
- Seleção de provedor STT
- Ajuste de parâmetros de áudio

## 🚀 Casos de Uso

### **📚 Provas de Idiomas**
- **Listening Comprehension**: Captura questões narradas
- **Pronunciation Tests**: Análise de fala do usuário
- **Audio Instructions**: Instruções faladas convertidas para texto

### **🎓 Provas Acadêmicas**
- **Questões Narradas**: Matemática, ciências com explicações em áudio
- **Instruções Faladas**: Comandos e orientações em áudio
- **Conteúdo Multimídia**: Vídeos com narração

### **♿ Acessibilidade**
- **Deficiência Visual**: Questões lidas em voz alta
- **Dificuldades de Leitura**: Suporte a áudio para texto
- **Múltiplas Modalidades**: Combinação de texto e áudio

### **🌐 Provas Internacionais**
- **Múltiplos Idiomas**: Suporte a pt-BR, en-US, es-ES, fr-FR, de-DE, it-IT
- **Certificações**: TOEFL, IELTS, DELE, DELF, etc.
- **Provas Corporativas**: Treinamentos com áudio

## ⚙️ Configuração e Instalação

### **📦 Dependências Básicas**
```bash
# Instalação mínima (Whisper + captura)
pip install sounddevice librosa openai-whisper

# Instalação completa
pip install -r requirements-audio.txt
```

### **🔧 Configuração via Variáveis de Ambiente**
```bash
# Habilitar módulo de áudio
ENABLE_AUDIO_MODULE=true

# Configurações de captura
AUDIO_SAMPLE_RATE=44100
AUDIO_CHANNELS=2
AUDIO_CAPTURE_DURATION=30
AUDIO_MIN_DURATION=2

# Provedor STT
STT_PROVIDER=whisper
STT_LANGUAGE=pt-BR
STT_MODEL_SIZE=base

# APIs (se usar provedores cloud)
GOOGLE_SPEECH_API_KEY=your_key_here
AZURE_SPEECH_KEY=your_key_here
AZURE_SPEECH_REGION=your_region_here
```

### **🎛️ Configuração via Interface**
- **Provedor STT**: Dropdown com opções disponíveis
- **Idioma**: Seleção entre idiomas suportados
- **Duração**: 5-120 segundos de captura
- **Ganho**: 0.5x a 2.0x amplificação
- **Filtros**: Redução de ruído e melhoria de voz

## 🔍 Provedores de Speech-to-Text

### **1. 🤖 Whisper (OpenAI) - Recomendado**
```python
# Configuração
STT_PROVIDER=whisper
STT_MODEL_SIZE=base  # tiny, base, small, medium, large

# Vantagens
✅ Funciona offline
✅ Boa precisão
✅ Múltiplos idiomas
✅ Gratuito
✅ Fácil instalação

# Desvantagens
❌ Requer mais recursos computacionais
❌ Modelos maiores são lentos
```

### **2. 🌐 Google Speech-to-Text**
```python
# Configuração
STT_PROVIDER=google
GOOGLE_SPEECH_API_KEY=path/to/credentials.json

# Vantagens
✅ Alta precisão
✅ Rápido
✅ Suporte a muitos idiomas
✅ Recursos avançados

# Desvantagens
❌ Requer internet
❌ Custo por uso
❌ Configuração mais complexa
```

### **3. 🔷 Azure Speech Services**
```python
# Configuração
STT_PROVIDER=azure
AZURE_SPEECH_KEY=your_key
AZURE_SPEECH_REGION=your_region

# Vantagens
✅ Integração com Microsoft
✅ Boa precisão
✅ Recursos empresariais

# Desvantagens
❌ Requer conta Azure
❌ Custo por uso
❌ Configuração específica
```

### **4. 🟠 AWS Transcribe**
```python
# Configuração
STT_PROVIDER=aws
AWS_ACCESS_KEY=your_access_key
AWS_SECRET_KEY=your_secret_key

# Vantagens
✅ Integração com AWS
✅ Escalabilidade

# Desvantagens
❌ Configuração complexa
❌ Requer S3 para arquivos
❌ Custo por uso
```

## 🎛️ Interface de Usuário

### **🎤 Controles Principais**
- **Botão de Gravação**: Inicia/para captura de áudio
- **Indicador de Status**: Visual feedback do estado atual
- **Barra de Progresso**: Mostra atividade durante processamento

### **⚙️ Configurações**
- **Provedor STT**: Seleção entre whisper, google, azure, aws
- **Idioma**: pt-BR, en-US, es-ES, fr-FR, de-DE, it-IT
- **Duração**: Tempo máximo de captura (5-120s)
- **Ganho**: Amplificação do áudio (0.5x-2.0x)
- **Filtros**: Redução de ruído e melhoria de voz

### **📊 Status do Sistema**
- **Status do Módulo**: Disponibilidade das dependências
- **Status STT**: Estado do provedor selecionado
- **Último Texto**: Prévia do último texto extraído

## 🔧 Integração com Sistema Principal

### **📝 Combinação com OCR**
```python
# Texto combinado de OCR + Áudio
combined_text = f"{ocr_text}\n\nÁudio: {audio_text}"

# Processamento pelo LLM
response = llm.process_question(combined_text)
```

### **📊 Logging Integrado**
- Questões de áudio são registradas no mesmo sistema
- Metadados incluem provedor STT usado
- Tempo de processamento de áudio
- Qualidade da transcrição

### **🔄 Detecção de Mudanças**
- Áudio é processado quando detectada mudança na tela
- Combinação inteligente de triggers visuais e auditivos
- Evita processamento desnecessário

## 📈 Performance e Otimização

### **⚡ Configurações de Performance**

#### **Rápida (Baixa Latência)**
```python
STT_PROVIDER=whisper
STT_MODEL_SIZE=tiny
AUDIO_CAPTURE_DURATION=15
ENABLE_NOISE_REDUCTION=false
```

#### **Balanceada (Recomendada)**
```python
STT_PROVIDER=whisper
STT_MODEL_SIZE=base
AUDIO_CAPTURE_DURATION=30
ENABLE_NOISE_REDUCTION=true
```

#### **Alta Precisão**
```python
STT_PROVIDER=google  # ou whisper com model=large
AUDIO_CAPTURE_DURATION=60
ENABLE_VOICE_ENHANCEMENT=true
```

### **📊 Métricas Esperadas**
- **Whisper Tiny**: ~2-5 segundos de processamento
- **Whisper Base**: ~5-10 segundos de processamento
- **Google/Azure**: ~1-3 segundos (com internet)
- **Precisão Geral**: 85-95% (varia por idioma e qualidade)

## 🛡️ Privacidade e Segurança

### **🔒 Configurações de Privacidade**
```python
# Não salvar gravações
AUDIO_SAVE_RECORDINGS=false

# Deletar após processamento
AUDIO_DELETE_AFTER_PROCESS=true

# Criptografar armazenamento temporário
AUDIO_ENCRYPT_STORAGE=true
```

### **🌐 Provedores Cloud vs Local**
- **Whisper**: Processamento 100% local, dados não saem do PC
- **Google/Azure/AWS**: Dados enviados para cloud (criptografados)
- **Recomendação**: Use Whisper para máxima privacidade

## 🔧 Troubleshooting

### **❌ Problemas Comuns**

#### **Módulo não disponível**
```bash
# Verificar instalação
pip install sounddevice librosa openai-whisper

# Linux: instalar PortAudio
sudo apt-get install portaudio19-dev

# Verificar configuração
ENABLE_AUDIO_MODULE=true
```

#### **Sem dispositivos de áudio**
```python
# Listar dispositivos disponíveis
import sounddevice as sd
print(sd.query_devices())

# Configurar dispositivo específico
AUDIO_DEVICE_INDEX=1
```

#### **Whisper muito lento**
```python
# Usar modelo menor
STT_MODEL_SIZE=tiny

# Reduzir duração
AUDIO_CAPTURE_DURATION=15

# Desabilitar filtros
ENABLE_NOISE_REDUCTION=false
```

#### **Google Speech não funciona**
```bash
# Verificar credenciais
export GOOGLE_APPLICATION_CREDENTIALS=path/to/key.json

# Verificar permissões da API
# Habilitar Speech-to-Text API no Google Cloud Console
```

## 🚀 Exemplos de Uso

### **📝 Uso Básico**
```python
from src.audio import AudioProcessor

# Inicializar processador
processor = AudioProcessor(callback_on_text=print)

# Iniciar captura
processor.start_recording()

# Parar quando necessário
processor.stop_recording()
```

### **⚙️ Configuração Avançada**
```python
# Configurar provedor específico
os.environ['STT_PROVIDER'] = 'google'
os.environ['STT_LANGUAGE'] = 'en-US'

# Inicializar com configurações
processor = AudioProcessor()

# Verificar status
status = processor.get_status()
print(f"Disponível: {status['available']}")
```

### **🎛️ Interface Completa**
```python
from src.ui.audio_controls import AudioControlsWidget

# Criar controles
controls = AudioControlsWidget()

# Conectar processador
controls.set_audio_processor(processor)

# Conectar sinais
controls.audio_text_received.connect(handle_audio_text)
```

## 🎉 Conclusão

O módulo de áudio transforma o Python Question Helper em uma solução completa para provas que incluem conteúdo falado, oferecendo:

- **🎤 Captura profissional** de áudio
- **🗣️ Conversão precisa** de fala para texto
- **🔧 Processamento avançado** de áudio
- **🎛️ Interface intuitiva** de controle
- **🔒 Opções de privacidade** flexíveis
- **⚡ Performance otimizada** para diferentes cenários

**Resultado**: Sistema universal que funciona com qualquer tipo de prova, seja visual, auditiva ou combinada! 🚀
