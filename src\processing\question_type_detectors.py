"""
Detectores específicos para diferentes tipos de questão.
"""
import cv2
import numpy as np
import hashlib
import re
from ..core.config import DEBUG
from ..core.monitor_config import MonitorConfig


class QuestionTypeDetectors:
    """
    Classe com métodos específicos para detectar mudanças em diferentes tipos de questão.
    """
    
    @staticmethod
    def extract_matching_elements_hash(image):
        """
        Extrai hash de elementos de questões de relacionar colunas.
        
        Args:
            image: A imagem capturada
            
        Returns:
            Hash dos elementos de matching ou None se não detectado
        """
        try:
            # Converte para escala de cinza
            gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
            
            # Obtém regiões das colunas
            y1, y2, x1, x2 = MonitorConfig.get_region_coordinates(gray.shape, "left_column")
            left_column = gray[y1:y2, x1:x2]
            
            y1, y2, x1, x2 = MonitorConfig.get_region_coordinates(gray.shape, "right_column")
            right_column = gray[y1:y2, x1:x2]
            
            y1, y2, x1, x2 = MonitorConfig.get_region_coordinates(gray.shape, "connections")
            connections = gray[y1:y2, x1:x2]
            
            # Detecta linhas de conexão
            edges = cv2.Canny(connections, 50, 150)
            lines = cv2.HoughLines(edges, 1, np.pi/180, threshold=50)
            
            # Cria representação das conexões
            connections_data = []
            if lines is not None:
                for line in lines:
                    rho, theta = line[0]
                    connections_data.append((round(rho, 2), round(theta, 2)))
            
            # Combina dados das colunas e conexões
            left_hash = hashlib.md5(left_column.tobytes()).hexdigest()
            right_hash = hashlib.md5(right_column.tobytes()).hexdigest()
            connections_str = str(sorted(connections_data))
            
            combined_data = f"{left_hash}|{right_hash}|{connections_str}"
            matching_hash = hashlib.md5(combined_data.encode()).hexdigest()
            
            if DEBUG:
                print(f"Hash matching: {matching_hash[:8]}... ({len(connections_data)} conexões)")
            
            return matching_hash
            
        except Exception as e:
            if DEBUG:
                print(f"Erro ao extrair hash de matching: {e}")
            return None

    @staticmethod
    def extract_drag_drop_elements_hash(image):
        """
        Extrai hash de elementos de questões drag and drop.
        
        Args:
            image: A imagem capturada
            
        Returns:
            Hash dos elementos drag&drop ou None se não detectado
        """
        try:
            # Converte para escala de cinza
            gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
            
            # Obtém regiões de itens e zonas
            y1, y2, x1, x2 = MonitorConfig.get_region_coordinates(gray.shape, "drag_items")
            drag_items = gray[y1:y2, x1:x2]
            
            y1, y2, x1, x2 = MonitorConfig.get_region_coordinates(gray.shape, "drop_zones")
            drop_zones = gray[y1:y2, x1:x2]
            
            # Detecta contornos em ambas as regiões
            edges_items = cv2.Canny(drag_items, 50, 150)
            edges_zones = cv2.Canny(drop_zones, 50, 150)
            
            contours_items, _ = cv2.findContours(edges_items, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            contours_zones, _ = cv2.findContours(edges_zones, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            # Analisa posições dos elementos
            items_positions = []
            for contour in contours_items:
                if cv2.contourArea(contour) > 500:  # Filtra contornos pequenos
                    x, y, w, h = cv2.boundingRect(contour)
                    items_positions.append((x, y, w, h))
            
            zones_positions = []
            for contour in contours_zones:
                if cv2.contourArea(contour) > 500:
                    x, y, w, h = cv2.boundingRect(contour)
                    zones_positions.append((x, y, w, h))
            
            # Combina informações
            items_str = str(sorted(items_positions))
            zones_str = str(sorted(zones_positions))
            combined_data = f"{items_str}|{zones_str}"
            
            drag_drop_hash = hashlib.md5(combined_data.encode()).hexdigest()
            
            if DEBUG:
                print(f"Hash drag&drop: {drag_drop_hash[:8]}... ({len(items_positions)} itens, {len(zones_positions)} zonas)")
            
            return drag_drop_hash
            
        except Exception as e:
            if DEBUG:
                print(f"Erro ao extrair hash de drag&drop: {e}")
            return None

    @staticmethod
    def extract_ordering_elements_hash(image):
        """
        Extrai hash de elementos de questões de ordenação.
        
        Args:
            image: A imagem capturada
            
        Returns:
            Hash dos elementos de ordenação ou None se não detectado
        """
        try:
            # Converte para escala de cinza
            gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
            
            # Obtém região dos itens
            y1, y2, x1, x2 = MonitorConfig.get_region_coordinates(gray.shape, "items")
            items_region = gray[y1:y2, x1:x2]
            
            # Detecta elementos ordenáveis
            edges = cv2.Canny(items_region, 50, 150)
            contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            # Analisa posições verticais (ordem)
            items_order = []
            for contour in contours:
                if cv2.contourArea(contour) > 300:
                    x, y, w, h = cv2.boundingRect(contour)
                    # Usa posição Y para determinar ordem
                    items_order.append((y, x, w, h))
            
            # Ordena por posição Y (ordem vertical)
            items_order.sort(key=lambda item: item[0])
            
            # Cria hash baseado na sequência
            sequence_str = str(items_order)
            ordering_hash = hashlib.md5(sequence_str.encode()).hexdigest()
            
            if DEBUG:
                print(f"Hash ordering: {ordering_hash[:8]}... ({len(items_order)} itens)")
            
            return ordering_hash
            
        except Exception as e:
            if DEBUG:
                print(f"Erro ao extrair hash de ordering: {e}")
            return None

    @staticmethod
    def extract_input_fields_hash(image):
        """
        Extrai hash de campos de entrada (fill in the blanks).
        
        Args:
            image: A imagem capturada
            
        Returns:
            Hash dos campos de entrada ou None se não detectado
        """
        try:
            # Converte para escala de cinza
            gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
            
            # Obtém região dos campos
            y1, y2, x1, x2 = MonitorConfig.get_region_coordinates(gray.shape, "input_fields")
            fields_region = gray[y1:y2, x1:x2]
            
            # Detecta campos de entrada (retângulos vazios)
            edges = cv2.Canny(fields_region, 50, 150)
            contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            # Filtra campos de entrada
            input_fields = []
            for contour in contours:
                area = cv2.contourArea(contour)
                if 500 < area < 5000:  # Tamanho típico de campo
                    x, y, w, h = cv2.boundingRect(contour)
                    # Verifica se é retangular
                    aspect_ratio = w / h
                    if 2 < aspect_ratio < 10:  # Campos são mais largos que altos
                        input_fields.append((x, y, w, h))
            
            # Ordena campos por posição
            input_fields.sort(key=lambda field: (field[1], field[0]))  # Y primeiro, depois X
            
            # Cria hash dos campos
            fields_str = str(input_fields)
            fields_hash = hashlib.md5(fields_str.encode()).hexdigest()
            
            if DEBUG:
                print(f"Hash input fields: {fields_hash[:8]}... ({len(input_fields)} campos)")
            
            return fields_hash
            
        except Exception as e:
            if DEBUG:
                print(f"Erro ao extrair hash de campos: {e}")
            return None

    @staticmethod
    def extract_text_area_hash(image):
        """
        Extrai hash de área de texto livre (essay).
        
        Args:
            image: A imagem capturada
            
        Returns:
            Hash da área de texto ou None se não detectado
        """
        try:
            # Converte para escala de cinza
            gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
            
            # Obtém região da área de texto
            y1, y2, x1, x2 = MonitorConfig.get_region_coordinates(gray.shape, "text_area")
            text_area = gray[y1:y2, x1:x2]
            
            # Calcula hash da área de texto
            text_hash = hashlib.md5(text_area.tobytes()).hexdigest()
            
            if DEBUG:
                print(f"Hash text area: {text_hash[:8]}...")
            
            return text_hash
            
        except Exception as e:
            if DEBUG:
                print(f"Erro ao extrair hash de área de texto: {e}")
            return None

    @staticmethod
    def extract_general_interactive_hash(image):
        """
        Extrai hash geral de elementos interativos.
        
        Args:
            image: A imagem capturada
            
        Returns:
            Hash geral dos elementos interativos
        """
        try:
            # Converte para escala de cinza
            gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
            
            # Detecta todos os elementos interativos
            edges = cv2.Canny(gray, 50, 150)
            contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            # Filtra elementos por tamanho
            interactive_elements = []
            for contour in contours:
                area = cv2.contourArea(contour)
                if 200 < area < 10000:  # Elementos interativos típicos
                    x, y, w, h = cv2.boundingRect(contour)
                    interactive_elements.append((x, y, w, h))
            
            # Ordena elementos
            interactive_elements.sort()
            
            # Cria hash
            elements_str = str(interactive_elements)
            general_hash = hashlib.md5(elements_str.encode()).hexdigest()
            
            if DEBUG:
                print(f"Hash geral interativo: {general_hash[:8]}... ({len(interactive_elements)} elementos)")
            
            return general_hash
            
        except Exception as e:
            if DEBUG:
                print(f"Erro ao extrair hash geral: {e}")
            return None
