"""
Módulo para monitorar mudanças na tela e detectar quando o usuário muda de questão.
"""
import time
import threading
import cv2
import numpy as np
import win32gui
from .screen_capture_simple import ScreenCapture
from .question_type_detectors import QuestionTypeDetectors
from .essay_detector import EssayQuestionDetector
from ..core.config import DEBUG
from ..core.monitor_config import MonitorConfig, DEFAULT_CONFIG

class ScreenMonitor:
    def __init__(self, callback_on_change=None, config_scenario="general"):
        """
        Inicializa o monitor de tela.

        Args:
            callback_on_change: Função a ser chamada quando uma mudança significativa for detectada
            config_scenario: Cenário de configuração ("general", "sensitive", "fast", "conservative")
        """
        self.screen_capture = ScreenCapture()
        self.callback_on_change = callback_on_change
        self.monitoring = False
        self.monitor_thread = None
        self.last_image = None
        self.last_image_hash = None
        self.window_handle = None
        self.paused = False  # Flag para pausar temporariamente o monitoramento

        # Carrega configurações otimizadas
        config = MonitorConfig.get_optimized_config(config_scenario)
        self.change_threshold = config["visual_threshold"]
        self.check_interval = config["check_interval"]
        self.progress_threshold = config["progress_threshold"]
        self.timer_threshold = config["timer_threshold"]
        self.text_threshold = config["text_threshold"]
        self.alternatives_threshold = config["alternatives_threshold"]
        self.detect_any_change = True  # Por padrão, detecta qualquer alteração visual

        # Atributos para detecção avançada
        self.last_progress_bar = None  # Estado da barra de progresso
        self.last_timer_value = None  # Valor do cronômetro
        self.last_question_text_hash = None  # Hash do texto da questão
        self.last_alternatives_hash = None  # Hash das alternativas

        # Novos atributos para tipos de questão expandidos
        self.last_question_type = None  # Tipo de questão detectado
        self.last_interactive_elements_hash = None  # Hash de elementos interativos
        self.last_connections_hash = None  # Hash de conexões (matching)
        self.last_positions_hash = None  # Hash de posições (drag&drop)
        self.last_input_fields_hash = None  # Hash de campos de entrada

        # Configuração atual
        self.config_scenario = config_scenario
        self.current_question_type = "auto_detect"  # Tipo atual da questão

        # Inicializa detector de questões dissertativas
        self.essay_detector = EssayQuestionDetector()
        self.last_essay_detection = None  # Resultado da última detecção de questão dissertativa

        print(f"ScreenMonitor inicializado com configuração '{config_scenario}':")
        print(f"  • Intervalo de verificação: {self.check_interval}s")
        print(f"  • Threshold visual: {self.change_threshold}")
        print(f"  • Threshold progresso: {self.progress_threshold}")
        print(f"  • Threshold timer: {self.timer_threshold}s")
        print(f"  • Threshold texto: {self.text_threshold}")
        print(f"  • Threshold alternativas: {self.alternatives_threshold}")

    def start_monitoring(self, window_handle=None):
        """
        Inicia o monitoramento da tela.

        Args:
            window_handle: Handle da janela a ser monitorada (opcional)
        """
        try:
            if self.monitoring:
                if DEBUG:
                    print("Monitoramento já está ativo")
                return False

            print(f"Iniciando monitoramento com handle: {window_handle}")

            self.window_handle = window_handle
            if window_handle:
                self.screen_capture.window_handle = window_handle
                try:
                    title = self.screen_capture.get_window_title(window_handle)
                    self.screen_capture.window_title = title
                    print(f"Título da janela: {title}")
                except Exception as e:
                    print(f"Erro ao obter título da janela: {e}")

            # Captura a imagem inicial
            print("Tentando capturar imagem inicial...")
            initial_image = self.capture_current_screen()
            if initial_image is None:
                print("Falha ao capturar imagem inicial")
                return False

            print(f"Imagem inicial capturada: {initial_image.shape}")
            self.last_image = initial_image

            print("Calculando hash da imagem...")
            try:
                self.last_image_hash = self.compute_image_hash(initial_image)
                print("Hash calculado com sucesso")
            except Exception as e:
                print(f"Erro ao calcular hash: {e}")
                return False

            # Inicia o thread de monitoramento
            print("Iniciando thread de monitoramento...")
            self.monitoring = True
            self.monitor_thread = threading.Thread(target=self._monitor_loop)
            self.monitor_thread.daemon = True
            self.monitor_thread.start()

            print("Monitoramento iniciado com sucesso")
            return True

        except Exception as e:
            print(f"Erro ao iniciar monitoramento: {e}")
            import traceback
            traceback.print_exc()
            return False

    def stop_monitoring(self):
        """
        Para o monitoramento da tela.
        """
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=1.0)
            self.monitor_thread = None

        if DEBUG:
            print("Monitoramento parado")

    def capture_current_screen(self):
        """
        Captura a tela atual com tratamento de erros robusto.

        Returns:
            A imagem capturada ou None se falhar
        """
        try:
            if not self.window_handle:
                print("Nenhum handle de janela definido, selecionando janela...")
                if not self.screen_capture.select_window():
                    print("Falha ao selecionar janela")
                    return None
                self.window_handle = self.screen_capture.window_handle
                print(f"Janela selecionada: {self.screen_capture.window_title}")

            # Verifica se a janela ainda existe
            try:
                if not win32gui.IsWindow(self.window_handle):
                    print("A janela não existe mais, selecionando nova janela...")
                    if not self.screen_capture.select_window():
                        print("Falha ao selecionar nova janela")
                        return None
                    self.window_handle = self.screen_capture.window_handle
            except Exception as e:
                print(f"Erro ao verificar janela: {e}")
                # Tenta selecionar uma nova janela
                if not self.screen_capture.select_window():
                    print("Falha ao selecionar nova janela após erro")
                    return None
                self.window_handle = self.screen_capture.window_handle

            # Atualiza o handle da janela no screen_capture
            self.screen_capture.window_handle = self.window_handle

            # Tenta capturar a janela
            print(f"Tentando capturar janela com handle {self.window_handle}...")
            image = self.screen_capture.capture_window()

            if image is None:
                print("Falha na captura, tentando novamente com seleção de janela...")
                # Se falhar, tenta selecionar a janela novamente
                if not self.screen_capture.select_window():
                    print("Falha ao selecionar janela após falha na captura")
                    return None
                self.window_handle = self.screen_capture.window_handle
                image = self.screen_capture.capture_window()

            if image is not None:
                print(f"Captura bem-sucedida: {image.shape}")
            else:
                print("Todas as tentativas de captura falharam")

            return image

        except Exception as e:
            print(f"Erro ao capturar tela: {e}")
            import traceback
            traceback.print_exc()
            return None

    def compute_image_hash(self, image):
        """
        Calcula um hash simples da imagem para comparação rápida.

        Args:
            image: A imagem para calcular o hash

        Returns:
            Um hash da imagem
        """
        try:
            # Redimensiona para uma resolução menor para comparação mais rápida
            small = cv2.resize(image, (32, 32))
            # Converte para escala de cinza
            if len(small.shape) == 3:  # Se a imagem for colorida
                gray = cv2.cvtColor(small, cv2.COLOR_BGR2GRAY)
            else:  # Se a imagem já estiver em escala de cinza
                gray = small

            # Método alternativo de hash (mais simples e robusto)
            # Calcula a média dos pixels
            avg = gray.mean()
            # Cria um hash binário baseado em pixels acima ou abaixo da média
            hash_value = np.zeros((8, 8), dtype=np.uint8)
            for i in range(8):
                for j in range(8):
                    y = i * 4
                    x = j * 4
                    block = gray[y:y+4, x:x+4]
                    hash_value[i, j] = 1 if block.mean() >= avg else 0

            return hash_value

        except Exception as e:
            print(f"Erro no cálculo do hash: {e}")
            # Retorna um hash vazio em caso de erro
            return np.zeros((8, 8), dtype=np.uint8)

    def compute_image_difference(self, image1, image2):
        """
        Calcula a diferença entre duas imagens.

        Args:
            image1: Primeira imagem
            image2: Segunda imagem

        Returns:
            Porcentagem de diferença entre as imagens (0.0 a 1.0)
        """
        try:
            # Calcula os hashes das imagens
            hash1 = self.compute_image_hash(image1)
            hash2 = self.compute_image_hash(image2)

            # Calcula a distância de Hamming (número de bits diferentes)
            # Convertendo para cálculo manual já que não estamos mais usando cv2.img_hash
            distance = np.sum(hash1 != hash2)

            # Normaliza para 0.0-1.0
            max_distance = hash1.size  # Número total de bits no hash (8x8 = 64)
            difference = distance / max_distance

            print(f"Diferença de imagem: {difference:.4f} (distância: {distance}/{max_distance})")

            return difference

        except Exception as e:
            print(f"Erro ao calcular diferença entre imagens: {e}")
            # Retorna um valor alto para forçar uma nova captura em caso de erro
            return 1.0

    def detect_progress_bar(self, image):
        """
        Detecta mudanças na barra de progresso.

        Args:
            image: A imagem capturada

        Returns:
            Valor estimado do progresso (0.0 a 1.0) ou None se não detectado
        """
        try:
            # Converte para HSV para melhor detecção de cores
            hsv = cv2.cvtColor(image, cv2.COLOR_RGB2HSV)

            # Define ranges de cores típicas para barras de progresso (azul, verde, laranja)
            # Azul
            lower_blue = np.array([100, 50, 50])
            upper_blue = np.array([130, 255, 255])
            mask_blue = cv2.inRange(hsv, lower_blue, upper_blue)

            # Verde
            lower_green = np.array([40, 50, 50])
            upper_green = np.array([80, 255, 255])
            mask_green = cv2.inRange(hsv, lower_green, upper_green)

            # Laranja/Amarelo
            lower_orange = np.array([10, 50, 50])
            upper_orange = np.array([30, 255, 255])
            mask_orange = cv2.inRange(hsv, lower_orange, upper_orange)

            # Combina todas as máscaras
            progress_mask = cv2.bitwise_or(mask_blue, cv2.bitwise_or(mask_green, mask_orange))

            # Encontra contornos
            contours, _ = cv2.findContours(progress_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            if contours:
                # Encontra o maior contorno (provavelmente a barra de progresso)
                largest_contour = max(contours, key=cv2.contourArea)
                x, y, w, h = cv2.boundingRect(largest_contour)

                # Calcula o progresso baseado na largura da área preenchida
                if w > 50 and h > 5:  # Filtro para barras muito pequenas
                    # Analisa a região da barra para calcular o progresso
                    bar_region = progress_mask[y:y+h, x:x+w]
                    filled_pixels = np.sum(bar_region > 0)
                    total_pixels = w * h
                    progress = filled_pixels / total_pixels if total_pixels > 0 else 0

                    if DEBUG:
                        print(f"Barra de progresso detectada: {progress:.2f} ({w}x{h})")

                    return progress

            return None

        except Exception as e:
            if DEBUG:
                print(f"Erro ao detectar barra de progresso: {e}")
            return None

    def detect_timer_value(self, image):
        """
        Detecta o valor do cronômetro/timer na imagem usando configurações regionais.

        Args:
            image: A imagem capturada

        Returns:
            Valor do timer em segundos ou None se não detectado
        """
        try:
            import pytesseract
            from ..core.config import TESSERACT_PATH
            import re

            # Configura o caminho do Tesseract
            if TESSERACT_PATH:
                pytesseract.pytesseract.tesseract_cmd = TESSERACT_PATH

            # Converte para escala de cinza
            gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)

            # Usa configuração regional para o timer
            y1, y2, x1, x2 = MonitorConfig.get_region_coordinates(gray.shape, "timer")
            timer_region = gray[y1:y2, x1:x2]

            # Aplica threshold para melhorar o OCR
            _, thresh = cv2.threshold(timer_region, 127, 255, cv2.THRESH_BINARY)

            # Extrai o texto usando configuração específica
            ocr_config = MonitorConfig.OCR_CONFIG['timer']
            text = pytesseract.image_to_string(thresh, lang='eng', config=ocr_config)

            # Usa padrões de tempo da configuração
            for pattern in MonitorConfig.TIME_PATTERNS:
                matches = re.findall(pattern, text)
                if matches:
                    match = matches[0]
                    if len(match) == 3:  # HH:MM:SS
                        hours, minutes, seconds = map(int, match)
                        total_seconds = hours * 3600 + minutes * 60 + seconds
                    elif len(match) == 2:  # MM:SS
                        minutes, seconds = map(int, match)
                        total_seconds = minutes * 60 + seconds
                    else:  # Apenas segundos
                        total_seconds = int(match[0])

                    if DEBUG:
                        print(f"Timer detectado: {total_seconds} segundos (região: {y1}:{y2}, {x1}:{x2})")

                    return total_seconds

            return None

        except Exception as e:
            if DEBUG:
                print(f"Erro ao detectar timer: {e}")
            return None

    def extract_question_text_hash(self, image):
        """
        Extrai e calcula hash do texto da questão usando configurações regionais.

        Args:
            image: A imagem capturada

        Returns:
            Hash do texto da questão ou None se não detectado
        """
        try:
            import pytesseract
            from ..core.config import TESSERACT_PATH
            import hashlib

            # Configura o caminho do Tesseract
            if TESSERACT_PATH:
                pytesseract.pytesseract.tesseract_cmd = TESSERACT_PATH

            # Converte para escala de cinza
            gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)

            # Usa configuração regional para a questão
            y1, y2, x1, x2 = MonitorConfig.get_region_coordinates(gray.shape, "question")
            question_region = gray[y1:y2, x1:x2]

            # Aplica threshold para melhorar o OCR
            _, thresh = cv2.threshold(question_region, 127, 255, cv2.THRESH_BINARY)

            # Extrai o texto usando configuração específica
            ocr_config = MonitorConfig.OCR_CONFIG['question']
            text = pytesseract.image_to_string(thresh, lang='por', config=ocr_config)

            # Remove espaços extras e normaliza
            normalized_text = ' '.join(text.split())

            if len(normalized_text) > 20:  # Só considera se há texto suficiente
                # Calcula hash MD5 do texto
                text_hash = hashlib.md5(normalized_text.encode()).hexdigest()

                if DEBUG:
                    print(f"Hash do texto da questão: {text_hash[:8]}... (texto: {len(normalized_text)} chars, região: {y1}:{y2}, {x1}:{x2})")

                return text_hash

            return None

        except Exception as e:
            if DEBUG:
                print(f"Erro ao extrair hash do texto da questão: {e}")
            return None

    def extract_alternatives_hash(self, image):
        """
        Extrai e calcula hash das alternativas de resposta usando configurações regionais.

        Args:
            image: A imagem capturada

        Returns:
            Hash das alternativas ou None se não detectado
        """
        try:
            import pytesseract
            from ..core.config import TESSERACT_PATH
            import hashlib
            import re

            # Configura o caminho do Tesseract
            if TESSERACT_PATH:
                pytesseract.pytesseract.tesseract_cmd = TESSERACT_PATH

            # Converte para escala de cinza
            gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)

            # Usa configuração regional para as alternativas
            y1, y2, x1, x2 = MonitorConfig.get_region_coordinates(gray.shape, "alternatives")
            alternatives_region = gray[y1:y2, x1:x2]

            # Aplica threshold para melhorar o OCR
            _, thresh = cv2.threshold(alternatives_region, 127, 255, cv2.THRESH_BINARY)

            # Extrai o texto usando configuração específica
            ocr_config = MonitorConfig.OCR_CONFIG['alternatives']
            text = pytesseract.image_to_string(thresh, lang='por', config=ocr_config)

            # Usa padrão de alternativas da configuração
            alternatives = re.findall(MonitorConfig.MULTIPLE_CHOICE_PATTERN, text, re.MULTILINE)

            if len(alternatives) >= MonitorConfig.MIN_ALTERNATIVES:
                # Normaliza e junta as alternativas
                normalized_alternatives = ' | '.join([alt.strip() for alt in alternatives])

                # Calcula hash MD5 das alternativas
                alternatives_hash = hashlib.md5(normalized_alternatives.encode()).hexdigest()

                if DEBUG:
                    print(f"Hash das alternativas: {alternatives_hash[:8]}... ({len(alternatives)} alternativas, região: {y1}:{y2}, {x1}:{x2})")

                return alternatives_hash

            return None

        except Exception as e:
            if DEBUG:
                print(f"Erro ao extrair hash das alternativas: {e}")
            return None

    def detect_question_type(self, image):
        """
        Detecta automaticamente o tipo de questão baseado na análise visual da interface.

        Args:
            image: A imagem capturada

        Returns:
            Tipo de questão detectado ("multiple_choice", "matching", "drag_drop", etc.)
        """
        try:
            import pytesseract
            from ..core.config import TESSERACT_PATH
            import re

            # Configura o caminho do Tesseract
            if TESSERACT_PATH:
                pytesseract.pytesseract.tesseract_cmd = TESSERACT_PATH

            # Converte para escala de cinza
            gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)

            # Extrai texto de toda a imagem para análise
            text = pytesseract.image_to_string(gray, lang='por', config='--psm 6')
            text_lower = text.lower()

            # 1. Verifica questões de múltipla escolha (A, B, C, D, E)
            multiple_choice_matches = re.findall(MonitorConfig.MULTIPLE_CHOICE_PATTERN, text)
            if len(multiple_choice_matches) >= 3:
                if DEBUG:
                    print(f"Tipo detectado: multiple_choice ({len(multiple_choice_matches)} alternativas)")
                return "multiple_choice"

            # 2. Verifica questões de relacionar colunas
            matching_matches = re.findall(MonitorConfig.MATCHING_PATTERN, text)
            if len(matching_matches) >= MonitorConfig.MIN_MATCHING_ITEMS:
                if DEBUG:
                    print(f"Tipo detectado: matching ({len(matching_matches)} itens)")
                return "matching"

            # 3. Verifica questões de preenchimento de lacunas
            fill_blank_matches = re.findall(MonitorConfig.FILL_BLANK_PATTERN, text)
            if len(fill_blank_matches) >= 2:
                if DEBUG:
                    print(f"Tipo detectado: fill_blank ({len(fill_blank_matches)} lacunas)")
                return "fill_blank"

            # 4. Verifica indicadores de drag and drop
            for indicator in MonitorConfig.DRAG_DROP_INDICATORS:
                if indicator in text_lower:
                    if DEBUG:
                        print(f"Tipo detectado: drag_drop (indicador: {indicator})")
                    return "drag_drop"

            # 5. Verifica indicadores de ordenação
            for indicator in MonitorConfig.ORDERING_INDICATORS:
                if indicator in text_lower:
                    if DEBUG:
                        print(f"Tipo detectado: ordering (indicador: {indicator})")
                    return "ordering"

            # 6. Verifica questões verdadeiro/falso
            if any(word in text_lower for word in ['verdadeiro', 'falso', 'true', 'false', 'correto', 'incorreto']):
                tf_count = text_lower.count('verdadeiro') + text_lower.count('falso') + text_lower.count('true') + text_lower.count('false')
                if tf_count >= 2:
                    if DEBUG:
                        print(f"Tipo detectado: true_false")
                    return "true_false"

            # 7. Verifica questões dissertativas/subjetivas usando detector especializado
            essay_result = self.essay_detector.detect_essay_question(text, image)
            if essay_result['is_essay'] and essay_result['confidence'] >= 0.15:
                if DEBUG:
                    print(f"Tipo detectado: essay (confiança: {essay_result['confidence']:.2f})")
                    print(f"Indicadores: {essay_result['indicators']}")
                # Armazena resultado da detecção para uso posterior
                self.last_essay_detection = essay_result
                return "essay"

            # 8. Análise visual para elementos interativos
            question_type = self._analyze_visual_elements(image)
            if question_type != "auto_detect":
                return question_type

            # Padrão: múltipla escolha se não conseguir determinar
            if DEBUG:
                print("Tipo detectado: multiple_choice (padrão)")
            return "multiple_choice"

        except Exception as e:
            if DEBUG:
                print(f"Erro na detecção de tipo de questão: {e}")
            return "auto_detect"

    def _analyze_visual_elements(self, image):
        """
        Analisa elementos visuais para detectar tipo de questão.

        Args:
            image: A imagem capturada

        Returns:
            Tipo de questão baseado em análise visual
        """
        try:
            # Converte para escala de cinza
            gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)

            # Detecta linhas (possíveis conexões em matching)
            edges = cv2.Canny(gray, 50, 150, apertureSize=3)
            lines = cv2.HoughLines(edges, 1, np.pi/180, threshold=100)

            if lines is not None and len(lines) > 5:
                # Muitas linhas podem indicar questão de relacionar
                if DEBUG:
                    print(f"Tipo detectado: matching (análise visual - {len(lines)} linhas)")
                return "matching"

            # Detecta contornos (possíveis elementos arrastáveis)
            contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            # Filtra contornos por tamanho (elementos interativos)
            interactive_contours = []
            for contour in contours:
                area = cv2.contourArea(contour)
                if 500 < area < 5000:  # Tamanho típico de elementos interativos
                    interactive_contours.append(contour)

            if len(interactive_contours) > 8:
                # Muitos elementos podem indicar drag&drop
                if DEBUG:
                    print(f"Tipo detectado: drag_drop (análise visual - {len(interactive_contours)} elementos)")
                return "drag_drop"

            # Detecta campos de entrada (retângulos vazios)
            rectangles = []
            for contour in contours:
                approx = cv2.approxPolyDP(contour, 0.02 * cv2.arcLength(contour, True), True)
                if len(approx) == 4:  # Retângulo
                    area = cv2.contourArea(contour)
                    if 1000 < area < 10000:  # Tamanho típico de campo de entrada
                        rectangles.append(contour)

            if len(rectangles) > 3:
                # Muitos retângulos podem indicar campos de entrada
                if DEBUG:
                    print(f"Tipo detectado: fill_blank (análise visual - {len(rectangles)} campos)")
                return "fill_blank"

            return "auto_detect"

        except Exception as e:
            if DEBUG:
                print(f"Erro na análise visual: {e}")
            return "auto_detect"

    def extract_interactive_elements_hash(self, image, question_type):
        """
        Extrai hash de elementos interativos baseado no tipo de questão.

        Args:
            image: A imagem capturada
            question_type: Tipo de questão detectado

        Returns:
            Hash dos elementos interativos ou None se não detectado
        """
        try:
            if question_type == "matching":
                return QuestionTypeDetectors.extract_matching_elements_hash(image)
            elif question_type == "drag_drop":
                return QuestionTypeDetectors.extract_drag_drop_elements_hash(image)
            elif question_type == "ordering":
                return QuestionTypeDetectors.extract_ordering_elements_hash(image)
            elif question_type == "fill_blank":
                return QuestionTypeDetectors.extract_input_fields_hash(image)
            elif question_type == "essay":
                return QuestionTypeDetectors.extract_text_area_hash(image)
            else:
                # Para outros tipos, usa análise visual geral
                return QuestionTypeDetectors.extract_general_interactive_hash(image)

        except Exception as e:
            if DEBUG:
                print(f"Erro ao extrair hash de elementos interativos: {e}")
            return None

    def _monitor_loop(self):
        """
        Loop principal de monitoramento com detecção avançada de mudanças.
        """
        print("Iniciando loop de monitoramento avançado...")

        # Contador para verificações periódicas
        check_count = 0

        while self.monitoring:
            try:
                # Aguarda o intervalo de verificação
                time.sleep(self.check_interval)

                # Verifica se o monitoramento está pausado
                if self.paused:
                    print("Monitoramento em pausa, aguardando retomada...")
                    time.sleep(1)  # Espera um pouco antes de verificar novamente
                    continue

                # Incrementa o contador
                check_count += 1

                # Log periódico para confirmar que o monitoramento está ativo
                if check_count % 3 == 0:  # A cada 3 verificações
                    print(f"Monitoramento ativo - verificação #{check_count}")

                # Captura a tela atual
                print("Capturando tela atual...")
                current_image = self.capture_current_screen()
                if current_image is None:
                    print("Falha ao capturar tela atual durante monitoramento")
                    continue

                print(f"Tela capturada: {current_image.shape}")

                # Detecta tipo de questão automaticamente
                current_question_type = self.detect_question_type(current_image)
                if current_question_type != self.current_question_type:
                    print(f"🔄 Tipo de questão alterado: {self.current_question_type} -> {current_question_type}")
                    self.current_question_type = current_question_type

                # Detecta elementos específicos da interface
                current_progress = self.detect_progress_bar(current_image)
                current_timer = self.detect_timer_value(current_image)
                current_question_hash = self.extract_question_text_hash(current_image)
                current_alternatives_hash = self.extract_alternatives_hash(current_image)
                current_interactive_hash = self.extract_interactive_elements_hash(current_image, current_question_type)

                # Verifica mudanças específicas
                changes_detected = []

                # 1. Mudança na barra de progresso
                if current_progress is not None and self.last_progress_bar is not None:
                    progress_diff = abs(current_progress - self.last_progress_bar)
                    if progress_diff > self.progress_threshold:
                        changes_detected.append(f"Barra de progresso: {self.last_progress_bar:.2f} -> {current_progress:.2f}")

                # 2. Mudança no timer
                if current_timer is not None and self.last_timer_value is not None:
                    timer_diff = abs(current_timer - self.last_timer_value)
                    if timer_diff > self.timer_threshold:
                        changes_detected.append(f"Timer: {self.last_timer_value}s -> {current_timer}s")

                # 3. Mudança no texto da questão
                if current_question_hash is not None and self.last_question_text_hash is not None:
                    if current_question_hash != self.last_question_text_hash:
                        changes_detected.append("Texto da questão alterado")

                # 4. Mudança nas alternativas
                if current_alternatives_hash is not None and self.last_alternatives_hash is not None:
                    if current_alternatives_hash != self.last_alternatives_hash:
                        changes_detected.append("Alternativas alteradas")

                # 5. Mudança em elementos interativos específicos do tipo de questão
                if current_interactive_hash is not None and self.last_interactive_elements_hash is not None:
                    if current_interactive_hash != self.last_interactive_elements_hash:
                        changes_detected.append(f"Elementos interativos alterados ({current_question_type})")

                # 6. Mudança visual geral (fallback)
                visual_difference = self.compute_image_difference(self.last_image, current_image)
                if self.detect_any_change and visual_difference > self.change_threshold:
                    changes_detected.append(f"Mudança visual geral: {visual_difference:.4f}")

                # Determina se deve acionar mudança
                should_trigger_change = len(changes_detected) > 0

                if should_trigger_change:
                    print(f"🔄 MUDANÇAS DETECTADAS:")
                    for change in changes_detected:
                        print(f"  • {change}")

                    # Salva as imagens para depuração
                    try:
                        if self.last_image is not None:
                            cv2.imwrite("last_image.png", cv2.cvtColor(self.last_image, cv2.COLOR_RGB2BGR))
                        cv2.imwrite("current_image.png", cv2.cvtColor(current_image, cv2.COLOR_RGB2BGR))
                    except Exception as e:
                        print(f"Erro ao salvar imagens de debug: {e}")

                    # Atualiza os estados anteriores
                    self.last_image = current_image.copy()
                    self.last_image_hash = self.compute_image_hash(current_image)
                    self.last_progress_bar = current_progress
                    self.last_timer_value = current_timer
                    self.last_question_text_hash = current_question_hash
                    self.last_alternatives_hash = current_alternatives_hash
                    self.last_interactive_elements_hash = current_interactive_hash
                    self.last_question_type = current_question_type

                    # Chama o callback se existir
                    if self.callback_on_change:
                        print("Chamando callback de mudança...")
                        # Pausa o monitoramento enquanto processa a mudança
                        self.pause_monitoring()
                        self.callback_on_change(current_image)
                    else:
                        print("Nenhum callback definido para mudanças")
                else:
                    # Atualiza estados mesmo sem mudanças significativas
                    if current_progress is not None:
                        self.last_progress_bar = current_progress
                    if current_timer is not None:
                        self.last_timer_value = current_timer
                    if current_question_hash is not None:
                        self.last_question_text_hash = current_question_hash
                    if current_alternatives_hash is not None:
                        self.last_alternatives_hash = current_alternatives_hash
                    if current_interactive_hash is not None:
                        self.last_interactive_elements_hash = current_interactive_hash
                    if current_question_type != self.last_question_type:
                        self.last_question_type = current_question_type

                    print(f"✅ Nenhuma mudança significativa detectada (visual: {visual_difference:.4f}, tipo: {current_question_type})")

            except Exception as e:
                print(f"Erro no loop de monitoramento: {e}")
                import traceback
                traceback.print_exc()

        print("Loop de monitoramento encerrado")

    def pause_monitoring(self):
        """
        Pausa temporariamente o monitoramento sem interromper o thread.
        """
        self.paused = True
        print("Monitoramento pausado temporariamente")

    def resume_monitoring(self):
        """
        Retoma o monitoramento após uma pausa.
        """
        self.paused = False
        print("Monitoramento retomado")

    def is_monitoring(self):
        """
        Verifica se o monitoramento está ativo.

        Returns:
            True se o monitoramento estiver ativo, False caso contrário
        """
        return self.monitoring

    def is_paused(self):
        """
        Verifica se o monitoramento está pausado.

        Returns:
            True se o monitoramento estiver pausado, False caso contrário
        """
        return self.paused

    def set_detect_any_change(self, detect_any_change):
        """
        Define se o monitor deve detectar qualquer alteração visual ou apenas mudanças específicas.

        Args:
            detect_any_change: True para detectar qualquer alteração, False para detectar apenas mudanças específicas
        """
        self.detect_any_change = detect_any_change
        print(f"Modo de detecção alterado: {'Qualquer alteração visual' if detect_any_change else 'Apenas mudanças específicas'}")

    def is_detect_any_change_enabled(self):
        """
        Verifica se a detecção de qualquer alteração visual está ativada.

        Returns:
            True se a detecção de qualquer alteração estiver ativada, False caso contrário
        """
        return self.detect_any_change

    def configure_sensitivity(self, progress_threshold=None, timer_threshold=None,
                            text_threshold=None, alternatives_threshold=None,
                            visual_threshold=None):
        """
        Configura a sensibilidade dos diferentes tipos de detecção.

        Args:
            progress_threshold: Threshold para mudanças na barra de progresso (0.0-1.0)
            timer_threshold: Threshold para mudanças no timer (segundos)
            text_threshold: Threshold para mudanças no texto da questão (0.0-1.0)
            alternatives_threshold: Threshold para mudanças nas alternativas (0.0-1.0)
            visual_threshold: Threshold para mudanças visuais gerais (0.0-1.0)
        """
        if progress_threshold is not None:
            self.progress_threshold = progress_threshold
            print(f"Threshold da barra de progresso: {progress_threshold}")

        if timer_threshold is not None:
            self.timer_threshold = timer_threshold
            print(f"Threshold do timer: {timer_threshold}s")

        if text_threshold is not None:
            self.text_threshold = text_threshold
            print(f"Threshold do texto: {text_threshold}")

        if alternatives_threshold is not None:
            self.alternatives_threshold = alternatives_threshold
            print(f"Threshold das alternativas: {alternatives_threshold}")

        if visual_threshold is not None:
            self.change_threshold = visual_threshold
            print(f"Threshold visual geral: {visual_threshold}")

    def get_sensitivity_config(self):
        """
        Retorna a configuração atual de sensibilidade.

        Returns:
            Dict com as configurações de threshold
        """
        return {
            'progress_threshold': self.progress_threshold,
            'timer_threshold': self.timer_threshold,
            'text_threshold': self.text_threshold,
            'alternatives_threshold': self.alternatives_threshold,
            'visual_threshold': self.change_threshold,
            'check_interval': self.check_interval,
            'config_scenario': self.config_scenario
        }

    def change_config_scenario(self, new_scenario):
        """
        Altera o cenário de configuração dinamicamente.

        Args:
            new_scenario: Novo cenário ("general", "sensitive", "fast", "conservative")
        """
        if new_scenario == self.config_scenario:
            print(f"Cenário '{new_scenario}' já está ativo")
            return

        # Carrega nova configuração
        config = MonitorConfig.get_optimized_config(new_scenario)

        # Atualiza configurações
        old_scenario = self.config_scenario
        self.config_scenario = new_scenario
        self.change_threshold = config["visual_threshold"]
        self.check_interval = config["check_interval"]
        self.progress_threshold = config["progress_threshold"]
        self.timer_threshold = config["timer_threshold"]
        self.text_threshold = config["text_threshold"]
        self.alternatives_threshold = config["alternatives_threshold"]

        print(f"🔄 Configuração alterada de '{old_scenario}' para '{new_scenario}':")
        print(f"  • Intervalo de verificação: {self.check_interval}s")
        print(f"  • Threshold visual: {self.change_threshold}")
        print(f"  • Threshold progresso: {self.progress_threshold}")
        print(f"  • Threshold timer: {self.timer_threshold}s")
        print(f"  • Threshold texto: {self.text_threshold}")
        print(f"  • Threshold alternativas: {self.alternatives_threshold}")

    def get_available_scenarios(self):
        """
        Retorna os cenários de configuração disponíveis.

        Returns:
            Lista com os cenários disponíveis
        """
        return ["general", "sensitive", "fast", "conservative"]

# Para testes
if __name__ == "__main__":
    def on_screen_change(image):
        print("Mudança de tela detectada!")
        cv2.imwrite("changed_screen.png", cv2.cvtColor(image, cv2.COLOR_RGB2BGR))

    monitor = ScreenMonitor(callback_on_change=on_screen_change)
    monitor.start_monitoring()

    try:
        print("Monitorando mudanças na tela. Pressione Ctrl+C para sair.")
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("Parando monitoramento...")
    finally:
        monitor.stop_monitoring()
