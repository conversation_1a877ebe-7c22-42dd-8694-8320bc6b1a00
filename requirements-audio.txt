# Dependências opcionais para o módulo de áudio do Python Question Helper
# Instale com: pip install -r requirements-audio.txt

# === CAPTURA DE ÁUDIO ===
# Biblioteca principal para captura de áudio
sounddevice>=0.4.6

# Alternativa para captura de áudio
pyaudio>=0.2.11

# === PROCESSAMENTO DE ÁUDIO ===
# Biblioteca para análise e processamento de áudio
librosa>=0.10.0

# Redução de ruído
noisereduce>=3.0.0

# Salvamento de arquivos de áudio
soundfile>=0.12.1

# Processamento científico (fallback para salvamento)
scipy>=1.10.0

# === SPEECH-TO-TEXT ===
# OpenAI Whisper (recomendado - funciona offline)
openai-whisper>=20231117

# Google Cloud Speech-to-Text
google-cloud-speech>=2.21.0

# Azure Cognitive Services Speech
azure-cognitiveservices-speech>=1.34.0

# AWS SDK para Transcribe
boto3>=1.34.0

# === VOICE ACTIVITY DETECTION ===
# WebRTC VAD para detecção de atividade de voz
webrtcvad>=2.0.10

# === PROCESSAMENTO ADICIONAL ===
# FFmpeg Python wrapper (para conversão de formatos)
ffmpeg-python>=0.2.0

# Pydub (alternativa para processamento de áudio)
pydub>=0.25.1

# === DEPENDÊNCIAS OPCIONAIS ===
# Para melhor performance com NumPy
numba>=0.58.0

# Para processamento de sinais
scikit-learn>=1.3.0

# === INSTALAÇÃO RECOMENDADA ===
# Para instalar apenas as dependências essenciais:
# pip install sounddevice librosa openai-whisper

# Para instalação completa com todos os provedores:
# pip install -r requirements-audio.txt

# === NOTAS DE INSTALAÇÃO ===
# 
# 1. WHISPER (Recomendado):
#    - Funciona offline
#    - Boa precisão
#    - pip install openai-whisper
#
# 2. GOOGLE SPEECH:
#    - Requer conta Google Cloud
#    - Configurar GOOGLE_APPLICATION_CREDENTIALS
#    - pip install google-cloud-speech
#
# 3. AZURE SPEECH:
#    - Requer conta Azure
#    - Configurar AZURE_SPEECH_KEY e AZURE_SPEECH_REGION
#    - pip install azure-cognitiveservices-speech
#
# 4. AWS TRANSCRIBE:
#    - Requer conta AWS
#    - Configurar AWS_ACCESS_KEY e AWS_SECRET_KEY
#    - pip install boto3
#
# 5. SOUNDDEVICE:
#    - Pode requerer instalação de PortAudio
#    - Windows: geralmente funciona direto
#    - Linux: sudo apt-get install portaudio19-dev
#    - macOS: brew install portaudio
#
# 6. FFMPEG (Opcional):
#    - Para suporte a mais formatos de áudio
#    - Windows: baixar de https://ffmpeg.org/
#    - Linux: sudo apt-get install ffmpeg
#    - macOS: brew install ffmpeg
