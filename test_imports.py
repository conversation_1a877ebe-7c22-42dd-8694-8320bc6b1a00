#!/usr/bin/env python3
"""
Script de teste para verificar se todos os imports estão funcionando.
"""

import sys
import os

# Adiciona o diretório atual ao path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Testa todos os imports principais."""
    
    print("🔍 TESTANDO IMPORTS...")
    print("="*50)
    
    # Teste 1: Config
    try:
        from config import DEBUG, TESSERACT_PATH
        print("✅ config - OK")
    except Exception as e:
        print(f"❌ config - ERRO: {e}")
        return False
    
    # Teste 2: Question Logger
    try:
        from question_logger import QuestionLogger
        print("✅ question_logger - OK")
    except Exception as e:
        print(f"❌ question_logger - ERRO: {e}")
        return False
    
    # Teste 3: Analytics Module
    try:
        from analytics_module import AnalyticsModule
        print("✅ analytics_module - OK")
    except Exception as e:
        print(f"❌ analytics_module - ERRO: {e}")
        return False
    
    # Teste 4: Review Interface
    try:
        from review_interface import ReviewInterface
        print("✅ review_interface - OK")
    except Exception as e:
        print(f"❌ review_interface - ERRO: {e}")
        return False
    
    # Teste 5: Main entry point
    try:
        from main import main
        print("✅ main - OK")
    except Exception as e:
        print(f"❌ main - ERRO: {e}")
        return False
    
    print("\n🎉 TODOS OS IMPORTS FUNCIONANDO!")
    return True

def test_src_imports():
    """Testa imports diretos da estrutura src/."""
    
    print("\n🔍 TESTANDO IMPORTS DIRETOS DA ESTRUTURA SRC...")
    print("="*50)
    
    # Teste direto dos módulos src
    modules_to_test = [
        ('src.core.config', 'Config'),
        ('src.logging.question_logger', 'QuestionLogger'),
        ('src.analytics.analytics_module', 'AnalyticsModule'),
        ('src.ui.review_interface', 'ReviewInterface'),
        ('src.core.main', 'QuestionHelper'),
        ('src.processing.ocr_processor', 'OCRProcessor'),
        ('src.llm.llm_manager', 'LLMManager'),
    ]
    
    success_count = 0
    
    for module_name, class_name in modules_to_test:
        try:
            module = __import__(module_name, fromlist=[class_name])
            if hasattr(module, class_name):
                print(f"✅ {module_name}.{class_name} - OK")
                success_count += 1
            else:
                print(f"⚠️ {module_name} - Módulo OK, mas {class_name} não encontrado")
        except Exception as e:
            print(f"❌ {module_name} - ERRO: {e}")
    
    print(f"\n📊 RESULTADO: {success_count}/{len(modules_to_test)} módulos funcionando")
    return success_count == len(modules_to_test)

if __name__ == "__main__":
    print("🧪 TESTE DE IMPORTS DO PYTHON QUESTION HELPER")
    print("="*60)
    
    # Testa imports de compatibilidade
    compat_ok = test_imports()
    
    # Testa imports diretos
    direct_ok = test_src_imports()
    
    print("\n" + "="*60)
    if compat_ok and direct_ok:
        print("🎉 TODOS OS TESTES PASSARAM!")
        print("✅ Sistema pronto para uso")
        sys.exit(0)
    else:
        print("❌ ALGUNS TESTES FALHARAM")
        print("🔧 Verifique os erros acima e corrija os imports")
        sys.exit(1)
