"""
Módulo para seleção de modelos LLM (versão simplificada).
"""
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
                           QComboBox, QGroupBox, QCheckBox, QRadioButton, QButtonGroup)
from PyQt5.QtCore import QSettings, Qt
from llm_models import get_vision_models
from llm_manager import <PERSON>MManager
from config import DEBUG

class ModelSelectorDialog(QDialog):
    def __init__(self, parent=None, current_model_id=None, detect_any_change=False):
        super().__init__(parent)
        self.setWindowTitle("Selecionar Modelo LLM")
        self.resize(400, 350)  # Tamanho reduzido
        self.current_model_id = current_model_id
        self.selected_model_id = None
        self.detect_any_change = detect_any_change

        # Inicializa as configurações
        self.settings = QSettings("AugmentC<PERSON>", "PythonQuestionHelper")

        # Cria a interface
        self.create_ui()

    def create_ui(self):
        """Cria a interface do diálogo."""
        # Cria o layout principal
        main_layout = QVBoxLayout()

        # Título
        title = QLabel("Selecione o Modelo LLM")
        title.setStyleSheet("font-weight: bold; font-size: 14px;")
        main_layout.addWidget(title)

        # Descrição
        desc = QLabel("Escolha um modelo de LLM para processar as questões:")
        desc.setWordWrap(True)
        main_layout.addWidget(desc)

        # Grupo de modelos
        models_group = QGroupBox("Modelos Disponíveis")
        models_layout = QVBoxLayout()

        # ComboBox para seleção de modelos
        self.models_combo = QComboBox()

        # Obtém apenas os modelos de visão
        vision_models = get_vision_models()

        # Filtra apenas os modelos gratuitos (com :free no ID)
        free_vision_models = [model for model in vision_models if ":free" in model['id']]

        # Ordena os modelos por nome em ordem alfabética
        free_vision_models.sort(key=lambda x: x['name'])

        # Adiciona os modelos ao combo box
        self.models_combo.addItem("--- Selecione um Modelo (Ordenados Alfabeticamente) ---", None)
        for model in free_vision_models:
            # Adiciona o modelo com indicação de que é gratuito
            self.models_combo.addItem(f"{model['name']} (Gratuito)", model['id'])

            # Define o tooltip com informações do modelo
            index = self.models_combo.count() - 1
            tooltip = f"ID: {model['id']}\n"
            tooltip += f"Descrição: {model['description']}\n"
            tooltip += f"Tamanho do contexto: {model['context_length']} tokens"
            self.models_combo.setItemData(index, tooltip, Qt.ToolTipRole)

            # Seleciona o modelo atual
            if model['id'] == self.current_model_id:
                self.models_combo.setCurrentIndex(index)

        # Adiciona um rótulo para mostrar informações do modelo selecionado
        self.model_info_label = QLabel("Selecione um modelo para ver suas informações")
        self.model_info_label.setWordWrap(True)
        self.model_info_label.setStyleSheet("font-size: 10pt; color: #333333; background-color: #f0f0f0; padding: 5px; border-radius: 3px;")

        # Conecta o sinal de mudança de seleção
        self.models_combo.currentIndexChanged.connect(self.update_model_info)

        models_layout.addWidget(self.models_combo)
        models_layout.addWidget(self.model_info_label)
        models_group.setLayout(models_layout)
        main_layout.addWidget(models_group)

        # Opções de monitoramento
        self.monitor_group = QGroupBox("Opções de Monitoramento")
        monitor_layout = QVBoxLayout()

        # Checkbox para detectar qualquer alteração visual
        self.detect_any_change_check = QCheckBox("Detectar qualquer alteração visual (não apenas mudança de questão)")
        self.detect_any_change_check.setChecked(self.detect_any_change)
        self.detect_any_change_check.setToolTip("Quando ativado, o sistema detectará qualquer alteração visual na tela, não apenas mudanças de questão")

        # Explicação sobre a detecção de alterações
        detect_label = QLabel("Por padrão, o sistema detecta apenas mudanças de questão (ex: Questão 7 → Questão 8). Ative esta opção se quiser detectar qualquer alteração visual.")
        detect_label.setWordWrap(True)

        monitor_layout.addWidget(self.detect_any_change_check)
        monitor_layout.addWidget(detect_label)

        self.monitor_group.setLayout(monitor_layout)
        main_layout.addWidget(self.monitor_group)

        # Botões de ação
        button_layout = QHBoxLayout()

        # Botão OK
        self.ok_button = QPushButton("OK")
        self.ok_button.clicked.connect(self.accept)

        # Botão Cancelar
        self.cancel_button = QPushButton("Cancelar")
        self.cancel_button.clicked.connect(self.reject)

        button_layout.addWidget(self.cancel_button)
        button_layout.addWidget(self.ok_button)

        main_layout.addLayout(button_layout)

        self.setLayout(main_layout)

    def get_selected_model_id(self):
        """Retorna o ID do modelo selecionado."""
        index = self.models_combo.currentIndex()
        return self.models_combo.itemData(index)

    def is_detect_any_change_enabled(self):
        """Retorna se a detecção de qualquer alteração visual está ativada."""
        return self.detect_any_change_check.isChecked()

    def update_model_info(self, index):
        """Atualiza as informações do modelo selecionado."""
        # Obtém o ID do modelo selecionado
        model_id = self.models_combo.itemData(index)

        if not model_id:
            # Se for um item de cabeçalho, limpa as informações
            self.model_info_label.setText("Selecione um modelo para ver suas informações")
            return

        # Obtém o tooltip do item selecionado
        tooltip = self.models_combo.itemData(index, Qt.ToolTipRole)

        if tooltip:
            # Formata o texto para exibição
            info_text = tooltip.replace("\n", "<br>")
            self.model_info_label.setText(f"<b>Informações do modelo:</b><br>{info_text}")
        else:
            self.model_info_label.setText("Informações não disponíveis para este modelo")
