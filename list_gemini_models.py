"""
Script para listar os modelos disponíveis no Google Gemini.
"""
import os
import google.generativeai as genai
from dotenv import load_dotenv

# <PERSON>eg<PERSON> as variáveis de ambiente
load_dotenv()

# Configura a API key
api_key = os.getenv("GEMINI_API_KEY")
genai.configure(api_key=api_key)

# Lista os modelos disponíveis
print("Modelos disponíveis no Google Gemini:")
for model in genai.list_models():
    if "generateContent" in model.supported_generation_methods:
        print(f"- {model.name}")
        print(f"  Descrição: {model.description}")
        print(f"  Métodos suportados: {model.supported_generation_methods}")
        print(f"  Versão: {model.version}")
        print()
