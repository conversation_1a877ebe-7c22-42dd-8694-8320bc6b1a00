"""
Módulo para gerenciar os modelos LLM disponíveis no OpenRouter.
"""
import requests
from config import LLM_API_KEY, DEBUG

# Lista de modelos gratuitos do OpenRouter
# Esta lista pode ser atualizada automaticamente ou manualmente
FREE_MODELS = [
    # Modelos que suportam visão (imagens) - IDs exatos conforme fornecidos
    {
        "id": "opengvlab/internvl3-14b:free",
        "name": "OpenGVLab: InternVL3 14B",
        "description": "Modelo da OpenGVLab com boa capacidade de visão",
        "supports_vision": True,
        "context_length": 32000,
        "category": "vision"
    },
    {
        "id": "opengvlab/internvl3-2b:free",
        "name": "OpenGVLab: InternVL3 2B",
        "description": "Versão menor e mais rápida do InternVL3",
        "supports_vision": True,
        "context_length": 32000,
        "category": "vision"
    },
    {
        "id": "moonshotai/kimi-vl-a3b-thinking:free",
        "name": "Moonshot AI: Kimi VL A3B Thinking",
        "description": "Modelo da Moonshot AI com capacidade de raciocínio visual",
        "supports_vision": True,
        "context_length": 131072,
        "category": "vision"
    },
    {
        "id": "meta-llama/llama-4-maverick:free",
        "name": "Meta: Llama 4 Maverick",
        "description": "Modelo avançado da Meta com suporte a visão",
        "supports_vision": True,
        "context_length": 256000,
        "category": "vision"
    },
    {
        "id": "meta-llama/llama-4-scout:free",
        "name": "Meta: Llama 4 Scout",
        "description": "Modelo da Meta com grande contexto e suporte a visão",
        "supports_vision": True,
        "context_length": 512000,
        "category": "vision"
    },
    {
        "id": "allenai/molmo-7b-d:free",
        "name": "AllenAI: Molmo 7B D",
        "description": "Modelo da Allen AI com suporte a visão",
        "supports_vision": True,
        "context_length": 4096,
        "category": "vision"
    },
    {
        "id": "bytedance-research/ui-tars-72b:free",
        "name": "Bytedance: UI-TARS 72B",
        "description": "Modelo da Bytedance com suporte a visão",
        "supports_vision": True,
        "context_length": 32768,
        "category": "vision"
    },
    {
        "id": "qwen/qwen2.5-vl-3b-instruct:free",
        "name": "Qwen: Qwen2.5 VL 3B Instruct",
        "description": "Modelo pequeno da Qwen com suporte a visão",
        "supports_vision": True,
        "context_length": 64000,
        "category": "vision"
    },
    {
        "id": "google/gemini-2.5-pro-exp-03-25",
        "name": "Google: Gemini 2.5 Pro Experimental",
        "description": "Modelo experimental do Google com suporte a visão",
        "supports_vision": True,
        "context_length": 1000000,
        "category": "vision"
    },
    {
        "id": "qwen/qwen2.5-vl-32b-instruct:free",
        "name": "Qwen: Qwen2.5 VL 32B Instruct",
        "description": "Modelo médio da Qwen com suporte a visão",
        "supports_vision": True,
        "context_length": 8192,
        "category": "vision"
    },
    {
        "id": "mistralai/mistral-small-3.1-24b-instruct:free",
        "name": "Mistral: Mistral Small 3.1 24B",
        "description": "Modelo da Mistral AI com suporte a visão",
        "supports_vision": True,
        "context_length": 96000,
        "category": "vision"
    },
    {
        "id": "google/gemma-3-1b-it:free",
        "name": "Google: Gemma 3 1B",
        "description": "Modelo pequeno do Google com suporte a visão",
        "supports_vision": True,
        "context_length": 32768,
        "category": "vision"
    },
    {
        "id": "google/gemma-3-4b-it:free",
        "name": "Google: Gemma 3 4B",
        "description": "Modelo do Google com suporte a visão",
        "supports_vision": True,
        "context_length": 131072,
        "category": "vision"
    },
    {
        "id": "google/gemma-3-12b-it:free",
        "name": "Google: Gemma 3 12B",
        "description": "Modelo médio do Google com suporte a visão",
        "supports_vision": True,
        "context_length": 131072,
        "category": "vision"
    },
    {
        "id": "google/gemma-3-27b-it:free",
        "name": "Google: Gemma 3 27B",
        "description": "Modelo grande do Google com suporte a visão",
        "supports_vision": True,
        "context_length": 96000,
        "category": "vision"
    },
    {
        "id": "qwen/qwen2.5-vl-72b-instruct:free",
        "name": "Qwen: Qwen2.5 VL 72B Instruct",
        "description": "Modelo grande da Qwen com suporte a visão",
        "supports_vision": True,
        "context_length": 131072,
        "category": "vision"
    },
    {
        "id": "google/gemini-2.0-flash-exp:free",
        "name": "Google: Gemini 2.0 Flash Experimental",
        "description": "Modelo experimental rápido do Google com suporte a visão",
        "supports_vision": True,
        "context_length": 1048576,
        "category": "vision"
    },
    {
        "id": "google/learnlm-1.5-pro-experimental:free",
        "name": "Google: LearnLM 1.5 Pro Experimental",
        "description": "Modelo experimental do Google com suporte a visão",
        "supports_vision": True,
        "context_length": 40960,
        "category": "vision"
    },
    {
        "id": "meta-llama/llama-3.2-11b-vision-instruct:free",
        "name": "Meta: Llama 3.2 11B Vision Instruct",
        "description": "Modelo da Meta com suporte a visão",
        "supports_vision": True,
        "context_length": 131072,
        "category": "vision"
    },
    {
        "id": "qwen/qwen-2.5-vl-7b-instruct:free",
        "name": "Qwen: Qwen2.5-VL 7B Instruct",
        "description": "Modelo da Qwen com suporte a visão",
        "supports_vision": True,
        "context_length": 64000,
        "category": "vision"
    },
    {
        "id": "google/gemini-flash-1.5-8b-exp",
        "name": "Google: Gemini 1.5 Flash 8B Experimental",
        "description": "Modelo experimental rápido do Google com suporte a visão",
        "supports_vision": True,
        "context_length": 128000,
        "category": "vision"
    },

    # Modelos apenas de texto (sem suporte a imagens)
    {
        "id": "google/gemini-pro:free",
        "name": "Gemini Pro",
        "description": "Modelo do Google, bom para tarefas gerais",
        "supports_vision": False,
        "context_length": 32000,
        "category": "text"
    },
    {
        "id": "meta-llama/llama-3-70b-instruct:free",
        "name": "Llama 3 70B Instruct",
        "description": "Modelo grande da Meta, bom para instruções complexas",
        "supports_vision": False,
        "context_length": 8000,
        "category": "text"
    },
    {
        "id": "meta-llama/llama-3-8b-instruct:free",
        "name": "Llama 3 8B Instruct",
        "description": "Modelo menor da Meta, mais rápido",
        "supports_vision": False,
        "context_length": 8000,
        "category": "text"
    },
    {
        "id": "mistralai/mistral-large-latest:free",
        "name": "Mistral Large",
        "description": "Modelo grande da Mistral AI, bom para tarefas complexas",
        "supports_vision": False,
        "context_length": 32000,
        "category": "text"
    },
    {
        "id": "mistralai/mistral-medium-latest:free",
        "name": "Mistral Medium",
        "description": "Modelo médio da Mistral AI, bom equilíbrio",
        "supports_vision": False,
        "context_length": 32000,
        "category": "text"
    },
    {
        "id": "mistralai/mistral-small-latest:free",
        "name": "Mistral Small",
        "description": "Modelo pequeno da Mistral AI, mais rápido",
        "supports_vision": False,
        "context_length": 32000,
        "category": "text"
    },
    {
        "id": "openai/gpt-3.5-turbo:free",
        "name": "GPT-3.5 Turbo",
        "description": "Modelo mais rápido da OpenAI, bom para tarefas simples",
        "supports_vision": False,
        "context_length": 16000,
        "category": "text"
    },
    {
        "id": "deepseek/deepseek-chat-v3-0324:free",
        "name": "DeepSeek Chat v3",
        "description": "Modelo de chat da DeepSeek, bom para conversas",
        "supports_vision": False,
        "context_length": 32000,
        "category": "text"
    }
]

def get_available_models(category=None):
    """
    Retorna a lista de modelos disponíveis, opcionalmente filtrados por categoria.

    Args:
        category: Categoria para filtrar ('vision', 'text', ou None para todos)

    Returns:
        Lista de modelos disponíveis
    """
    if category:
        return [model for model in FREE_MODELS if model.get('category') == category]
    return FREE_MODELS

def get_vision_models():
    """
    Retorna apenas os modelos que suportam visão.

    Returns:
        Lista de modelos que suportam visão
    """
    return get_available_models(category='vision')

def get_text_models():
    """
    Retorna apenas os modelos de texto (sem suporte a visão).

    Returns:
        Lista de modelos de texto
    """
    return get_available_models(category='text')

def fetch_models_from_openrouter():
    """
    Busca a lista de modelos disponíveis no OpenRouter.

    Returns:
        Lista de modelos disponíveis ou None se falhar
    """
    try:
        if not LLM_API_KEY:
            if DEBUG:
                print("API key não configurada. Usando lista local de modelos.")
            return None

        url = "https://openrouter.ai/api/v1/models"
        headers = {
            "Authorization": f"Bearer {LLM_API_KEY}"
        }

        response = requests.get(url, headers=headers)

        if response.status_code == 200:
            data = response.json()

            # Filtra apenas os modelos gratuitos
            free_models = []
            for model in data.get("data", []):
                if model.get("id", "").endswith(":free"):
                    free_models.append({
                        "id": model.get("id"),
                        "name": model.get("name"),
                        "description": model.get("description", ""),
                        "supports_vision": "vision" in model.get("capabilities", []),
                        "context_length": model.get("context_length", 4000)
                    })

            return free_models
        else:
            if DEBUG:
                print(f"Erro ao buscar modelos: {response.status_code} - {response.text}")
            return None

    except Exception as e:
        if DEBUG:
            print(f"Erro ao buscar modelos: {str(e)}")
        return None

def update_models():
    """
    Atualiza a lista de modelos disponíveis.

    Returns:
        Lista atualizada de modelos
    """
    fetched_models = fetch_models_from_openrouter()

    if fetched_models:
        return fetched_models
    else:
        return FREE_MODELS

def get_model_by_id(model_id):
    """
    Retorna um modelo pelo ID.

    Args:
        model_id: ID do modelo

    Returns:
        Modelo ou None se não encontrado
    """
    for model in FREE_MODELS:
        if model["id"] == model_id:
            return model
    return None

# Para testes
if __name__ == "__main__":
    models = get_available_models()
    print(f"Modelos disponíveis: {len(models)}")
    for model in models:
        print(f"- {model['name']} ({model['id']})")
        print(f"  {model['description']}")
        print(f"  Suporta visão: {model['supports_vision']}")
        print(f"  Tamanho do contexto: {model['context_length']}")
        print()
