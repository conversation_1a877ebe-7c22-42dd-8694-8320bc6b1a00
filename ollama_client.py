"""
Cliente para o Ollama (LLM local).
"""
import os
import json
import base64
import requests
from config import DEBUG
from llm_client_base import LLMClientBase
from enhanced_prompt_manager import EnhancedPromptManager

class OllamaClient(LLMClientBase):
    """
    Cliente para o Ollama, que permite executar modelos LLM localmente.
    """

    # Lista de modelos Ollama que suportam visão
    VISION_MODELS = [
        {
            "id": "llava:latest",
            "name": "LLaVA",
            "description": "Modelo multimodal baseado no Llama 2",
            "supports_vision": True,
            "context_length": 4096,
            "category": "vision"
        },
        {
            "id": "bakllava:latest",
            "name": "BakLLaVA",
            "description": "Versão do LLaVA otimizada para performance",
            "supports_vision": True,
            "context_length": 4096,
            "category": "vision"
        },
        {
            "id": "llama3-vision:latest",
            "name": "Llama 3 Vision",
            "description": "Modelo Llama 3 com suporte a visão",
            "supports_vision": True,
            "context_length": 8192,
            "category": "vision"
        },
        {
            "id": "moondream:latest",
            "name": "Moondream",
            "description": "Modelo leve para visão computacional",
            "supports_vision": True,
            "context_length": 4096,
            "category": "vision"
        }
    ]

    def __init__(self, model_id="llava:latest"):
        """
        Inicializa o cliente Ollama.

        Args:
            model_id: ID do modelo Ollama a ser usado
        """
        self.base_url = "http://localhost:11434/api"
        self.model = model_id

        # Inicializa o gerenciador de prompts com validação
        self.prompt_manager = EnhancedPromptManager()

        # Verifica se o Ollama está disponível
        self.is_available = self._check_availability()

        if not self.is_available and DEBUG:
            print("Aviso: Ollama não está disponível. Verifique se o serviço está em execução.")

    def _check_availability(self):
        """
        Verifica se o Ollama está disponível.

        Returns:
            True se o Ollama estiver disponível, False caso contrário
        """
        try:
            response = requests.get(f"{self.base_url}/tags")
            return response.status_code == 200
        except Exception as e:
            if DEBUG:
                print(f"Erro ao verificar disponibilidade do Ollama: {str(e)}")
            return False

    def encode_image_to_base64(self, image_path):
        """
        Codifica uma imagem para base64.

        Args:
            image_path: Caminho para a imagem

        Returns:
            String base64 da imagem
        """
        with open(image_path, "rb") as image_file:
            return base64.b64encode(image_file.read()).decode('utf-8')

    def process_image(self, image_path, prompt=None):
        """
        Processa uma imagem com o Ollama.

        Args:
            image_path: Caminho para a imagem
            prompt: Prompt opcional para enviar junto com a imagem

        Returns:
            Resposta do Ollama
        """
        if not self.is_available:
            return "Erro: Ollama não está disponível. Verifique se o serviço está em execução."

        if not os.path.exists(image_path):
            return f"Erro: Arquivo de imagem não encontrado em {image_path}"

        try:
            # Codifica a imagem para base64
            base64_image = self.encode_image_to_base64(image_path)

            # Prepara o prompt padrão se não for fornecido
            if not prompt:
                prompt = """
                Você é um professor especialista analisando uma questão de múltipla escolha.

                MODO DE RACIOCÍNIO ATIVADO:
                1. Identifique o assunto específico da questão na imagem.
                2. Analise recursivamente a lógica por trás de cada alternativa, questionando seu próprio raciocínio.
                3. Para questões que envolvem cálculos, mostre TODOS os passos intermediários detalhadamente.
                4. Após chegar a uma conclusão inicial, reavalie-a usando uma abordagem diferente para verificação.
                5. Avalie cada alternativa metodicamente, explicando por que as incorretas devem ser descartadas.
                6. Implemente um sistema de votação interna: gere três análises independentes e escolha a resposta mais frequente.

                FORMATO OBRIGATÓRIO DA RESPOSTA:
                - Comece SEMPRE com "Resposta correta: [LETRA]) [TEXTO DA ALTERNATIVA]"
                - Responda SEMPRE em português (PT-BR), mesmo que a pergunta esteja em inglês
                - Destaque claramente por que as outras alternativas estão incorretas
                - Seja direto e objetivo, priorizando a resposta correta

                Se você não conseguir ver a imagem ou a imagem não estiver clara, informe isso na sua resposta.
                """

            # Prepara a requisição para o Ollama com parâmetros otimizados para precisão
            payload = {
                "model": self.model,
                "prompt": prompt,
                "images": [base64_image],
                "stream": False,
                "options": {
                    "temperature": 0.05,  # Temperatura muito baixa para respostas mais determinísticas
                    "top_p": 0.95,
                    "top_k": 40,
                    "num_predict": 1500,  # Aumentado para permitir análise mais detalhada
                    "stop": ["<|im_end|>"]
                }
            }

            if DEBUG:
                print(f"Enviando imagem para Ollama com o modelo {self.model}")

            # Faz a requisição para o Ollama
            response = requests.post(f"{self.base_url}/generate", json=payload)

            if response.status_code != 200:
                return f"Erro na API do Ollama: {response.status_code} - {response.text}"

            # Extrai a resposta
            result = response.json()
            answer = result.get("response", "")

            return answer

        except Exception as e:
            error_msg = f"Erro ao processar imagem com Ollama: {str(e)}"
            if DEBUG:
                print(error_msg)
            return error_msg

    def process_image_with_validation(self, image_path, question_text="", max_retries=2):
        """
        Processa uma imagem com sistema de validação integrado.

        Args:
            image_path: Caminho para a imagem
            question_text: Texto da questão (se disponível via OCR)
            max_retries: Número máximo de tentativas de reprocessamento

        Returns:
            Resposta validada do Ollama
        """
        if DEBUG:
            print("Processando imagem com validação no Ollama")

        # Se não temos o texto da questão, usa o método normal
        if not question_text:
            if DEBUG:
                print("Texto da questão não disponível, usando processamento normal")
            return self.process_image(image_path)

        # Primeira tentativa com prompt normal
        llm_response = self.process_image(image_path)

        # Valida a resposta
        validation_result = self.prompt_manager.validate_response(question_text, llm_response)

        if DEBUG:
            print(f"Resultado da validação: {validation_result}")

        # Se a validação falhou, tenta com prompt de validação
        retry_count = 0
        while not validation_result.get('is_valid', True) and retry_count < max_retries:
            if DEBUG:
                print(f"Validação falhou: {validation_result.get('error', 'Erro desconhecido')}")
                print(f"Tentativa {retry_count + 1} com prompt de validação...")

            # Usa prompt com validação
            validated_prompt = self.prompt_manager.create_validated_prompt(question_text)
            llm_response = self.process_image(image_path, validated_prompt)

            # Valida novamente
            validation_result = self.prompt_manager.validate_response(question_text, llm_response)
            retry_count += 1

        # Formata a resposta final
        if validation_result.get('is_valid', True):
            formatted_response = self.prompt_manager.format_final_response(validation_result, llm_response)
            return formatted_response
        else:
            # Se ainda assim falhou, retorna a resposta original com aviso
            return f"⚠️ Resposta com validação limitada:\n\n{llm_response}\n\n(Erro de validação: {validation_result.get('error', 'Desconhecido')})"

    def get_available_models(self):
        """
        Retorna a lista de modelos disponíveis no Ollama.

        Returns:
            Lista de dicionários com informações dos modelos
        """
        if not self.is_available:
            return self.VISION_MODELS

        try:
            response = requests.get(f"{self.base_url}/tags")

            if response.status_code != 200:
                return self.VISION_MODELS

            # Filtra apenas os modelos que suportam visão
            models = response.json().get("models", [])
            vision_models = []

            for model in models:
                model_name = model.get("name", "")
                # Verifica se o modelo está na lista de modelos que suportam visão
                for vision_model in self.VISION_MODELS:
                    if vision_model["id"] in model_name:
                        vision_models.append({
                            "id": model_name,
                            "name": vision_model["name"],
                            "description": vision_model["description"],
                            "supports_vision": True,
                            "context_length": vision_model["context_length"],
                            "category": "vision"
                        })

            # Se não encontrou nenhum modelo com suporte a visão, retorna a lista padrão
            if not vision_models:
                return self.VISION_MODELS

            return vision_models

        except Exception as e:
            if DEBUG:
                print(f"Erro ao obter modelos do Ollama: {str(e)}")
            return self.VISION_MODELS

    def set_model(self, model_id):
        """
        Define o modelo a ser usado.

        Args:
            model_id: ID do modelo

        Returns:
            True se o modelo foi definido com sucesso, False caso contrário
        """
        # Verifica se o modelo está disponível
        available_models = self.get_available_models()
        model_ids = [model["id"] for model in available_models]

        if model_id in model_ids:
            self.model = model_id
            return True
        else:
            # Se o modelo não estiver disponível, usa o primeiro da lista
            if available_models:
                self.model = available_models[0]["id"]
                if DEBUG:
                    print(f"Modelo {model_id} não disponível. Usando {self.model} como alternativa.")
                return True
            return False

    def get_provider_name(self):
        """
        Retorna o nome do provedor de LLM.

        Returns:
            Nome do provedor
        """
        return "Ollama (Local)"
