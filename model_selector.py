"""
Módulo para seleção de modelos LLM.
"""
from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
                           QComboBox, QGroupBox, QFormLayout, QTabWidget, QWidget, QCheckBox)
from PyQt5.QtCore import Qt, QSettings
from llm_models import get_available_models, get_vision_models, get_text_models, get_model_by_id

class ModelSelectorDialog(QDialog):
    def __init__(self, parent=None, current_model_id=None, detect_any_change=False):
        super().__init__(parent)
        self.setWindowTitle("Selecionar Modelo LLM")
        self.resize(400, 350)  # Tamanho reduzido
        self.current_model_id = current_model_id
        self.selected_model_id = None
        self.detect_any_change = detect_any_change

        # Inicializa as configurações
        self.settings = QSettings("AugmentCode", "PythonQuestionHelper")

        # Cria o layout principal
        main_layout = QVBoxLayout()

        # Cria as abas
        self.tab_widget = QTabWidget()

        # Cria as abas individuais
        self.create_vision_tab()
        self.create_text_tab()

        # Adiciona as abas ao widget de abas
        main_layout.addWidget(self.tab_widget)

        # Informações do modelo selecionado
        self.model_info_group = QGroupBox("Informações do Modelo Selecionado")
        model_info_layout = QVBoxLayout()
        self.model_info_label = QLabel("Selecione um modelo para ver suas informações")
        self.model_info_label.setWordWrap(True)
        model_info_layout.addWidget(self.model_info_label)
        self.model_info_group.setLayout(model_info_layout)
        main_layout.addWidget(self.model_info_group)

        # Opções de monitoramento e marcação automática
        self.monitor_group = QGroupBox("Opções de Monitoramento")
        monitor_layout = QVBoxLayout()

        # Checkbox para detectar qualquer alteração visual
        self.detect_any_change_check = QCheckBox("Detectar qualquer alteração visual (não apenas mudança de questão)")
        self.detect_any_change_check.setChecked(self.detect_any_change)
        self.detect_any_change_check.setToolTip("Quando ativado, o sistema detectará qualquer alteração visual na tela, não apenas mudanças de questão")

        # Explicação sobre a detecção de alterações
        detect_label = QLabel("Por padrão, o sistema detecta apenas mudanças de questão (ex: Questão 7 → Questão 8). Ative esta opção se quiser detectar qualquer alteração visual.")
        detect_label.setWordWrap(True)

        # Checkbox para mostrar o apontador visual
        self.show_pointer_check = QCheckBox("Mostrar apontador visual para a resposta correta")
        self.show_pointer_check.setChecked(self.show_answer_pointer)
        self.show_pointer_check.setToolTip("Quando ativado, o sistema mostrará um círculo ao redor da alternativa correta na tela")

        # Explicação sobre o apontador visual
        pointer_label = QLabel("O apontador visual destaca a alternativa correta na tela, facilitando a identificação da resposta. Funciona como um 'laser pointer' virtual.")
        pointer_label.setWordWrap(True)

        monitor_layout.addWidget(self.detect_any_change_check)
        monitor_layout.addWidget(detect_label)
        monitor_layout.addWidget(self.show_pointer_check)
        monitor_layout.addWidget(pointer_label)

        self.monitor_group.setLayout(monitor_layout)
        main_layout.addWidget(self.monitor_group)

        # Opção de marcação automática
        self.auto_mark_group = QGroupBox("Marcação Automática")
        auto_mark_layout = QVBoxLayout()

        # Checkbox para ativar/desativar marcação automática
        self.auto_mark_check = QCheckBox("Marcar respostas automaticamente")
        self.auto_mark_check.setChecked(self.auto_mark_enabled)
        self.auto_mark_check.setToolTip("Quando ativado, o sistema tentará marcar automaticamente as respostas e avançar para a próxima questão")

        # Aviso sobre a marcação automática
        warning_label = QLabel("⚠️ ATENÇÃO: A marcação automática tentará clicar nas alternativas e avançar para a próxima questão. Use com cautela e esteja pronto para intervir manualmente se necessário.")
        warning_label.setWordWrap(True)
        warning_label.setStyleSheet("color: #cc6600; font-weight: bold;")

        auto_mark_layout.addWidget(self.auto_mark_check)
        auto_mark_layout.addWidget(warning_label)

        self.auto_mark_group.setLayout(auto_mark_layout)
        main_layout.addWidget(self.auto_mark_group)

        # Botões de OK e Cancelar
        button_layout = QHBoxLayout()
        self.ok_button = QPushButton("OK")
        self.ok_button.clicked.connect(self.accept)
        self.cancel_button = QPushButton("Cancelar")
        self.cancel_button.clicked.connect(self.reject)

        button_layout.addStretch()
        button_layout.addWidget(self.cancel_button)
        button_layout.addWidget(self.ok_button)

        main_layout.addLayout(button_layout)

        self.setLayout(main_layout)

        # Seleciona o modelo atual
        if current_model_id:
            self.select_current_model(current_model_id)

    def create_vision_tab(self):
        """Cria a aba de modelos com suporte a visão."""
        vision_tab = QWidget()
        layout = QVBoxLayout()

        # Lista de modelos com suporte a visão
        vision_models = get_vision_models()

        # Cria um botão para cada modelo
        for model in vision_models:
            model_button = QPushButton(f"{model['name']}")
            model_button.setToolTip(model['description'])
            model_button.setProperty("model_id", model['id'])
            model_button.clicked.connect(self.on_model_selected)

            # Destaca o botão se for o modelo atual
            if self.current_model_id == model['id']:
                model_button.setStyleSheet("background-color: #d0f0d0; font-weight: bold;")

            layout.addWidget(model_button)

        layout.addStretch()

        vision_tab.setLayout(layout)
        self.tab_widget.addTab(vision_tab, "Modelos com Visão")

    def create_text_tab(self):
        """Cria a aba de modelos apenas de texto."""
        text_tab = QWidget()
        layout = QVBoxLayout()

        # Lista de modelos apenas de texto
        text_models = get_text_models()

        # Cria um botão para cada modelo
        for model in text_models:
            model_button = QPushButton(f"{model['name']}")
            model_button.setToolTip(model['description'])
            model_button.setProperty("model_id", model['id'])
            model_button.clicked.connect(self.on_model_selected)

            # Destaca o botão se for o modelo atual
            if self.current_model_id == model['id']:
                model_button.setStyleSheet("background-color: #d0f0d0; font-weight: bold;")

            layout.addWidget(model_button)

        layout.addStretch()

        text_tab.setLayout(layout)
        self.tab_widget.addTab(text_tab, "Modelos de Texto")

    def on_model_selected(self):
        """Chamado quando um modelo é selecionado."""
        sender = self.sender()
        model_id = sender.property("model_id")
        self.selected_model_id = model_id

        # Atualiza as informações do modelo
        model = get_model_by_id(model_id)
        if model:
            info_text = f"<b>{model['name']}</b><br>"
            info_text += f"{model['description']}<br><br>"
            info_text += f"<b>Suporta visão:</b> {'Sim' if model['supports_vision'] else 'Não'}<br>"
            info_text += f"<b>Tamanho do contexto:</b> {model['context_length']} tokens<br>"
            info_text += f"<b>ID:</b> {model['id']}"

            self.model_info_label.setText(info_text)

            # Habilita o botão OK
            self.ok_button.setEnabled(True)

            # Destaca o botão selecionado e remove o destaque dos outros
            for tab_index in range(self.tab_widget.count()):
                tab = self.tab_widget.widget(tab_index)
                for i in range(tab.layout().count()):
                    item = tab.layout().itemAt(i)
                    if item and item.widget() and isinstance(item.widget(), QPushButton):
                        button = item.widget()
                        if button.property("model_id") == model_id:
                            button.setStyleSheet("background-color: #d0f0d0; font-weight: bold;")
                        else:
                            button.setStyleSheet("")

    def select_current_model(self, model_id):
        """Seleciona o modelo atual."""
        self.selected_model_id = model_id

        # Atualiza as informações do modelo
        model = get_model_by_id(model_id)
        if model:
            info_text = f"<b>{model['name']}</b><br>"
            info_text += f"{model['description']}<br><br>"
            info_text += f"<b>Suporta visão:</b> {'Sim' if model['supports_vision'] else 'Não'}<br>"
            info_text += f"<b>Tamanho do contexto:</b> {model['context_length']} tokens<br>"
            info_text += f"<b>ID:</b> {model['id']}"

            self.model_info_label.setText(info_text)

            # Seleciona a aba correta
            if model.get('category') == 'vision':
                self.tab_widget.setCurrentIndex(0)
            else:
                self.tab_widget.setCurrentIndex(1)

    def get_selected_model_id(self):
        """Retorna o ID do modelo selecionado."""
        return self.selected_model_id

    def is_auto_mark_enabled(self):
        """Retorna se a marcação automática está ativada."""
        return self.auto_mark_check.isChecked()

    def is_detect_any_change_enabled(self):
        """Retorna se a detecção de qualquer alteração visual está ativada."""
        return self.detect_any_change_check.isChecked()

    def is_show_pointer_enabled(self):
        """Retorna se o apontador visual está ativado."""
        return self.show_pointer_check.isChecked()

# Para testes
if __name__ == "__main__":
    from PyQt5.QtWidgets import QApplication
    import sys

    app = QApplication(sys.argv)
    dialog = ModelSelectorDialog(current_model_id="anthropic/claude-3-opus-20240229:free")
    if dialog.exec_() == QDialog.Accepted:
        print(f"Modelo selecionado: {dialog.get_selected_model_id()}")
    else:
        print("Seleção cancelada")
