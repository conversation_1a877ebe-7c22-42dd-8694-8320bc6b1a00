"""
Python Question Helper - Sistema Inteligente de Assistência para Questões

Um sistema avançado de OCR e IA para auxiliar na resolução de questões,
com análise de padrões e sistema de logging para melhoria contínua.

Desenvolvido por BoZolinO
Versão: 1.0.0
"""

__version__ = "1.0.0"
__author__ = "BoZolinO"
__email__ = "<EMAIL>"
__description__ = "Sistema Inteligente de Assistência para Questões"

# Imports principais removidos para evitar imports circulares
# Use imports diretos quando necessário:
# from src.core.main import QuestionHelper
# from src.core.config import DEBUG, VERSION
# from src.logging.question_logger import QuestionLogger
# from src.analytics.analytics_module import AnalyticsModule

__all__ = [
    # Exports serão definidos quando necessário
]
