"""
Python Question Helper - Sistema Inteligente de Assistência para Questões

Um sistema avançado de OCR e IA para auxiliar na resolução de questões,
com análise de padrões e sistema de logging para melhoria contínua.

Desenvolvido por BoZolinO
Versão: 1.0.0
"""

__version__ = "1.0.0"
__author__ = "BoZolinO"
__email__ = "<EMAIL>"
__description__ = "Sistema Inteligente de Assistência para Questões"

# Imports principais
from .core.main import QuestionHelper
from .core.config import DEBUG, VERSION
from .logging.question_logger import QuestionLogger
from .analytics.analytics_module import AnalyticsModule

__all__ = [
    'QuestionHelper',
    'QuestionLogger', 
    'AnalyticsModule',
    'DEBUG',
    'VERSION'
]
