"""
Controles de interface para o módulo de áudio.
"""
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QComboBox, QSlider, QCheckBox, QGroupBox, QProgressBar,
    QSpinBox, QFrame
)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal
from PyQt5.QtGui import QFont, QPalette

from ..core.config import (
    ENABLE_AUDIO_MODULE, STT_PROVIDER, STT_LANGUAGE,
    AUDIO_SAMPLE_RATE, AUDIO_CAPTURE_DURATION, AUDIO_GAIN,
    DEBUG
)


class AudioControlsWidget(QWidget):
    """
    Widget de controles para o módulo de áudio.
    """
    
    # Sinais
    audio_toggle_requested = pyqtSignal(bool)  # True para iniciar, False para parar
    audio_text_received = pyqtSignal(str)      # Texto extraído do áudio
    settings_changed = pyqtSignal(dict)        # Configurações alteradas
    
    def __init__(self, parent=None):
        """
        Inicializa os controles de áudio.
        
        Args:
            parent: Widget pai
        """
        super().__init__(parent)
        
        self.audio_processor = None
        self.is_recording = False
        self.is_processing = False
        
        # Timer para atualizar status
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self._update_status)
        self.status_timer.start(1000)  # Atualiza a cada segundo
        
        self._setup_ui()
        self._connect_signals()
        
        # Verifica se o módulo está habilitado
        if not ENABLE_AUDIO_MODULE:
            self.setEnabled(False)
            if DEBUG:
                print("Controles de áudio desabilitados (módulo não habilitado)")
    
    def _setup_ui(self):
        """Configura a interface de usuário."""
        layout = QVBoxLayout(self)
        layout.setSpacing(10)
        
        # Título
        title_label = QLabel("🎤 Processamento de Áudio")
        title_font = QFont()
        title_font.setBold(True)
        title_font.setPointSize(12)
        title_label.setFont(title_font)
        layout.addWidget(title_label)
        
        # Separador
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        layout.addWidget(separator)
        
        # Controles principais
        main_controls = self._create_main_controls()
        layout.addWidget(main_controls)
        
        # Configurações
        settings_group = self._create_settings_group()
        layout.addWidget(settings_group)
        
        # Status
        status_group = self._create_status_group()
        layout.addWidget(status_group)
        
        # Espaçador
        layout.addStretch()
    
    def _create_main_controls(self) -> QGroupBox:
        """Cria os controles principais."""
        group = QGroupBox("Controles")
        layout = QVBoxLayout(group)
        
        # Botão de gravação
        self.record_button = QPushButton("🎤 Iniciar Captura de Áudio")
        self.record_button.setMinimumHeight(40)
        self.record_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #3d8b40;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)
        layout.addWidget(self.record_button)
        
        # Indicador de status
        status_layout = QHBoxLayout()
        
        self.status_label = QLabel("⚪ Inativo")
        self.status_label.setStyleSheet("color: #666666; font-weight: bold;")
        status_layout.addWidget(self.status_label)
        
        status_layout.addStretch()
        
        # Barra de progresso
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setMaximum(0)  # Indeterminado
        status_layout.addWidget(self.progress_bar)
        
        layout.addLayout(status_layout)
        
        return group
    
    def _create_settings_group(self) -> QGroupBox:
        """Cria o grupo de configurações."""
        group = QGroupBox("Configurações")
        layout = QVBoxLayout(group)
        
        # Provedor STT
        stt_layout = QHBoxLayout()
        stt_layout.addWidget(QLabel("Provedor STT:"))
        
        self.stt_combo = QComboBox()
        self.stt_combo.addItems(["whisper", "google", "azure", "aws"])
        self.stt_combo.setCurrentText(STT_PROVIDER)
        stt_layout.addWidget(self.stt_combo)
        
        layout.addLayout(stt_layout)
        
        # Idioma
        lang_layout = QHBoxLayout()
        lang_layout.addWidget(QLabel("Idioma:"))
        
        self.language_combo = QComboBox()
        self.language_combo.addItems([
            "pt-BR", "en-US", "es-ES", "fr-FR", "de-DE", "it-IT"
        ])
        self.language_combo.setCurrentText(STT_LANGUAGE)
        lang_layout.addWidget(self.language_combo)
        
        layout.addLayout(lang_layout)
        
        # Duração de captura
        duration_layout = QHBoxLayout()
        duration_layout.addWidget(QLabel("Duração (s):"))
        
        self.duration_spin = QSpinBox()
        self.duration_spin.setRange(5, 120)
        self.duration_spin.setValue(AUDIO_CAPTURE_DURATION)
        duration_layout.addWidget(self.duration_spin)
        
        layout.addLayout(duration_layout)
        
        # Ganho de áudio
        gain_layout = QHBoxLayout()
        gain_layout.addWidget(QLabel("Ganho:"))
        
        self.gain_slider = QSlider(Qt.Horizontal)
        self.gain_slider.setRange(50, 200)  # 0.5x a 2.0x
        self.gain_slider.setValue(int(AUDIO_GAIN * 100))
        
        self.gain_label = QLabel(f"{AUDIO_GAIN:.1f}x")
        self.gain_label.setMinimumWidth(40)
        
        gain_layout.addWidget(self.gain_slider)
        gain_layout.addWidget(self.gain_label)
        
        layout.addLayout(gain_layout)
        
        # Opções avançadas
        self.noise_reduction_cb = QCheckBox("Redução de ruído")
        self.noise_reduction_cb.setChecked(True)
        layout.addWidget(self.noise_reduction_cb)
        
        self.voice_enhancement_cb = QCheckBox("Melhoria de voz")
        self.voice_enhancement_cb.setChecked(True)
        layout.addWidget(self.voice_enhancement_cb)
        
        return group
    
    def _create_status_group(self) -> QGroupBox:
        """Cria o grupo de status."""
        group = QGroupBox("Status do Sistema")
        layout = QVBoxLayout(group)
        
        # Status do módulo
        self.module_status_label = QLabel("Módulo: Verificando...")
        layout.addWidget(self.module_status_label)
        
        # Status do STT
        self.stt_status_label = QLabel("STT: Verificando...")
        layout.addWidget(self.stt_status_label)
        
        # Último texto processado
        self.last_text_label = QLabel("Último texto: Nenhum")
        self.last_text_label.setWordWrap(True)
        self.last_text_label.setStyleSheet("color: #333333; font-style: italic;")
        layout.addWidget(self.last_text_label)
        
        return group
    
    def _connect_signals(self):
        """Conecta os sinais dos controles."""
        self.record_button.clicked.connect(self._toggle_recording)
        self.stt_combo.currentTextChanged.connect(self._on_settings_changed)
        self.language_combo.currentTextChanged.connect(self._on_settings_changed)
        self.duration_spin.valueChanged.connect(self._on_settings_changed)
        self.gain_slider.valueChanged.connect(self._on_gain_changed)
        self.noise_reduction_cb.toggled.connect(self._on_settings_changed)
        self.voice_enhancement_cb.toggled.connect(self._on_settings_changed)
    
    def _toggle_recording(self):
        """Alterna entre iniciar e parar gravação."""
        if not self.is_recording:
            self.audio_toggle_requested.emit(True)
        else:
            self.audio_toggle_requested.emit(False)
    
    def _on_gain_changed(self, value):
        """Callback para mudança no ganho."""
        gain = value / 100.0
        self.gain_label.setText(f"{gain:.1f}x")
        self._on_settings_changed()
    
    def _on_settings_changed(self):
        """Callback para mudanças nas configurações."""
        settings = {
            'stt_provider': self.stt_combo.currentText(),
            'language': self.language_combo.currentText(),
            'duration': self.duration_spin.value(),
            'gain': self.gain_slider.value() / 100.0,
            'noise_reduction': self.noise_reduction_cb.isChecked(),
            'voice_enhancement': self.voice_enhancement_cb.isChecked()
        }
        self.settings_changed.emit(settings)
    
    def _update_status(self):
        """Atualiza o status da interface."""
        if self.audio_processor:
            status = self.audio_processor.get_status()
            
            # Status do módulo
            if status['available']:
                self.module_status_label.setText("Módulo: ✅ Disponível")
                self.module_status_label.setStyleSheet("color: green;")
            else:
                self.module_status_label.setText("Módulo: ❌ Indisponível")
                self.module_status_label.setStyleSheet("color: red;")
            
            # Status STT
            if hasattr(self.audio_processor, 'stt_converter') and self.audio_processor.stt_converter:
                stt_status = self.audio_processor.stt_converter.get_status()
                if stt_status['available']:
                    self.stt_status_label.setText(f"STT: ✅ {stt_status['provider'].title()}")
                    self.stt_status_label.setStyleSheet("color: green;")
                else:
                    self.stt_status_label.setText(f"STT: ❌ {stt_status['provider'].title()}")
                    self.stt_status_label.setStyleSheet("color: red;")
            else:
                self.stt_status_label.setText("STT: ❌ Não configurado")
                self.stt_status_label.setStyleSheet("color: red;")
            
            # Atualiza estado dos controles
            self.is_recording = status['recording']
            self.is_processing = status['processing']
            
            if self.is_recording:
                self.record_button.setText("🛑 Parar Captura")
                self.record_button.setStyleSheet("""
                    QPushButton {
                        background-color: #f44336;
                        color: white;
                        border: none;
                        border-radius: 5px;
                        font-size: 14px;
                        font-weight: bold;
                    }
                    QPushButton:hover {
                        background-color: #da190b;
                    }
                """)
                self.status_label.setText("🔴 Gravando...")
                self.status_label.setStyleSheet("color: red; font-weight: bold;")
                self.progress_bar.setVisible(True)
            elif self.is_processing:
                self.record_button.setText("⏳ Processando...")
                self.record_button.setEnabled(False)
                self.status_label.setText("⚙️ Processando áudio...")
                self.status_label.setStyleSheet("color: orange; font-weight: bold;")
                self.progress_bar.setVisible(True)
            else:
                self.record_button.setText("🎤 Iniciar Captura de Áudio")
                self.record_button.setEnabled(True)
                self.record_button.setStyleSheet("""
                    QPushButton {
                        background-color: #4CAF50;
                        color: white;
                        border: none;
                        border-radius: 5px;
                        font-size: 14px;
                        font-weight: bold;
                    }
                    QPushButton:hover {
                        background-color: #45a049;
                    }
                """)
                self.status_label.setText("⚪ Inativo")
                self.status_label.setStyleSheet("color: #666666; font-weight: bold;")
                self.progress_bar.setVisible(False)
    
    def set_audio_processor(self, processor):
        """
        Define o processador de áudio.
        
        Args:
            processor: Instância do AudioProcessor
        """
        self.audio_processor = processor
        if DEBUG:
            print("AudioProcessor definido nos controles")
    
    def on_audio_text_received(self, text: str):
        """
        Callback para quando texto é extraído do áudio.
        
        Args:
            text: Texto extraído
        """
        # Atualiza label do último texto
        display_text = text[:100] + "..." if len(text) > 100 else text
        self.last_text_label.setText(f"Último texto: {display_text}")
        
        # Emite sinal
        self.audio_text_received.emit(text)
        
        if DEBUG:
            print(f"Texto recebido nos controles: {text}")
    
    def get_current_settings(self) -> dict:
        """
        Retorna as configurações atuais.
        
        Returns:
            Dicionário com configurações
        """
        return {
            'stt_provider': self.stt_combo.currentText(),
            'language': self.language_combo.currentText(),
            'duration': self.duration_spin.value(),
            'gain': self.gain_slider.value() / 100.0,
            'noise_reduction': self.noise_reduction_cb.isChecked(),
            'voice_enhancement': self.voice_enhancement_cb.isChecked()
        }
