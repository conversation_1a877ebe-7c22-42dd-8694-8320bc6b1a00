"""
Sistema de validação e verificação de respostas para LLM.
Garante precisão e fundamentação adequada das respostas.
"""
import re
from typing import Dict, List, Tuple, Optional
from config import DEBUG

class ResponseValidator:
    """
    Sistema completo de validação e verificação de respostas do LLM.
    """

    # Domínios de conhecimento e suas características
    KNOWLEDGE_DOMAINS = {
        'matematica': {
            'keywords': ['calcular', 'equação', 'fórmula', 'número', 'soma', 'multiplicação', 'divisão', 'porcentagem', 'função', 'derivada', 'integral', 'geometria', 'álgebra'],
            'confidence_base': 90,  # Alta confiança para cálculos
            'requires_steps': True
        },
        'fisica': {
            'keywords': ['força', 'energia', 'velocidade', 'aceleração', 'massa', 'newton', 'joule', 'watt', 'movimento', 'óptica', 'termodinâmica', 'eletricidade'],
            'confidence_base': 85,
            'requires_steps': True
        },
        'quimica': {
            'keywords': ['elemento', 'átomo', 'molécula', 'reação', 'ph', 'ácido', 'base', 'oxidação', 'redução', 'tabela periódica', 'ligação'],
            'confidence_base': 85,
            'requires_steps': True
        },
        'direito': {
            'keywords': ['lei', 'artigo', 'código', 'constituição', 'jurisprudência', 'processo', 'tribunal', 'crime', 'contravenção', 'civil', 'penal'],
            'confidence_base': 80,
            'requires_steps': False
        },
        'historia': {
            'keywords': ['século', 'guerra', 'revolução', 'império', 'república', 'período', 'dinastia', 'tratado', 'independência'],
            'confidence_base': 75,
            'requires_steps': False
        },
        'geografia': {
            'keywords': ['país', 'capital', 'continente', 'oceano', 'clima', 'relevo', 'população', 'economia', 'região'],
            'confidence_base': 75,
            'requires_steps': False
        },
        'biologia': {
            'keywords': ['célula', 'dna', 'gene', 'espécie', 'evolução', 'ecossistema', 'fotossíntese', 'respiração', 'sistema'],
            'confidence_base': 80,
            'requires_steps': False
        },
        'portugues': {
            'keywords': ['gramática', 'sintaxe', 'semântica', 'literatura', 'texto', 'interpretação', 'concordância', 'regência'],
            'confidence_base': 70,
            'requires_steps': False
        },
        'ingles': {
            'keywords': ['grammar', 'vocabulary', 'tense', 'verb', 'noun', 'adjective', 'preposition', 'translation'],
            'confidence_base': 70,
            'requires_steps': False
        }
    }

    def __init__(self):
        """Inicializa o validador de respostas."""
        if DEBUG:
            print("Inicializando ResponseValidator...")

    def extract_alternatives_from_question(self, question_text: str) -> List[str]:
        """
        Extrai as alternativas disponíveis da questão.

        Args:
            question_text: Texto da questão

        Returns:
            Lista de alternativas encontradas
        """
        alternatives = []

        # Padrões para identificar alternativas (mais flexíveis)
        patterns = [
            r'[A-E]\)\s*([^\n]+?)(?=\s*[A-E]\)|$)',  # A) texto até próxima alternativa
            r'[A-E]\s*-\s*([^\n]+?)(?=\s*[A-E]\s*-|$)',  # A - texto
            r'[A-E]\.\s*([^\n]+?)(?=\s*[A-E]\.|$)',  # A. texto
            r'[A-E]\s*:\s*([^\n]+?)(?=\s*[A-E]\s*:|$)',  # A: texto
        ]

        for pattern in patterns:
            matches = re.findall(pattern, question_text, re.IGNORECASE | re.MULTILINE | re.DOTALL)
            if matches:
                alternatives = [match.strip() for match in matches]
                break

        # Se não encontrou com os padrões acima, tenta uma abordagem mais simples
        if not alternatives:
            lines = question_text.split('\n')
            for line in lines:
                line = line.strip()
                if re.match(r'^[A-E]\)', line):
                    # Remove a letra e o parêntese
                    alt_text = re.sub(r'^[A-E]\)\s*', '', line).strip()
                    if alt_text:
                        alternatives.append(alt_text)

        if DEBUG:
            print(f"Alternativas extraídas: {alternatives}")

        return alternatives

    def identify_knowledge_domain(self, question_text: str) -> str:
        """
        Identifica o domínio de conhecimento da questão.

        Args:
            question_text: Texto da questão

        Returns:
            Domínio identificado
        """
        question_lower = question_text.lower()

        # Conta palavras-chave por domínio
        domain_scores = {}
        for domain, info in self.KNOWLEDGE_DOMAINS.items():
            score = sum(1 for keyword in info['keywords'] if keyword in question_lower)
            if score > 0:
                domain_scores[domain] = score

        # Retorna o domínio com maior pontuação
        if domain_scores:
            identified_domain = max(domain_scores, key=domain_scores.get)
            if DEBUG:
                print(f"Domínio identificado: {identified_domain} (score: {domain_scores[identified_domain]})")
            return identified_domain

        if DEBUG:
            print("Domínio não identificado, usando 'geral'")
        return 'geral'

    def calculate_confidence_level(self, domain: str, response_type: str, has_calculations: bool = False) -> int:
        """
        Calcula o nível de confiança da resposta.

        Args:
            domain: Domínio de conhecimento
            response_type: Tipo de resposta (factual, calculation, inference, estimation)
            has_calculations: Se a resposta inclui cálculos

        Returns:
            Nível de confiança (0-100)
        """
        base_confidence = self.KNOWLEDGE_DOMAINS.get(domain, {}).get('confidence_base', 60)

        # Ajustes baseados no tipo de resposta
        type_adjustments = {
            'factual': 0,      # Conhecimento factual estabelecido
            'calculation': 10,  # Aplicação de fórmulas/leis
            'inference': -15,   # Inferência lógica fundamentada
            'estimation': -30   # Estimativa baseada em padrões
        }

        confidence = base_confidence + type_adjustments.get(response_type, -20)

        # Bônus para cálculos mostrados
        if has_calculations and domain in ['matematica', 'fisica', 'quimica']:
            confidence += 5

        # Garante que está no range 0-100
        confidence = max(0, min(100, confidence))

        if DEBUG:
            print(f"Confiança calculada: {confidence}% (domínio: {domain}, tipo: {response_type})")

        return confidence

    def validate_response_format(self, response: str) -> Tuple[bool, str, str]:
        """
        Valida se a resposta está no formato correto.

        Args:
            response: Resposta do LLM

        Returns:
            Tupla (é_válida, letra_resposta, texto_alternativa)
        """
        # Padrão para resposta correta
        pattern = r'Resposta correta:\s*([A-E])\)\s*(.+?)(?:\n|$)'
        match = re.search(pattern, response, re.IGNORECASE | re.MULTILINE)

        if match:
            letter = match.group(1).upper()
            text = match.group(2).strip()
            return True, letter, text

        if DEBUG:
            print("Formato de resposta inválido")

        return False, "", ""

    def check_alternative_exists(self, selected_letter: str, selected_text: str, available_alternatives: List[str]) -> bool:
        """
        Verifica se a alternativa selecionada existe entre as disponíveis.

        Args:
            selected_letter: Letra da alternativa selecionada
            selected_text: Texto da alternativa selecionada
            available_alternatives: Lista de alternativas disponíveis

        Returns:
            True se a alternativa existe
        """
        if not available_alternatives:
            if DEBUG:
                print("Nenhuma alternativa disponível para comparação")
            return True  # Se não conseguimos extrair alternativas, assumimos que está correto

        # Verifica se o índice da letra está dentro do range
        letter_index = ord(selected_letter) - ord('A')
        if letter_index >= len(available_alternatives):
            if DEBUG:
                print(f"Letra {selected_letter} não existe (apenas {len(available_alternatives)} alternativas)")
            return False

        # Verifica similaridade do texto (permite pequenas diferenças)
        expected_text = available_alternatives[letter_index]
        similarity = self._calculate_text_similarity(selected_text, expected_text)

        if DEBUG:
            print(f"Similaridade entre textos: {similarity:.2f}")
            print(f"Selecionado: {selected_text}")
            print(f"Esperado: {expected_text}")

        return similarity > 0.7  # 70% de similaridade mínima

    def _calculate_text_similarity(self, text1: str, text2: str) -> float:
        """
        Calcula a similaridade entre dois textos.

        Args:
            text1: Primeiro texto
            text2: Segundo texto

        Returns:
            Similaridade (0.0 a 1.0)
        """
        # Normaliza os textos
        t1 = re.sub(r'[^\w\s]', '', text1.lower()).strip()
        t2 = re.sub(r'[^\w\s]', '', text2.lower()).strip()

        # Calcula palavras em comum
        words1 = set(t1.split())
        words2 = set(t2.split())

        if not words1 and not words2:
            return 1.0

        if not words1 or not words2:
            return 0.0

        intersection = words1.intersection(words2)
        union = words1.union(words2)

        return len(intersection) / len(union)

    def generate_validation_prompt(self, question_text: str, domain: str) -> str:
        """
        Gera um prompt de validação específico para o domínio.

        Args:
            question_text: Texto da questão
            domain: Domínio de conhecimento

        Returns:
            Prompt de validação
        """
        domain_info = self.KNOWLEDGE_DOMAINS.get(domain, {})
        requires_steps = domain_info.get('requires_steps', False)

        base_prompt = f"""
        SISTEMA DE VALIDAÇÃO E VERIFICAÇÃO DE RESPOSTAS ATIVADO

        DOMÍNIO IDENTIFICADO: {domain.upper()}

        PROCESSO OBRIGATÓRIO DE VALIDAÇÃO:

        1. VERIFICAÇÃO DE ALTERNATIVAS:
           - Identifique TODAS as alternativas disponíveis (A, B, C, D, E)
           - Certifique-se de que sua resposta corresponde EXATAMENTE a uma das alternativas
           - Se sua resposta inicial não corresponder, REPROCESSE a questão

        2. FUNDAMENTAÇÃO BASEADA EM CONHECIMENTO ESPECÍFICO:
           - Aplique leis, fórmulas, princípios ou regulamentações específicas da área
           - EVITE respostas baseadas em "achismo" ou intuição
           - Base sua resposta em conhecimento factual documentado
        """

        if requires_steps:
            base_prompt += """
        3. CÁLCULOS E PASSOS (OBRIGATÓRIO PARA ESTA ÁREA):
           - Mostre TODOS os passos matemáticos/científicos
           - Identifique as fórmulas utilizadas
           - Apresente os cálculos de forma clara e sequencial
        """

        base_prompt += """
        4. SISTEMA DE AUTO-VERIFICAÇÃO INTERNA:
           - Após gerar a resposta inicial, questione: "Esta resposta está correta?"
           - Reavalie usando uma metodologia alternativa
           - Compare os resultados e escolha a resposta mais consistente

        5. CLASSIFICAÇÃO DA FONTE DO CONHECIMENTO:
           - Conhecimento factual estabelecido (alta confiança)
           - Aplicação de fórmulas/leis (alta confiança)
           - Inferência lógica fundamentada (média confiança)
           - Estimativa baseada em padrões (baixa confiança)

        FORMATO OBRIGATÓRIO DA RESPOSTA:
        Resposta correta: [LETRA]) [TEXTO EXATO DA ALTERNATIVA]

        Explicação: [Raciocínio conciso baseado em conhecimento específico]

        Confiança: [X]% (baseado em: [fonte do conhecimento])

        Alternativas incorretas: [Justificativa breve para cada uma]

        IMPORTANTE: Se sua confiança for inferior a 70%, REPROCESSE usando abordagem diferente.
        """

        return base_prompt

    def validate_and_process_response(self, question_text: str, llm_response: str) -> Dict:
        """
        Valida e processa a resposta do LLM.

        Args:
            question_text: Texto da questão original
            llm_response: Resposta do LLM

        Returns:
            Dicionário com informações da validação
        """
        if DEBUG:
            print("Iniciando validação da resposta...")

        # 1. Extrai alternativas da questão
        available_alternatives = self.extract_alternatives_from_question(question_text)

        # 2. Identifica o domínio
        domain = self.identify_knowledge_domain(question_text)

        # 3. Valida formato da resposta
        is_valid_format, selected_letter, selected_text = self.validate_response_format(llm_response)

        if not is_valid_format:
            return {
                'is_valid': False,
                'error': 'Formato de resposta inválido',
                'needs_reprocessing': True,
                'domain': domain,
                'confidence': 0
            }

        # 4. Verifica se a alternativa existe
        alternative_exists = self.check_alternative_exists(selected_letter, selected_text, available_alternatives)

        if not alternative_exists:
            return {
                'is_valid': False,
                'error': f'Alternativa {selected_letter} não corresponde às opções disponíveis',
                'needs_reprocessing': True,
                'domain': domain,
                'confidence': 0
            }

        # 5. Extrai informações da resposta
        confidence = self.extract_confidence_from_response(llm_response)
        response_type = self.classify_response_type(llm_response)
        has_calculations = self.check_for_calculations(llm_response)

        # 6. Calcula confiança se não foi fornecida
        if confidence == 0:
            confidence = self.calculate_confidence_level(domain, response_type, has_calculations)

        # 7. Verifica se precisa reprocessar por baixa confiança
        needs_reprocessing = confidence < 70

        return {
            'is_valid': True,
            'selected_letter': selected_letter,
            'selected_text': selected_text,
            'domain': domain,
            'confidence': confidence,
            'response_type': response_type,
            'has_calculations': has_calculations,
            'needs_reprocessing': needs_reprocessing,
            'available_alternatives': available_alternatives
        }

    def extract_confidence_from_response(self, response: str) -> int:
        """
        Extrai o nível de confiança da resposta.

        Args:
            response: Resposta do LLM

        Returns:
            Nível de confiança (0-100)
        """
        # Procura por padrões de confiança
        patterns = [
            r'Confiança:\s*(\d+)%',
            r'Confiança:\s*(\d+)',
            r'Confidence:\s*(\d+)%',
            r'Confidence:\s*(\d+)'
        ]

        for pattern in patterns:
            match = re.search(pattern, response, re.IGNORECASE)
            if match:
                confidence = int(match.group(1))
                return max(0, min(100, confidence))

        return 0  # Não encontrou confiança explícita

    def classify_response_type(self, response: str) -> str:
        """
        Classifica o tipo de resposta baseado no conteúdo.

        Args:
            response: Resposta do LLM

        Returns:
            Tipo de resposta
        """
        response_lower = response.lower()

        # Indicadores de conhecimento factual
        factual_indicators = ['lei', 'artigo', 'constituição', 'fórmula', 'teorema', 'princípio', 'definição']
        if any(indicator in response_lower for indicator in factual_indicators):
            return 'factual'

        # Indicadores de cálculo
        calculation_indicators = ['=', '+', '-', '*', '/', 'calcular', 'resultado', 'soma', 'multiplicar']
        if any(indicator in response_lower for indicator in calculation_indicators):
            return 'calculation'

        # Indicadores de inferência
        inference_indicators = ['portanto', 'logo', 'assim', 'consequentemente', 'deduz-se', 'conclui-se']
        if any(indicator in response_lower for indicator in inference_indicators):
            return 'inference'

        # Indicadores de estimativa
        estimation_indicators = ['aproximadamente', 'cerca de', 'provavelmente', 'possivelmente', 'estima-se']
        if any(indicator in response_lower for indicator in estimation_indicators):
            return 'estimation'

        return 'inference'  # Padrão

    def check_for_calculations(self, response: str) -> bool:
        """
        Verifica se a resposta contém cálculos.

        Args:
            response: Resposta do LLM

        Returns:
            True se contém cálculos
        """
        # Procura por operações matemáticas
        math_patterns = [
            r'\d+\s*[+\-*/]\s*\d+',  # Operações básicas
            r'\d+\s*=\s*\d+',        # Igualdades
            r'\d+\s*%',              # Porcentagens
            r'\d+\s*\^\s*\d+',       # Potências
            r'√\d+',                 # Raízes
            r'\d+\s*×\s*\d+',        # Multiplicação
            r'\d+\s*÷\s*\d+'         # Divisão
        ]

        for pattern in math_patterns:
            if re.search(pattern, response):
                return True

        return False

    def format_validated_response(self, validation_result: Dict, original_response: str) -> str:
        """
        Formata a resposta validada para o usuário.

        Args:
            validation_result: Resultado da validação
            original_response: Resposta original do LLM

        Returns:
            Resposta formatada
        """
        if not validation_result['is_valid']:
            return f"Erro na validação: {validation_result['error']}"

        # Extrai a explicação da resposta original
        explanation = self._extract_explanation(original_response)

        # Extrai justificativas das alternativas incorretas
        incorrect_justification = self._extract_incorrect_alternatives(original_response)

        # Determina a fonte do conhecimento
        knowledge_source = self._get_knowledge_source_description(validation_result['response_type'])

        formatted_response = f"**Resposta correta: {validation_result['selected_letter']}) {validation_result['selected_text']}**\n\n"

        if explanation:
            formatted_response += f"Explicação: {explanation}\n\n"

        formatted_response += f"Confiança: {validation_result['confidence']}% (baseado em: {knowledge_source})\n\n"

        if incorrect_justification:
            formatted_response += f"Alternativas incorretas: {incorrect_justification}"

        return formatted_response

    def _extract_explanation(self, response: str) -> str:
        """Extrai a explicação da resposta."""
        # Procura por explicação após "Explicação:"
        pattern = r'Explicação:\s*(.+?)(?:\n\n|Confiança:|Alternativas incorretas:|$)'
        match = re.search(pattern, response, re.IGNORECASE | re.DOTALL)
        if match:
            return match.group(1).strip()

        # Se não encontrou, procura por texto após a resposta correta
        pattern = r'Resposta correta:.*?\n\n(.+?)(?:\n\n|Confiança:|$)'
        match = re.search(pattern, response, re.IGNORECASE | re.DOTALL)
        if match:
            return match.group(1).strip()

        return ""

    def _extract_incorrect_alternatives(self, response: str) -> str:
        """Extrai as justificativas das alternativas incorretas."""
        pattern = r'Alternativas incorretas:\s*(.+?)$'
        match = re.search(pattern, response, re.IGNORECASE | re.DOTALL)
        if match:
            return match.group(1).strip()
        return ""

    def _get_knowledge_source_description(self, response_type: str) -> str:
        """Retorna a descrição da fonte do conhecimento."""
        descriptions = {
            'factual': 'conhecimento factual estabelecido',
            'calculation': 'aplicação de fórmulas/leis',
            'inference': 'inferência lógica fundamentada',
            'estimation': 'estimativa baseada em padrões'
        }
        return descriptions.get(response_type, 'análise geral')

# Para testes
if __name__ == "__main__":
    validator = ResponseValidator()

    # Teste com uma questão de exemplo
    question = """
    Questão 8: Analise:
    I. Desviar o trânsito pesado, como de carretas e caminhões, para longe das zonas residenciais e das áreas de lazer.
    II. Fazer campanhas educativas para que os motoristas reduzam o uso da buzina.
    III. Conceder alvará de funcionamento somente para discotecas localizadas em bairros residenciais.
    IV. Permitir a circulação de carros com propaganda sonora no período entre 20 e 23 horas.

    Seriam medidas para evitar o excesso de ruído nas cidades as que estão dispostas APENAS em

    A) II e IV.
    B) I e III.
    C) I e II.
    D) I e IV.
    """

    response = "Resposta correta: C) I e II.\n\nExplicação: As medidas I e II são efetivas para reduzir ruído urbano. A medida I (desviar trânsito pesado) reduz o ruído de veículos grandes, e a medida II (campanhas educativas sobre buzina) reduz ruído desnecessário. As medidas III e IV aumentariam o ruído.\n\nConfiança: 85% (baseado em: conhecimento factual estabelecido)\n\nAlternativas incorretas: A) Inclui IV que aumenta ruído; B) Inclui III que aumenta ruído; D) Inclui IV que aumenta ruído."

    result = validator.validate_and_process_response(question, response)
    print("Resultado da validação:")
    print(result)

    formatted = validator.format_validated_response(result, response)
    print("\nResposta formatada:")
    print(formatted)
