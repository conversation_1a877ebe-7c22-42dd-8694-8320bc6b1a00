"""
Gerenciador de prompts aprimorado para melhorar a qualidade das respostas do LLM.
Implementa análise recursiva, verificação de confiança e sistema de votação interna.
Inclui sistema de validação e verificação de respostas para garantir precisão e fundamentação.
"""
import re
from .config import DEBUG
from .response_validator import ResponseValidator

class EnhancedPromptManager:
    """
    Gerencia os prompts enviados para os modelos LLM para melhorar a qualidade das respostas.
    Implementa técnicas avançadas como análise recursiva, verificação de confiança e sistema de votação.
    """

    def __init__(self):
        """Inicializa o gerenciador de prompts aprimorado."""
        self.validator = ResponseValidator()
        if DEBUG:
            print("Inicializando EnhancedPromptManager com sistema de validação...")

    # Categorias de questões e suas palavras-chave
    CATEGORIES = {
        "matemática": ["matemática", "equação", "cálculo", "geometria", "álgebra", "trigonometria",
                      "função", "derivada", "integral", "estatística", "probabilidade", "matriz",
                      "determinante", "logaritmo", "exponencial", "polinômio", "fração", "decimal",
                      "porcentagem", "juros", "aritmética", "número", "conjunto", "gráfico", "ângulo"],

        "física": ["física", "mecânica", "cinemática", "dinâmica", "energia", "força", "movimento",
                  "velocidade", "aceleração", "gravitação", "eletricidade", "magnetismo", "óptica",
                  "termodinâmica", "fluido", "pressão", "temperatura", "calor", "onda", "som", "luz",
                  "newton", "joule", "watt", "ampere", "volt", "coulomb", "resistência", "circuito"],

        "trânsito": ["detran", "cnh", "habilitação", "trânsito", "veículo", "motorista", "frenagem",
                    "parada", "reação", "seguimento", "sinalização", "velocidade", "distância", "freio",
                    "perigo", "aciona", "percorre", "desde", "momento"],

        "química": ["química", "elemento", "átomo", "molécula", "reação", "ácido", "base", "sal",
                   "orgânica", "inorgânica", "estequiometria", "mol", "solução", "concentração",
                   "ph", "oxidação", "redução", "ligação", "iônica", "covalente", "tabela periódica",
                   "hidrocarboneto", "polímero", "isótopo", "equilíbrio químico", "catalisador"],

        "biologia": ["biologia", "célula", "tecido", "órgão", "sistema", "genética", "dna", "rna",
                    "proteína", "enzima", "evolução", "ecologia", "fotossíntese", "respiração",
                    "mitose", "meiose", "hereditariedade", "mutação", "espécie", "taxonomia",
                    "reino", "filo", "classe", "ordem", "família", "gênero", "ecossistema"],

        "história": ["história", "século", "período", "era", "idade", "antiga", "medieval", "moderna",
                    "contemporânea", "guerra", "revolução", "império", "república", "monarquia",
                    "colonização", "independência", "civilização", "cultura", "sociedade", "política",
                    "economia", "religião", "arte", "literatura", "filosofia", "arqueologia"],

        "geografia": ["geografia", "continente", "país", "região", "estado", "cidade", "clima",
                     "relevo", "hidrografia", "vegetação", "população", "demografia", "economia",
                     "indústria", "agricultura", "comércio", "transporte", "urbanização", "rural",
                     "mapa", "cartografia", "latitude", "longitude", "hemisfério", "trópico", "equador"],

        "português": ["português", "gramática", "ortografia", "fonética", "morfologia", "sintaxe",
                     "semântica", "texto", "interpretação", "literatura", "linguagem", "verbo",
                     "substantivo", "adjetivo", "pronome", "artigo", "preposição", "conjunção",
                     "advérbio", "oração", "período", "sujeito", "predicado", "objeto", "complemento"],

        "inglês": ["inglês", "english", "grammar", "vocabulary", "verb", "noun", "adjective",
                  "adverb", "pronoun", "preposition", "conjunction", "tense", "present", "past",
                  "future", "continuous", "perfect", "reading", "writing", "listening", "speaking",
                  "phrasal", "idiom", "collocation", "sentence", "paragraph", "text", "translation"],

        "informática": ["informática", "computador", "software", "hardware", "programa", "sistema",
                       "operacional", "rede", "internet", "arquivo", "pasta", "memória", "processador",
                       "algoritmo", "programação", "linguagem", "código", "banco de dados", "sql",
                       "web", "site", "página", "navegador", "servidor", "cliente", "protocolo", "ip"]
    }

    # Prompts específicos para cada categoria com análise recursiva e verificação de confiança
    CATEGORY_PROMPTS = {
        "matemática": """
        Você é um professor especialista em matemática analisando uma questão de prova.

        MODO DE RACIOCÍNIO ATIVADO:
        1. Identifique o tipo específico de problema matemático (álgebra, geometria, cálculo, etc.).
        2. Analise cuidadosamente todos os dados fornecidos, incluindo fórmulas, equações e valores.
        3. Planeje uma estratégia de resolução passo a passo.
        4. Execute cada passo da resolução, mostrando claramente todos os cálculos intermediários.
        5. Verifique seus cálculos duas vezes, buscando possíveis erros.
        6. Questione seu próprio raciocínio: "Existe outra abordagem que poderia levar a um resultado diferente?"
        7. Confirme se a resposta é dimensionalmente consistente e matematicamente plausível.
        8. Avalie cada alternativa, explicando por que as incorretas não são válidas.
        """,

        "física": """
        Você é um professor especialista em física analisando uma questão de prova.

        MODO DE RACIOCÍNIO ATIVADO:
        1. Identifique o ramo específico da física envolvido (mecânica, eletromagnetismo, termodinâmica, etc.).
        2. Identifique os princípios físicos relevantes e as equações aplicáveis.
        3. Liste todas as variáveis conhecidas e desconhecidas.
        4. Converta todas as unidades para um sistema consistente (preferencialmente SI).
        5. Resolva o problema passo a passo, mostrando todos os cálculos intermediários.
        6. Verifique se a resposta é dimensionalmente consistente e fisicamente plausível.
        7. Questione seu próprio raciocínio: "Estou considerando todos os fenômenos físicos relevantes?"
        8. Avalie cada alternativa, explicando por que as incorretas violam princípios físicos.
        """,

        "trânsito": """
        Você é um instrutor especialista em legislação de trânsito analisando uma questão do DETRAN.

        ATENÇÃO ESPECIAL PARA AMBIGUIDADES:
        1. Leia CADA palavra da pergunta com extrema atenção.
        2. Para questões de distâncias, identifique exatamente o período descrito:
           - "Desde que vê o perigo" = inclui tempo de reação
           - "Aciona os freios" = início da frenagem
           - "Até parar" = fim do processo
        3. Definições importantes:
           - Distância de REAÇÃO: do momento que vê até começar a frear
           - Distância de FRENAGEM: enquanto está freando até parar
           - Distância de PARADA: reação + frenagem (total)
           - Distância de SEGUIMENTO: espaço entre veículos
        4. Considere a interpretação mais comum em provas do DETRAN.
        5. Se há ambiguidade, escolha a interpretação que engloba todo o processo descrito.
        """,

        # Outros prompts específicos para cada categoria seguem o mesmo padrão
    }

    # Prompt padrão para questões que não se encaixam em nenhuma categoria específica
    DEFAULT_PROMPT = """
    Você é um professor especialista analisando uma questão de múltipla escolha.

    MODO DE RACIOCÍNIO ATIVADO:
    1. Leia CADA palavra da pergunta com atenção máxima.
    2. Identifique termos-chave que definem exatamente o que está sendo perguntado.
    3. Analise possíveis ambiguidades na formulação da pergunta.
    4. Considere a interpretação mais comum e aceita na área específica.
    5. Avalie cada alternativa metodicamente, eliminando as incorretas.
    6. Questione seu próprio raciocínio: "Esta interpretação está correta?"
    7. Seja CONCISO e DIRETO na resposta final.
    """

    # Instruções para o formato da resposta concisa e direta
    RESPONSE_FORMAT = """
    FORMATO OBRIGATÓRIO DA RESPOSTA (CONCISO E DIRETO):

    VOCÊ DEVE INICIAR SUA RESPOSTA EXATAMENTE COM:
    "Resposta correta: [LETRA]) [TEXTO EXATO DA ALTERNATIVA]"

    Exemplo: "Resposta correta: A) A força gravitacional"

    DEPOIS, adicione uma explicação CONCISA (máximo 2-3 frases):
    - Explique o raciocínio principal
    - Mencione por que as outras alternativas estão incorretas (se relevante)
    - Seja DIRETO e CLARO

    IMPORTANTE:
    - NÃO use seções numeradas ou títulos
    - NÃO seja verboso ou repetitivo
    - PRIORIZE clareza e concisão
    - Máximo 100 palavras na explicação
    """

    # Instruções para verificação simplificada
    VERIFICATION_INSTRUCTIONS = """
    VERIFICAÇÃO FINAL:

    Antes de responder, confirme:
    1. Você leu CADA palavra da pergunta com atenção?
    2. Você considerou possíveis ambiguidades na formulação?
    3. Sua resposta corresponde EXATAMENTE a uma das alternativas?
    4. Você tem confiança na sua interpretação?

    Se houver dúvida, escolha a interpretação mais comum e aceita na área.
    """

    @staticmethod
    def detect_category(text):
        """
        Detecta a categoria da questão com base no texto.

        Args:
            text: Texto da questão

        Returns:
            Categoria detectada ou None
        """
        text = text.lower()

        # Pontuação para cada categoria
        category_scores = {category: 0 for category in EnhancedPromptManager.CATEGORIES}

        # Calcula a pontuação para cada categoria
        for category, keywords in EnhancedPromptManager.CATEGORIES.items():
            for keyword in keywords:
                if keyword in text:
                    # Aumenta a pontuação baseada no número de ocorrências e na especificidade da palavra-chave
                    occurrences = len(re.findall(r'\b' + re.escape(keyword) + r'\b', text))
                    specificity = len(keyword) / 5  # Palavras mais longas são geralmente mais específicas
                    category_scores[category] += occurrences * specificity

        # Encontra a categoria com maior pontuação
        max_score = max(category_scores.values())
        if max_score > 0:
            # Retorna a categoria com maior pontuação
            for category, score in category_scores.items():
                if score == max_score:
                    if DEBUG:
                        print(f"Categoria detectada: {category} (pontuação: {score})")
                    return category

        # Se nenhuma categoria foi detectada com confiança suficiente
        if DEBUG:
            print("Usando prompt padrão (sem categoria específica)")
        return None

    @staticmethod
    def create_enhanced_prompt(text):
        """
        Cria um prompt aprimorado com base no texto da questão.

        Args:
            text: Texto da questão

        Returns:
            Prompt aprimorado
        """
        # Detecta a categoria da questão
        category = EnhancedPromptManager.detect_category(text)

        # Seleciona o prompt específico para a categoria ou o prompt padrão
        if category and category in EnhancedPromptManager.CATEGORY_PROMPTS:
            category_prompt = EnhancedPromptManager.CATEGORY_PROMPTS[category]
            context_prefix = f"Você é um professor especialista em {category} analisando uma questão de prova."
        else:
            category_prompt = EnhancedPromptManager.DEFAULT_PROMPT
            context_prefix = "Você é um professor especialista analisando uma questão de prova."

        # Combina os prompts com as novas instruções de análise recursiva e verificação
        enhanced_prompt = f"""
        {context_prefix}

        {category_prompt}

        {EnhancedPromptManager.RESPONSE_FORMAT}

        {EnhancedPromptManager.VERIFICATION_INSTRUCTIONS}

        LEMBRE-SE:
        - Sua resposta DEVE começar com "Resposta correta: [LETRA]) [TEXTO]" sem nenhum texto antes disso.
        - Responda SEMPRE em português (PT-BR), mesmo que a questão esteja em inglês.
        - Mostre todos os passos do seu raciocínio, especialmente em cálculos.
        - Justifique por que as outras alternativas estão incorretas.
        - Use o sistema de votação interna para aumentar a confiança na sua resposta.

        Analise a seguinte questão:

        {text}

        IMPORTANTE: Comece sua resposta DIRETAMENTE com "Resposta correta: [LETRA]) [TEXTO]"
        """

        return enhanced_prompt

    def create_validated_prompt(self, text):
        """
        Cria um prompt com sistema de validação integrado.

        Args:
            text: Texto da questão

        Returns:
            Prompt com validação
        """
        # Identifica o domínio usando o validador
        domain = self.validator.identify_knowledge_domain(text)

        # Gera o prompt de validação específico para o domínio
        validation_prompt = self.validator.generate_validation_prompt(text, domain)

        # Combina com o prompt aprimorado existente
        enhanced_prompt = self.create_enhanced_prompt(text)

        # Integra a validação
        validated_prompt = f"""
        {validation_prompt}

        {enhanced_prompt}

        SISTEMA DE VALIDAÇÃO ATIVADO:
        - Verifique se sua resposta corresponde EXATAMENTE a uma das alternativas disponíveis
        - Se não corresponder, REPROCESSE a questão
        - Inclua nível de confiança baseado na fonte do conhecimento
        - Justifique por que as outras alternativas estão incorretas
        """

        return validated_prompt

    def validate_response(self, question_text, llm_response):
        """
        Valida a resposta do LLM usando o sistema de validação.

        Args:
            question_text: Texto da questão original
            llm_response: Resposta do LLM

        Returns:
            Resultado da validação
        """
        return self.validator.validate_and_process_response(question_text, llm_response)

    def format_final_response(self, validation_result, original_response):
        """
        Formata a resposta final para o usuário.

        Args:
            validation_result: Resultado da validação
            original_response: Resposta original do LLM

        Returns:
            Resposta formatada
        """
        return self.validator.format_validated_response(validation_result, original_response)
