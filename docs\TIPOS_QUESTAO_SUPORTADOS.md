# Tipos de Questão Suportados - Sistema Expandido

## 📋 Visão Geral

O sistema de detecção foi expandido para suportar **7 tipos diferentes** de questões, cada um com estratégias específicas de detecção de mudanças e configurações otimizadas.

## 🎯 Tipos de Questão Suportados

### 1. **Múltipla Escolha (Multiple Choice)**
- **Descrição**: Questões com alternativas A, B, C, D, E
- **Detecção**: Padrão regex `[A-E]\s*[\)\.]?\s*[^\n\r]+`
- **Elementos Monitorados**:
  - Texto da questão
  - Alternativas A-E
  - Seleção de resposta
- **Configuração**:
  ```python
  {
      "alternatives_threshold": 0.15,
      "text_threshold": 0.25,
      "detection_regions": ["question", "alternatives"]
  }
  ```

### 2. **Relacionar Colunas (Matching)**
- **Descrição**: Conectar itens da coluna esquerda com itens da direita
- **Detecção**: 
  - Padrão regex `(\d+[\.\)]\s*[^\n\r]+)|([IVX]+[\.\)]\s*[^\n\r]+)`
  - Análise visual de linhas de conexão
- **Elementos Monitorados**:
  - Itens da coluna esquerda
  - Itens da coluna direita
  - Linhas de conexão
  - Posições dos elementos
- **Configuração**:
  ```python
  {
      "text_threshold": 0.1,
      "visual_threshold": 0.08,
      "connection_threshold": 0.1,
      "detection_regions": ["left_column", "right_column", "connections"]
  }
  ```

### 3. **Arrastar e Soltar (Drag & Drop)**
- **Descrição**: Arrastar elementos para posições específicas
- **Detecção**: 
  - Palavras-chave: "drag", "drop", "arrastar", "soltar", "mover"
  - Análise de contornos interativos
- **Elementos Monitorados**:
  - Itens arrastáveis
  - Zonas de soltar
  - Posições dos elementos
  - Estado dos elementos
- **Configuração**:
  ```python
  {
      "text_threshold": 0.12,
      "visual_threshold": 0.1,
      "position_threshold": 0.08,
      "detection_regions": ["drag_items", "drop_zones"]
  }
  ```

### 4. **Ordenação (Ordering)**
- **Descrição**: Colocar itens em sequência correta
- **Detecção**: 
  - Palavras-chave: "order", "sequence", "ordenar", "sequência"
  - Análise de posições verticais
- **Elementos Monitorados**:
  - Itens da lista
  - Ordem/sequência
  - Posições verticais
- **Configuração**:
  ```python
  {
      "text_threshold": 0.15,
      "visual_threshold": 0.1,
      "sequence_threshold": 0.1,
      "detection_regions": ["items", "sequence_area"]
  }
  ```

### 5. **Preenchimento de Lacunas (Fill in the Blanks)**
- **Descrição**: Completar texto com palavras/frases
- **Detecção**: 
  - Padrão regex `_{3,}|__+|\[.*?\]|\(\s*\)`
  - Análise de campos retangulares
- **Elementos Monitorados**:
  - Campos de entrada
  - Texto preenchido
  - Posições dos campos
- **Configuração**:
  ```python
  {
      "text_threshold": 0.08,
      "visual_threshold": 0.06,
      "input_threshold": 0.1,
      "detection_regions": ["input_fields"]
  }
  ```

### 6. **Verdadeiro/Falso (True/False)**
- **Descrição**: Questões com opções verdadeiro/falso
- **Detecção**: 
  - Palavras-chave: "verdadeiro", "falso", "true", "false", "correto", "incorreto"
- **Elementos Monitorados**:
  - Texto da questão
  - Opções V/F
  - Seleção de resposta
- **Configuração**:
  ```python
  {
      "alternatives_threshold": 0.3,
      "text_threshold": 0.2,
      "detection_regions": ["question", "alternatives"]
  }
  ```

### 7. **Questões Abertas (Essay)**
- **Descrição**: Campos de texto livre para respostas dissertativas
- **Detecção**: 
  - Palavras-chave: "escreva", "redija", "explique", "descreva", "justifique"
- **Elementos Monitorados**:
  - Área de texto
  - Conteúdo digitado
  - Cursor/foco
- **Configuração**:
  ```python
  {
      "text_threshold": 0.05,
      "visual_threshold": 0.03,
      "content_threshold": 0.1,
      "detection_regions": ["text_area"]
  }
  ```

## 🔍 Detecção Automática de Tipo

### **Processo de Identificação**
1. **Análise de Texto (OCR)**
   - Extrai texto de toda a imagem
   - Aplica padrões regex específicos
   - Conta elementos característicos

2. **Análise Visual**
   - Detecta linhas (conexões)
   - Identifica contornos (elementos interativos)
   - Analisa formas geométricas

3. **Palavras-chave**
   - Busca indicadores específicos
   - Analisa contexto da questão
   - Identifica comandos de ação

### **Prioridade de Detecção**
1. Múltipla escolha (3+ alternativas A-E)
2. Relacionar colunas (3+ itens numerados)
3. Preenchimento de lacunas (2+ campos)
4. Drag & drop (palavras-chave)
5. Ordenação (palavras-chave)
6. Verdadeiro/falso (palavras-chave)
7. Questões abertas (palavras-chave)
8. Múltipla escolha (padrão)

## 📊 Regiões de Detecção Específicas

### **Regiões Padrão**
- **Timer**: 0%-20% altura, 70%-100% largura
- **Questão**: 20%-70% altura, 10%-90% largura
- **Alternativas**: 40%-90% altura, 10%-90% largura

### **Regiões Específicas para Matching**
- **Coluna Esquerda**: 30%-80% altura, 10%-45% largura
- **Coluna Direita**: 30%-80% altura, 55%-90% largura
- **Conexões**: 30%-80% altura, 40%-60% largura

### **Regiões para Drag & Drop**
- **Itens Arrastáveis**: 30%-60% altura, 10%-90% largura
- **Zonas de Soltar**: 60%-90% altura, 10%-90% largura

### **Regiões para Campos de Entrada**
- **Campos**: 30%-80% altura, 10%-90% largura
- **Área de Texto**: 40%-90% altura, 10%-90% largura

## ⚙️ Configurações Otimizadas por Tipo

### **Sensibilidade Alta (Matching, Fill Blanks)**
- Thresholds reduzidos para detectar mudanças sutis
- Foco em elementos interativos específicos
- Monitoramento de posições precisas

### **Sensibilidade Média (Multiple Choice, True/False)**
- Configurações balanceadas
- Foco em texto e alternativas
- Detecção de seleções

### **Sensibilidade Baixa (Essay, Ordering)**
- Thresholds maiores para evitar falsos positivos
- Foco em mudanças significativas de conteúdo
- Tolerância a pequenas variações

## 🚀 Funcionalidades Avançadas

### **Adaptação Dinâmica**
- Sistema detecta tipo automaticamente
- Ajusta configurações em tempo real
- Otimiza estratégia de monitoramento

### **Detecção Multi-Camadas**
- Cada tipo tem detectores específicos
- Fallback para detecção visual geral
- Combinação de múltiplas estratégias

### **Logs Detalhados**
```
🔄 Tipo de questão alterado: multiple_choice -> matching
Tipo detectado: matching (análise visual - 8 linhas)
Hash matching: a1b2c3d4... (3 conexões)
🔄 MUDANÇAS DETECTADAS:
  • Elementos interativos alterados (matching)
```

## 📈 Resultados Esperados

### **Precisão por Tipo**
- **Multiple Choice**: 95%+ (padrão bem definido)
- **Matching**: 90%+ (análise visual robusta)
- **Drag & Drop**: 85%+ (elementos dinâmicos)
- **Fill Blanks**: 90%+ (campos bem definidos)
- **True/False**: 95%+ (padrão simples)
- **Ordering**: 85%+ (posições variáveis)
- **Essay**: 80%+ (conteúdo livre)

### **Tempo de Detecção**
- **Identificação de tipo**: <1 segundo
- **Detecção de mudança**: 2-5 segundos
- **Processamento total**: <30 segundos

## 🔧 Personalização e Ajustes

### **Configuração por Tipo**
```python
from src.core.monitor_config import QUESTION_TYPE_CONFIGS

# Ajustar configuração específica
config = QUESTION_TYPE_CONFIGS["matching"]
config["connection_threshold"] = 0.05  # Mais sensível

# Aplicar configuração
monitor.configure_for_question_type("matching", config)
```

### **Regiões Customizadas**
```python
# Ajustar região para interface específica
MonitorConfig.LEFT_COLUMN_REGION = {
    'top': 0.25,    # Ajuste conforme necessário
    'bottom': 0.75,
    'left': 0.05,
    'right': 0.4
}
```

### **Novos Tipos de Questão**
Para adicionar suporte a novos tipos:

1. **Adicionar padrões** em `monitor_config.py`
2. **Criar detector** em `question_type_detectors.py`
3. **Configurar regiões** específicas
4. **Testar e ajustar** thresholds

## 🎯 Casos de Uso Específicos

### **Provas de Idiomas**
- Matching para vocabulário
- Fill blanks para gramática
- Essay para redação

### **Provas de Matemática**
- Multiple choice para cálculos
- Drag & drop para fórmulas
- Ordering para sequências

### **Provas de Ciências**
- Matching para classificações
- Fill blanks para conceitos
- Essay para explicações

### **Provas Técnicas**
- Drag & drop para diagramas
- Ordering para processos
- Multiple choice para conceitos

## 🎉 Conclusão

O sistema expandido oferece **suporte completo** para todos os tipos principais de questão encontrados em provas online, com:

- **Detecção automática** de tipo
- **Configurações otimizadas** por tipo
- **Estratégias específicas** de monitoramento
- **Alta precisão** e confiabilidade
- **Flexibilidade** para customização

**Resultado**: Sistema universal que funciona com qualquer tipo de interface de prova! 🚀
