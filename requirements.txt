# Python Question Helper - Core Requirements
# Version: 1.0.0
# Author: BoZolinO

# =============================================================================
# CORE DEPENDENCIES (Required for basic functionality)
# =============================================================================

# Image processing and computer vision
pillow>=9.5.0,<11.0.0
opencv-python>=4.8.0,<5.0.0
numpy>=1.24.3,<2.0.0

# OCR (Optical Character Recognition)
pytesseract>=0.3.10,<1.0.0

# GUI Framework
PyQt5>=5.15.9,<6.0.0

# Screen capture and automation
pyautogui>=0.9.54,<1.0.0
pynput>=1.7.6,<2.0.0
mss>=9.0.1,<10.0.0

# HTTP requests and API communication
requests>=2.31.0,<3.0.0

# Environment and configuration
python-dotenv>=1.0.0,<2.0.0

# LLM API clients
openai>=1.12.0,<2.0.0
google-generativeai>=0.3.2,<1.0.0
huggingface-hub>=0.20.3,<1.0.0

# Platform-specific dependencies
pywin32>=306; sys_platform == "win32"

# =============================================================================
# OPTIONAL DEPENDENCIES (Install separately if needed)
# =============================================================================

# For advanced analytics and reporting:
# pandas>=1.5.0,<3.0.0
# matplotlib>=3.6.0,<4.0.0
# seaborn>=0.12.0,<1.0.0

# For web-based advertisements:
# PyQtWebEngine>=5.15.6,<6.0.0

# For development and testing:
# pytest>=7.0.0,<8.0.0
# pytest-cov>=4.0.0,<5.0.0
# black>=22.0.0,<24.0.0
# flake8>=5.0.0,<7.0.0
# mypy>=1.0.0,<2.0.0

# For building executables:
# pyinstaller>=5.0.0,<7.0.0
# cx-freeze>=6.0.0,<7.0.0

# =============================================================================
# INSTALLATION COMMANDS
# =============================================================================

# Basic installation:
# pip install -r requirements.txt

# With analytics features:
# pip install -r requirements.txt pandas matplotlib seaborn

# With web features:
# pip install -r requirements.txt PyQtWebEngine

# Development installation:
# pip install -r requirements.txt pytest pytest-cov black flake8 mypy

# Full installation (all features):
# pip install -r requirements.txt pandas matplotlib seaborn PyQtWebEngine
