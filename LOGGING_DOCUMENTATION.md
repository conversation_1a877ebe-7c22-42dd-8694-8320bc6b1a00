# Sistema de Logging e Análise - Python Question Helper

## 📋 Visão Geral

O sistema de logging e análise foi desenvolvido para coletar dados detalhados sobre questões processadas, identificar padrões de erro e melhorar a precisão das respostas do LLM, reduzindo "alucinações" e aumentando a taxa de acerto.

## 🎯 Objetivos Principais

- **Coleta Estruturada**: Registrar cada questão processada com metadados completos
- **Análise de Padrões**: Identificar tendências e problemas recorrentes
- **Validação Manual**: Sistema para revisar e marcar respostas como corretas/incorretas
- **Insights Automáticos**: Gerar recomendações para melhorar o sistema
- **Métricas de Qualidade**: Distinguir entre "não sei" (aceitável) e "chute" (inaceitável)

## 🏗️ Arquitetura do Sistema

### Componentes Principais

1. **`question_logger.py`** - Sistema de logging estruturado
2. **`analytics_module.py`** - Análise de padrões e geração de relatórios
3. **`review_interface.py`** - Interface gráfica para validação manual
4. **`test_logging_system.py`** - Testes e validação do sistema

### Banco de Dados (SQLite)

#### Tabela `questions`
```sql
- id: INTEGER PRIMARY KEY
- session_id: TEXT (ID da sessão)
- timestamp: DATETIME (momento do processamento)
- question_hash: TEXT UNIQUE (hash único da questão)
- question_number: INTEGER (número da questão detectado)
- domain: TEXT (domínio categorizado automaticamente)
- question_type: TEXT (tipo: factual, cálculo, interpretação, etc.)
- has_images: BOOLEAN (presença de imagens/diagramas)
- ocr_text: TEXT (texto extraído via OCR)
- ocr_confidence: REAL (confiança do OCR 0-100)
- llm_provider: TEXT (provedor LLM utilizado)
- llm_model: TEXT (modelo LLM específico)
- llm_response: TEXT (resposta completa do LLM)
- llm_confidence: INTEGER (nível de confiança 0-100)
- selected_answer: TEXT (letra da alternativa selecionada)
- processing_time: REAL (tempo de processamento em segundos)
- image_path: TEXT (caminho da imagem salva)
- metadata: TEXT (metadados adicionais em JSON)
- is_validated: BOOLEAN (se foi validada manualmente)
- is_correct: BOOLEAN (se a resposta está correta)
- error_type: TEXT (tipo de erro se incorreta)
- reviewer_notes: TEXT (notas do revisor)
```

## 🔧 Funcionalidades Implementadas

### 1. Sistema de Logging Estruturado

#### Categorização Automática por Domínio
- **Legislação**: leis, artigos, CTB, constituição
- **Sinalização**: placas, sinais, semáforos
- **Direção Defensiva**: segurança, prevenção
- **Primeiros Socorros**: socorro, ferimentos, acidentes
- **Mecânica**: motor, freios, manutenção
- **Meio Ambiente**: poluição, emissões
- **Infrações**: multas, penalidades, pontos
- **Distâncias**: cálculos de frenagem, seguimento

#### Detecção de Tipo de Questão
- **Cálculo**: questões com números, velocidades, distâncias
- **Interpretação**: situações, casos, cenários
- **Factual**: conhecimento de leis e normas
- **Aplicação**: procedimentos práticos

#### Extração de Confiança
O sistema extrai automaticamente o nível de confiança das respostas usando:
- Padrões explícitos: "Confiança: 85%"
- Indicadores linguísticos: "certamente", "provavelmente", "talvez"
- Estrutura da resposta: presença de "Resposta correta:"
- Heurísticas: tamanho e detalhamento da resposta

### 2. Sistema de Validação Manual

#### Interface de Revisão (`review_interface.py`)
- **Lista de Questões**: Filtros por domínio e confiança
- **Detalhes Completos**: OCR, resposta LLM, imagem original
- **Validação Simples**: Botões "Correto" / "Incorreto"
- **Classificação de Erros**: Tipos específicos de erro
- **Notas do Revisor**: Comentários detalhados

#### Tipos de Erro Suportados
- **Alucinação**: Informação inventada ou incorreta
- **Interpretação incorreta**: Mal entendimento do contexto
- **Conhecimento desatualizado**: Informação obsoleta
- **Erro de cálculo**: Problemas matemáticos
- **Resposta incompleta**: Falta de informações importantes

### 3. Análise de Padrões

#### Métricas de Qualidade
- **Precisão Geral**: Taxa de acerto global
- **Precisão por Domínio**: Performance específica por área
- **Precisão por Provedor**: Comparação entre LLMs
- **Correlação Confiança-Precisão**: Validação da calibração
- **Taxa de Alucinação**: Respostas incorretas com alta confiança

#### Detecção de Alucinação
- **Alta confiança + erro**: Respostas erradas com >80% confiança
- **Números específicos**: Possível invenção de dados
- **Explicações detalhadas**: Excesso de detalhes suspeitos
- **Informações contraditórias**: Inconsistências internas

#### Análise Temporal
- **Tendências**: Melhoria ou piora ao longo do tempo
- **Padrões diários**: Performance por período
- **Evolução por domínio**: Progresso específico por área

### 4. Geração de Insights

#### Recomendações Automáticas
- **Precisão baixa**: Sugestões para melhorar prompts
- **Alta alucinação**: Implementar validação adicional
- **Domínios problemáticos**: Prompts especializados
- **Confiança descalibrada**: Ajustar thresholds

#### Relatórios Visuais
- **Gráficos de precisão**: Por domínio e provedor
- **Distribuição de confiança**: Análise de calibração
- **Tendências temporais**: Evolução da performance
- **Análise de erros**: Padrões de falhas

## 🚀 Como Usar

### 1. Integração Automática
O sistema está integrado ao `main.py` e registra automaticamente:
- Todas as questões processadas (manual e automático)
- Metadados completos (tempo, provedor, modelo, etc.)
- Imagens das questões (organizadas por data)

### 2. Revisão Manual
```bash
python review_interface.py
```
- Interface gráfica para validar respostas
- Filtros por domínio e confiança
- Navegação simples entre questões
- Exportação de dados

### 3. Análise de Dados
```python
from analytics_module import AnalyticsModule

analytics = AnalyticsModule()
report = analytics.generate_comprehensive_report(days_back=30)
analytics.generate_visual_report("output_directory")
```

### 4. Exportação
```python
from question_logger import QuestionLogger

logger = QuestionLogger()
logger.export_data("dados.csv", "csv")
logger.export_data("dados.json", "json")
```

## 📊 Métricas e KPIs

### Métricas Principais
- **Taxa de Acerto Global**: % de respostas corretas validadas
- **Taxa de Validação**: % de questões revisadas manualmente
- **Tempo Médio de Processamento**: Performance do sistema
- **Score de Qualidade**: Métrica composta (0-100)

### Métricas por Categoria
- **Precisão por Domínio**: Identificar áreas problemáticas
- **Precisão por Provedor**: Comparar performance dos LLMs
- **Precisão por Tipo**: Factual vs. cálculo vs. interpretação

### Métricas de Confiança
- **Correlação Confiança-Precisão**: Calibração do sistema
- **Taxa de Alucinação**: Erros com alta confiança
- **Distribuição de Confiança**: Padrões de incerteza

## 🔍 Critérios de Qualidade

### Distinção "Não Sei" vs "Chute"
- **"Não Sei" (Aceitável)**: Baixa confiança + resposta honesta
- **"Chute" (Inaceitável)**: Alta confiança + resposta incorreta

### Flags de Alucinação
- Confiança >80% + resposta incorreta
- Números específicos não presentes no texto
- Explicações excessivamente detalhadas
- Contradições internas na resposta

### Priorização
- **Precisão > Velocidade**: Melhor resposta correta que rápida
- **Honestidade > Confiança**: Preferir "não sei" a chute
- **Validação > Automação**: Revisar respostas suspeitas

## 🛠️ Manutenção e Backup

### Limpeza Automática
```python
logger.cleanup_old_data(days_to_keep=30)
```

### Backup de Dados
```python
logger.export_data("backup_YYYYMMDD.json", "json")
```

### Monitoramento
- Verificar taxa de validação semanal
- Analisar tendências de precisão
- Revisar recomendações do sistema
- Atualizar prompts baseado em insights

## 📈 Roadmap de Melhorias

### Curto Prazo
- [ ] Dashboard web para visualização
- [ ] Alertas automáticos para problemas
- [ ] Integração com sistema de feedback

### Médio Prazo
- [ ] Machine learning para detecção de alucinação
- [ ] Análise semântica avançada
- [ ] Sistema de recomendação de prompts

### Longo Prazo
- [ ] Integração com múltiplos sistemas
- [ ] API para análise externa
- [ ] Sistema de benchmark automático

## 🔧 Troubleshooting

### Problemas Comuns
1. **Banco não criado**: Verificar permissões de escrita
2. **Interface não abre**: Instalar PyQt5
3. **Gráficos não geram**: Instalar matplotlib
4. **Logging não funciona**: Verificar imports

### Logs de Debug
Ativar `DEBUG = True` em `config.py` para logs detalhados.

---

**Desenvolvido para o Python Question Helper**  
*Sistema de análise e melhoria contínua de respostas LLM*
