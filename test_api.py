"""
Teste de conexão com a API do OpenRouter.
"""
import os
import base64
import json
import requests
from openai import OpenAI
from dotenv import load_dotenv

# <PERSON>ega as variáveis de ambiente
load_dotenv()

# Configurações da API
API_KEY = os.getenv("LLM_API_KEY")
MODEL = os.getenv("LLM_MODEL", "google/gemini-2.0-flash-exp:free")
BASE_URL = os.getenv("LLM_BASE_URL", "https://openrouter.ai/api/v1")
SITE_URL = os.getenv("LLM_SITE_URL", "http://localhost")
SITE_NAME = os.getenv("LLM_SITE_NAME", "Python Question Helper")

def test_api_connection():
    """Testa a conexão com a API do OpenRouter."""
    print(f"Testando conexão com a API...")
    print(f"API Key: {API_KEY[:5]}...{API_KEY[-5:]}")
    print(f"Modelo: {MODEL}")
    print(f"Base URL: {BASE_URL}")
    
    try:
        # Testa a conexão com a API
        url = f"{BASE_URL}/models"
        headers = {
            "Authorization": f"Bearer {API_KEY}"
        }
        
        response = requests.get(url, headers=headers)
        
        if response.status_code == 200:
            print(f"Conexão bem-sucedida! Status: {response.status_code}")
            data = response.json()
            
            # Verifica se o modelo está disponível
            available_models = [model["id"] for model in data.get("data", [])]
            if MODEL in available_models:
                print(f"Modelo '{MODEL}' está disponível!")
            else:
                print(f"Modelo '{MODEL}' NÃO está disponível!")
                print("Modelos disponíveis:")
                for model in data.get("data", []):
                    if ":free" in model["id"]:
                        print(f"  - {model['id']} ({model['name']})")
            
            return True
        else:
            print(f"Erro na conexão! Status: {response.status_code}")
            print(f"Resposta: {response.text}")
            return False
            
    except Exception as e:
        print(f"Erro ao testar conexão: {str(e)}")
        return False

def test_image_processing():
    """Testa o processamento de imagem com a API."""
    print(f"\nTestando processamento de imagem...")
    
    # Verifica se existe uma imagem para teste
    image_path = "current_capture.png"
    if not os.path.exists(image_path):
        print(f"Imagem de teste não encontrada: {image_path}")
        return False
        
    try:
        # Codifica a imagem para base64
        with open(image_path, "rb") as image_file:
            base64_image = base64.b64encode(image_file.read()).decode('utf-8')
            
        print(f"Imagem codificada com sucesso. Tamanho: {len(base64_image)} caracteres")
        
        # Inicializa o cliente OpenAI
        client = OpenAI(
            base_url=BASE_URL,
            api_key=API_KEY
        )
        
        # Prepara o prompt
        prompt = "Descreva o que você vê nesta imagem em poucas palavras."
        
        # Chama a API
        print(f"Enviando requisição para a API com o modelo {MODEL}...")
        
        response = client.chat.completions.create(
            extra_headers={
                "HTTP-Referer": SITE_URL,
                "X-Title": SITE_NAME,
            },
            model=MODEL,
            messages=[
                {
                    "role": "system",
                    "content": "Você é um assistente que descreve imagens de forma concisa."
                },
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": prompt},
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/jpeg;base64,{base64_image}",
                                "detail": "high"
                            }
                        }
                    ]
                }
            ],
            max_tokens=100,
            temperature=0.1,
            timeout=30
        )
        
        # Verifica a resposta
        if response and hasattr(response, 'choices') and len(response.choices) > 0:
            answer = response.choices[0].message.content.strip()
            print(f"Resposta recebida com sucesso!")
            print(f"Resposta: {answer}")
            return True
        else:
            print(f"Resposta inválida ou vazia!")
            print(f"Resposta completa: {response}")
            return False
            
    except Exception as e:
        print(f"Erro ao processar imagem: {str(e)}")
        return False

def test_alternative_model():
    """Testa um modelo alternativo."""
    global MODEL
    
    # Lista de modelos alternativos para testar
    alternative_models = [
        "google/gemini-2.0-flash-exp:free",
        "meta-llama/llama-3.2-11b-vision-instruct:free",
        "google/gemini-2.5-pro-exp-03-25",
        "qwen/qwen2.5-vl-72b-instruct:free"
    ]
    
    for alt_model in alternative_models:
        if alt_model != MODEL:
            print(f"\nTestando modelo alternativo: {alt_model}")
            old_model = MODEL
            MODEL = alt_model
            
            success = test_image_processing()
            
            if success:
                print(f"Modelo alternativo {alt_model} funcionou com sucesso!")
                print(f"Recomendação: Atualize o arquivo .env para usar este modelo.")
                return alt_model
            else:
                print(f"Modelo alternativo {alt_model} também falhou.")
                
            # Restaura o modelo original
            MODEL = old_model
    
    return None

if __name__ == "__main__":
    print("=== TESTE DE API DO OPENROUTER ===")
    
    # Testa a conexão com a API
    connection_ok = test_api_connection()
    
    if connection_ok:
        # Testa o processamento de imagem
        image_ok = test_image_processing()
        
        if not image_ok:
            print("\nO modelo atual falhou no processamento de imagem.")
            print("Testando modelos alternativos...")
            
            working_model = test_alternative_model()
            
            if working_model:
                print(f"\n=== RECOMENDAÇÃO ===")
                print(f"Atualize o arquivo .env para usar o modelo: {working_model}")
                print(f"LLM_MODEL={working_model}")
            else:
                print("\nTodos os modelos testados falharam.")
                print("Verifique sua conexão com a internet e a validade da API key.")
    
    print("\n=== TESTE CONCLUÍDO ===")
