"""
Configuration settings for the Python Question Helper application.
"""
import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Tesseract configuration
# Caminhos comuns para o Tesseract OCR
# Tente um destes caminhos ou atualize para o caminho correto da sua instalação
TESSERACT_PATHS = [
    r"C:\Program Files\Tesseract-OCR\tesseract.exe",  # Caminho padrão 64-bit
    r"C:\Program Files (x86)\Tesseract-OCR\tesseract.exe",  # Caminho padrão 32-bit
    r"C:\Users\<USER>\Tesseract-OCR\tesseract.exe",  # Caminho alternativo
    r"tesseract",  # Usar o PATH do sistema
]

# O código tentará cada um desses caminhos
# Você pode definir manualmente o caminho correto aqui:
TESSERACT_PATH = r"C:\Users\<USER>\AppData\Local\Programs\Tesseract-OCR\tesseract.exe"  # Caminho atualizado com o executável

# LLM API configuration
LLM_API_KEY = os.getenv("LLM_API_KEY")
LLM_MODEL = os.getenv("LLM_MODEL", "google/gemini-2.0-flash-exp:free")  # Modelo gratuito com suporte a visão
LLM_BASE_URL = os.getenv("LLM_BASE_URL", "https://openrouter.ai/api/v1")
LLM_SITE_URL = os.getenv("LLM_SITE_URL", "http://localhost")
LLM_SITE_NAME = os.getenv("LLM_SITE_NAME", "Python Question Helper")

# Configuração de provedores alternativos
LLM_PROVIDER = os.getenv("LLM_PROVIDER", "huggingface")  # Opções: openrouter, ollama, gemini, huggingface
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")
HUGGINGFACE_API_KEY = os.getenv("HUGGINGFACE_API_KEY")
OLLAMA_BASE_URL = os.getenv("OLLAMA_BASE_URL", "http://localhost:11434/api")

# UI configuration
OVERLAY_OPACITY = 0.85
OVERLAY_WIDTH = 350  # Reduced width
OVERLAY_HEIGHT = 400  # Reduced height
OVERLAY_POSITION_X = 10  # Distance from right edge of screen
OVERLAY_POSITION_Y = 10  # Distance from top edge of screen

# Screen capture configuration
CAPTURE_INTERVAL = 2.0  # Seconds between captures
BROWSER_TITLE_CONTAINS = "Question"  # Partial window title to identify the browser window

# Debug mode (set to True to help diagnose OCR issues)
DEBUG = os.getenv("DEBUG", "True").lower() == "true"

# Configurações de anúncios
ADS_ENABLED = os.getenv("ADS_ENABLED", "False").lower() == "true"
ADS_CLIENT_ID = os.getenv("ADS_CLIENT_ID", "ca-pub-6285721618252255")  # ID do cliente AdSense
ADS_BANNER_SLOT = os.getenv("ADS_BANNER_SLOT", "5914752176")  # Slot para anúncios de display
ADS_RECTANGLE_SLOT = os.getenv("ADS_RECTANGLE_SLOT", "5914752176")  # Slot para anúncios retangulares
ADS_MULTIPLEX_SLOT = os.getenv("ADS_MULTIPLEX_SLOT", "5914752176")  # Slot para anúncios multiplex (autorelaxed)
