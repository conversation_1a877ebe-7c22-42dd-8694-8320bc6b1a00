"""
Gerenciador de prompts aprimorado para melhorar a qualidade das respostas do LLM.
Implementa análise recursiva, verificação de confiança e sistema de votação interna.
"""
import re
from config import DEBUG

class EnhancedPromptManager:
    """
    Gerencia os prompts enviados para os modelos LLM para melhorar a qualidade das respostas.
    Implementa técnicas avançadas como análise recursiva, verificação de confiança e sistema de votação.
    """

    # Categorias de questões e suas palavras-chave
    CATEGORIES = {
        "matemática": ["matemática", "equação", "cálculo", "geometria", "álgebra", "trigonometria", 
                      "função", "derivada", "integral", "estatística", "probabilidade", "matriz", 
                      "determinante", "logaritmo", "exponencial", "polinômio", "fração", "decimal",
                      "porcentagem", "juros", "aritmética", "número", "conjunto", "gráfico", "ângulo"],
        
        "física": ["física", "mecânica", "cinemática", "dinâmica", "energia", "força", "movimento", 
                  "velocidade", "aceleração", "gravitação", "eletricidade", "magnetismo", "óptica", 
                  "termodinâmica", "fluido", "pressão", "temperatura", "calor", "onda", "som", "luz",
                  "newton", "joule", "watt", "ampere", "volt", "coulomb", "resistência", "circuito"],
        
        "química": ["química", "elemento", "átomo", "molécula", "reação", "ácido", "base", "sal", 
                   "orgânica", "inorgânica", "estequiometria", "mol", "solução", "concentração", 
                   "ph", "oxidação", "redução", "ligação", "iônica", "covalente", "tabela periódica",
                   "hidrocarboneto", "polímero", "isótopo", "equilíbrio químico", "catalisador"],
        
        "biologia": ["biologia", "célula", "tecido", "órgão", "sistema", "genética", "dna", "rna", 
                    "proteína", "enzima", "evolução", "ecologia", "fotossíntese", "respiração", 
                    "mitose", "meiose", "hereditariedade", "mutação", "espécie", "taxonomia", 
                    "reino", "filo", "classe", "ordem", "família", "gênero", "ecossistema"],
        
        "história": ["história", "século", "período", "era", "idade", "antiga", "medieval", "moderna", 
                    "contemporânea", "guerra", "revolução", "império", "república", "monarquia", 
                    "colonização", "independência", "civilização", "cultura", "sociedade", "política",
                    "economia", "religião", "arte", "literatura", "filosofia", "arqueologia"],
        
        "geografia": ["geografia", "continente", "país", "região", "estado", "cidade", "clima", 
                     "relevo", "hidrografia", "vegetação", "população", "demografia", "economia", 
                     "indústria", "agricultura", "comércio", "transporte", "urbanização", "rural",
                     "mapa", "cartografia", "latitude", "longitude", "hemisfério", "trópico", "equador"],
        
        "português": ["português", "gramática", "ortografia", "fonética", "morfologia", "sintaxe", 
                     "semântica", "texto", "interpretação", "literatura", "linguagem", "verbo", 
                     "substantivo", "adjetivo", "pronome", "artigo", "preposição", "conjunção", 
                     "advérbio", "oração", "período", "sujeito", "predicado", "objeto", "complemento"],
        
        "inglês": ["inglês", "english", "grammar", "vocabulary", "verb", "noun", "adjective", 
                  "adverb", "pronoun", "preposition", "conjunction", "tense", "present", "past", 
                  "future", "continuous", "perfect", "reading", "writing", "listening", "speaking",
                  "phrasal", "idiom", "collocation", "sentence", "paragraph", "text", "translation"],
        
        "informática": ["informática", "computador", "software", "hardware", "programa", "sistema", 
                       "operacional", "rede", "internet", "arquivo", "pasta", "memória", "processador", 
                       "algoritmo", "programação", "linguagem", "código", "banco de dados", "sql", 
                       "web", "site", "página", "navegador", "servidor", "cliente", "protocolo", "ip"]
    }

    # Prompts específicos para cada categoria com análise recursiva e verificação de confiança
    CATEGORY_PROMPTS = {
        "matemática": """
        Você é um professor especialista em matemática analisando uma questão de prova.
        
        MODO DE RACIOCÍNIO ATIVADO:
        1. Identifique o tipo específico de problema matemático (álgebra, geometria, cálculo, etc.).
        2. Analise cuidadosamente todos os dados fornecidos, incluindo fórmulas, equações e valores.
        3. Planeje uma estratégia de resolução passo a passo.
        4. Execute cada passo da resolução, mostrando claramente todos os cálculos intermediários.
        5. Verifique seus cálculos duas vezes, buscando possíveis erros.
        6. Questione seu próprio raciocínio: "Existe outra abordagem que poderia levar a um resultado diferente?"
        7. Confirme se a resposta é dimensionalmente consistente e matematicamente plausível.
        8. Avalie cada alternativa, explicando por que as incorretas não são válidas.
        """,

        "física": """
        Você é um professor especialista em física analisando uma questão de prova.
        
        MODO DE RACIOCÍNIO ATIVADO:
        1. Identifique o ramo específico da física envolvido (mecânica, eletromagnetismo, termodinâmica, etc.).
        2. Identifique os princípios físicos relevantes e as equações aplicáveis.
        3. Liste todas as variáveis conhecidas e desconhecidas.
        4. Converta todas as unidades para um sistema consistente (preferencialmente SI).
        5. Resolva o problema passo a passo, mostrando todos os cálculos intermediários.
        6. Verifique se a resposta é dimensionalmente consistente e fisicamente plausível.
        7. Questione seu próprio raciocínio: "Estou considerando todos os fenômenos físicos relevantes?"
        8. Avalie cada alternativa, explicando por que as incorretas violam princípios físicos.
        """,

        # Outros prompts específicos para cada categoria seguem o mesmo padrão
    }

    # Prompt padrão para questões que não se encaixam em nenhuma categoria específica
    DEFAULT_PROMPT = """
    Você é um professor especialista analisando uma questão de múltipla escolha.
    
    MODO DE RACIOCÍNIO ATIVADO:
    1. Identifique o assunto específico da questão.
    2. Analise cuidadosamente todos os detalhes e informações fornecidas.
    3. Considere o que a questão está realmente perguntando.
    4. Desenvolva um raciocínio passo a passo para chegar à resposta.
    5. Avalie cada alternativa metodicamente, eliminando as incorretas.
    6. Questione seu próprio raciocínio: "Existe alguma interpretação alternativa que levaria a outra resposta?"
    7. Verifique sua resposta duas vezes antes de fornecer a conclusão final.
    8. Explique por que cada alternativa incorreta deve ser descartada.
    """

    # Instruções para o formato da resposta com análise recursiva
    RESPONSE_FORMAT = """
    FORMATO OBRIGATÓRIO DA RESPOSTA:

    1. ANÁLISE INICIAL (raciocínio interno):
       - Identifique o assunto específico: [ASSUNTO]
       - Analise os dados e informações fornecidas
       - Desenvolva seu raciocínio passo a passo
       - Para cálculos, mostre TODOS os passos intermediários

    2. PRIMEIRA CONCLUSÃO:
       - Indique qual alternativa parece correta e por quê

    3. VERIFICAÇÃO DE CONFIANÇA:
       - Questione sua conclusão inicial
       - Tente resolver o problema usando uma abordagem diferente
       - Verifique possíveis erros ou interpretações alternativas

    4. ANÁLISE DAS ALTERNATIVAS:
       - Para cada alternativa incorreta, explique ESPECIFICAMENTE por que ela está errada

    5. CONCLUSÃO FINAL:
       - Confirme sua resposta final com nível de confiança

    VOCÊ DEVE INICIAR SUA RESPOSTA FINAL EXATAMENTE COM ESTA FRASE:
    "Resposta correta: [LETRA]) [TEXTO DA ALTERNATIVA]"

    Exemplo exato: "Resposta correta: A) A força gravitacional"

    Esta frase DEVE ser a primeira linha da sua resposta final, sem nenhum texto antes dela.
    """

    # Instruções para verificação adicional e sistema de votação interna
    VERIFICATION_INSTRUCTIONS = """
    SISTEMA DE VERIFICAÇÃO AVANÇADA:

    1. Gere internamente três análises independentes do problema.
    2. Compare as conclusões das três análises.
    3. Se todas chegarem à mesma resposta, seu nível de confiança é alto.
    4. Se houver divergência, reavalie cuidadosamente cada abordagem.
    5. Escolha a resposta com base na análise mais rigorosa e fundamentada.

    Antes de finalizar, verifique:
    - Você considerou TODAS as informações da questão?
    - Você analisou TODAS as alternativas?
    - Você tem CERTEZA ABSOLUTA da resposta? Se não, indique seu nível de confiança.
    - Existe alguma ambiguidade ou exceção que poderia afetar a resposta?

    Se você não tiver 100% de certeza, indique claramente qual é a alternativa mais provável e por quê.
    """

    @staticmethod
    def detect_category(text):
        """
        Detecta a categoria da questão com base no texto.

        Args:
            text: Texto da questão

        Returns:
            Categoria detectada ou None
        """
        text = text.lower()

        # Pontuação para cada categoria
        category_scores = {category: 0 for category in EnhancedPromptManager.CATEGORIES}

        # Calcula a pontuação para cada categoria
        for category, keywords in EnhancedPromptManager.CATEGORIES.items():
            for keyword in keywords:
                if keyword in text:
                    # Aumenta a pontuação baseada no número de ocorrências e na especificidade da palavra-chave
                    occurrences = len(re.findall(r'\b' + re.escape(keyword) + r'\b', text))
                    specificity = len(keyword) / 5  # Palavras mais longas são geralmente mais específicas
                    category_scores[category] += occurrences * specificity

        # Encontra a categoria com maior pontuação
        max_score = max(category_scores.values())
        if max_score > 0:
            # Retorna a categoria com maior pontuação
            for category, score in category_scores.items():
                if score == max_score:
                    if DEBUG:
                        print(f"Categoria detectada: {category} (pontuação: {score})")
                    return category

        # Se nenhuma categoria foi detectada com confiança suficiente
        if DEBUG:
            print("Usando prompt padrão (sem categoria específica)")
        return None

    @staticmethod
    def create_enhanced_prompt(text):
        """
        Cria um prompt aprimorado com base no texto da questão.

        Args:
            text: Texto da questão

        Returns:
            Prompt aprimorado
        """
        # Detecta a categoria da questão
        category = EnhancedPromptManager.detect_category(text)

        # Seleciona o prompt específico para a categoria ou o prompt padrão
        if category and category in EnhancedPromptManager.CATEGORY_PROMPTS:
            category_prompt = EnhancedPromptManager.CATEGORY_PROMPTS[category]
            context_prefix = f"Você é um professor especialista em {category} analisando uma questão de prova."
        else:
            category_prompt = EnhancedPromptManager.DEFAULT_PROMPT
            context_prefix = "Você é um professor especialista analisando uma questão de prova."

        # Combina os prompts com as novas instruções de análise recursiva e verificação
        enhanced_prompt = f"""
        {context_prefix}

        {category_prompt}

        {EnhancedPromptManager.RESPONSE_FORMAT}

        {EnhancedPromptManager.VERIFICATION_INSTRUCTIONS}

        LEMBRE-SE: 
        - Sua resposta DEVE começar com "Resposta correta: [LETRA]) [TEXTO]" sem nenhum texto antes disso.
        - Responda SEMPRE em português (PT-BR), mesmo que a questão esteja em inglês.
        - Mostre todos os passos do seu raciocínio, especialmente em cálculos.
        - Justifique por que as outras alternativas estão incorretas.
        - Use o sistema de votação interna para aumentar a confiança na sua resposta.

        Analise a seguinte questão:

        {text}

        IMPORTANTE: Comece sua resposta DIRETAMENTE com "Resposta correta: [LETRA]) [TEXTO]"
        """

        return enhanced_prompt
