#!/usr/bin/env python3
"""
Script de backup e migração para o sistema de logging do Python Question Helper.
Permite fazer backup, restaurar e migrar dados entre diferentes versões.
"""

import os
import sys
import shutil
import sqlite3
import json
import zipfile
from datetime import datetime
from typing import Dict, List, Optional

# Adiciona o diretório atual ao path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from question_logger import QuestionLogger
from config import DEBUG

class BackupMigration:
    """Sistema de backup e migração de dados."""
    
    def __init__(self, db_path: str = "question_logs.db", images_dir: str = "logged_images"):
        """
        Inicializa o sistema de backup.
        
        Args:
            db_path: Caminho do banco de dados
            images_dir: Diretório de imagens
        """
        self.db_path = db_path
        self.images_dir = images_dir
        self.backup_dir = "backups"
        
        # Cria diretório de backup se não existir
        os.makedirs(self.backup_dir, exist_ok=True)
        
        if DEBUG:
            print(f"BackupMigration inicializado - DB: {db_path}, Images: {images_dir}")
    
    def create_full_backup(self, backup_name: str = None) -> str:
        """
        Cria backup completo do sistema (banco + imagens).
        
        Args:
            backup_name: Nome do backup (opcional)
            
        Returns:
            Caminho do arquivo de backup criado
        """
        if not backup_name:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_name = f"question_helper_backup_{timestamp}"
        
        backup_path = os.path.join(self.backup_dir, f"{backup_name}.zip")
        
        try:
            with zipfile.ZipFile(backup_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                # Adiciona banco de dados
                if os.path.exists(self.db_path):
                    zipf.write(self.db_path, "question_logs.db")
                    print(f"✅ Banco de dados adicionado ao backup")
                
                # Adiciona imagens
                if os.path.exists(self.images_dir):
                    for root, dirs, files in os.walk(self.images_dir):
                        for file in files:
                            file_path = os.path.join(root, file)
                            arc_path = os.path.relpath(file_path, os.path.dirname(self.images_dir))
                            zipf.write(file_path, arc_path)
                    print(f"✅ Imagens adicionadas ao backup")
                
                # Adiciona metadados do backup
                metadata = {
                    'backup_date': datetime.now().isoformat(),
                    'version': '1.0',
                    'db_path': self.db_path,
                    'images_dir': self.images_dir,
                    'total_questions': self._count_questions(),
                    'backup_type': 'full'
                }
                
                zipf.writestr("backup_metadata.json", json.dumps(metadata, indent=2))
                print(f"✅ Metadados adicionados ao backup")
            
            file_size = os.path.getsize(backup_path) / (1024 * 1024)  # MB
            print(f"🎉 Backup completo criado: {backup_path}")
            print(f"📦 Tamanho: {file_size:.2f} MB")
            
            return backup_path
            
        except Exception as e:
            print(f"❌ Erro ao criar backup: {e}")
            if os.path.exists(backup_path):
                os.remove(backup_path)
            return None
    
    def create_data_only_backup(self, backup_name: str = None) -> str:
        """
        Cria backup apenas dos dados (sem imagens).
        
        Args:
            backup_name: Nome do backup (opcional)
            
        Returns:
            Caminho do arquivo de backup criado
        """
        if not backup_name:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_name = f"question_helper_data_{timestamp}"
        
        backup_path = os.path.join(self.backup_dir, f"{backup_name}.zip")
        
        try:
            with zipfile.ZipFile(backup_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                # Adiciona banco de dados
                if os.path.exists(self.db_path):
                    zipf.write(self.db_path, "question_logs.db")
                
                # Exporta dados em JSON para portabilidade
                logger = QuestionLogger(self.db_path, self.images_dir)
                json_export_path = "temp_export.json"
                
                if logger.export_data(json_export_path, 'json'):
                    zipf.write(json_export_path, "questions_export.json")
                    os.remove(json_export_path)
                
                # Adiciona metadados
                metadata = {
                    'backup_date': datetime.now().isoformat(),
                    'version': '1.0',
                    'backup_type': 'data_only',
                    'total_questions': self._count_questions()
                }
                
                zipf.writestr("backup_metadata.json", json.dumps(metadata, indent=2))
            
            file_size = os.path.getsize(backup_path) / 1024  # KB
            print(f"🎉 Backup de dados criado: {backup_path}")
            print(f"📦 Tamanho: {file_size:.2f} KB")
            
            return backup_path
            
        except Exception as e:
            print(f"❌ Erro ao criar backup de dados: {e}")
            if os.path.exists(backup_path):
                os.remove(backup_path)
            return None
    
    def restore_backup(self, backup_path: str, restore_images: bool = True) -> bool:
        """
        Restaura backup do sistema.
        
        Args:
            backup_path: Caminho do arquivo de backup
            restore_images: Se deve restaurar imagens também
            
        Returns:
            True se restauração foi bem-sucedida
        """
        if not os.path.exists(backup_path):
            print(f"❌ Arquivo de backup não encontrado: {backup_path}")
            return False
        
        try:
            # Cria backup dos dados atuais antes de restaurar
            current_backup = self.create_data_only_backup("pre_restore_backup")
            if current_backup:
                print(f"🔄 Backup atual criado antes da restauração: {current_backup}")
            
            with zipfile.ZipFile(backup_path, 'r') as zipf:
                # Lê metadados
                try:
                    metadata_content = zipf.read("backup_metadata.json")
                    metadata = json.loads(metadata_content.decode())
                    print(f"📋 Restaurando backup de {metadata.get('backup_date', 'data desconhecida')}")
                    print(f"📊 Total de questões: {metadata.get('total_questions', 'desconhecido')}")
                except:
                    print("⚠️ Metadados não encontrados, continuando restauração...")
                
                # Restaura banco de dados
                if "question_logs.db" in zipf.namelist():
                    # Backup do banco atual
                    if os.path.exists(self.db_path):
                        backup_db_path = f"{self.db_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                        shutil.copy2(self.db_path, backup_db_path)
                        print(f"🔄 Banco atual salvo em: {backup_db_path}")
                    
                    # Restaura novo banco
                    zipf.extract("question_logs.db", ".")
                    print(f"✅ Banco de dados restaurado")
                
                # Restaura imagens se solicitado
                if restore_images:
                    # Backup do diretório atual
                    if os.path.exists(self.images_dir):
                        backup_images_dir = f"{self.images_dir}_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                        shutil.copytree(self.images_dir, backup_images_dir)
                        print(f"🔄 Imagens atuais salvas em: {backup_images_dir}")
                        shutil.rmtree(self.images_dir)
                    
                    # Restaura imagens
                    for file_info in zipf.infolist():
                        if file_info.filename.startswith(self.images_dir + "/"):
                            zipf.extract(file_info, ".")
                    
                    print(f"✅ Imagens restauradas")
            
            print(f"🎉 Restauração concluída com sucesso!")
            return True
            
        except Exception as e:
            print(f"❌ Erro durante restauração: {e}")
            return False
    
    def migrate_to_new_version(self, target_version: str = "2.0") -> bool:
        """
        Migra dados para nova versão do sistema.
        
        Args:
            target_version: Versão alvo da migração
            
        Returns:
            True se migração foi bem-sucedida
        """
        print(f"🔄 Iniciando migração para versão {target_version}...")
        
        try:
            # Cria backup antes da migração
            backup_path = self.create_full_backup(f"pre_migration_v{target_version}")
            if not backup_path:
                print("❌ Falha ao criar backup pré-migração")
                return False
            
            # Aplica migrações específicas da versão
            if target_version == "2.0":
                success = self._migrate_to_v2()
            else:
                print(f"⚠️ Migração para versão {target_version} não implementada")
                return False
            
            if success:
                print(f"🎉 Migração para versão {target_version} concluída!")
                return True
            else:
                print(f"❌ Falha na migração para versão {target_version}")
                return False
                
        except Exception as e:
            print(f"❌ Erro durante migração: {e}")
            return False
    
    def _migrate_to_v2(self) -> bool:
        """Migração específica para versão 2.0."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Exemplo: adiciona nova coluna se não existir
                try:
                    cursor.execute("ALTER TABLE questions ADD COLUMN quality_score REAL")
                    print("✅ Coluna quality_score adicionada")
                except sqlite3.OperationalError:
                    print("ℹ️ Coluna quality_score já existe")
                
                # Exemplo: atualiza dados existentes
                cursor.execute("""
                    UPDATE questions 
                    SET quality_score = CASE 
                        WHEN is_correct = 1 THEN llm_confidence / 100.0
                        WHEN is_correct = 0 THEN (100 - llm_confidence) / 100.0
                        ELSE 0.5
                    END
                    WHERE quality_score IS NULL
                """)
                
                updated_rows = cursor.rowcount
                print(f"✅ {updated_rows} registros atualizados com quality_score")
                
                conn.commit()
                return True
                
        except Exception as e:
            print(f"❌ Erro na migração v2.0: {e}")
            return False
    
    def _count_questions(self) -> int:
        """Conta o número total de questões no banco."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT COUNT(*) FROM questions")
                return cursor.fetchone()[0]
        except:
            return 0
    
    def list_backups(self) -> List[Dict]:
        """
        Lista todos os backups disponíveis.
        
        Returns:
            Lista de informações dos backups
        """
        backups = []
        
        if not os.path.exists(self.backup_dir):
            return backups
        
        for filename in os.listdir(self.backup_dir):
            if filename.endswith('.zip'):
                backup_path = os.path.join(self.backup_dir, filename)
                
                try:
                    with zipfile.ZipFile(backup_path, 'r') as zipf:
                        # Tenta ler metadados
                        metadata = {}
                        if "backup_metadata.json" in zipf.namelist():
                            metadata_content = zipf.read("backup_metadata.json")
                            metadata = json.loads(metadata_content.decode())
                        
                        file_size = os.path.getsize(backup_path) / (1024 * 1024)  # MB
                        
                        backups.append({
                            'filename': filename,
                            'path': backup_path,
                            'size_mb': file_size,
                            'created': metadata.get('backup_date', 'desconhecido'),
                            'type': metadata.get('backup_type', 'desconhecido'),
                            'questions': metadata.get('total_questions', 0)
                        })
                        
                except Exception as e:
                    print(f"⚠️ Erro ao ler backup {filename}: {e}")
        
        # Ordena por data de criação (mais recente primeiro)
        backups.sort(key=lambda x: x['created'], reverse=True)
        return backups
    
    def cleanup_old_backups(self, keep_count: int = 5) -> int:
        """
        Remove backups antigos, mantendo apenas os mais recentes.
        
        Args:
            keep_count: Número de backups a manter
            
        Returns:
            Número de backups removidos
        """
        backups = self.list_backups()
        
        if len(backups) <= keep_count:
            print(f"ℹ️ Apenas {len(backups)} backups encontrados, nenhum removido")
            return 0
        
        removed_count = 0
        backups_to_remove = backups[keep_count:]
        
        for backup in backups_to_remove:
            try:
                os.remove(backup['path'])
                print(f"🗑️ Backup removido: {backup['filename']}")
                removed_count += 1
            except Exception as e:
                print(f"❌ Erro ao remover {backup['filename']}: {e}")
        
        print(f"✅ {removed_count} backups antigos removidos")
        return removed_count

def main():
    """Função principal para operações de backup."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Sistema de Backup e Migração")
    parser.add_argument('action', choices=['backup', 'restore', 'migrate', 'list', 'cleanup'],
                       help='Ação a executar')
    parser.add_argument('--name', help='Nome do backup')
    parser.add_argument('--path', help='Caminho do backup para restaurar')
    parser.add_argument('--data-only', action='store_true', help='Backup apenas dos dados')
    parser.add_argument('--no-images', action='store_true', help='Não restaurar imagens')
    parser.add_argument('--version', default='2.0', help='Versão alvo para migração')
    parser.add_argument('--keep', type=int, default=5, help='Número de backups a manter na limpeza')
    
    args = parser.parse_args()
    
    backup_system = BackupMigration()
    
    if args.action == 'backup':
        if args.data_only:
            backup_system.create_data_only_backup(args.name)
        else:
            backup_system.create_full_backup(args.name)
    
    elif args.action == 'restore':
        if not args.path:
            print("❌ Especifique o caminho do backup com --path")
            return
        backup_system.restore_backup(args.path, not args.no_images)
    
    elif args.action == 'migrate':
        backup_system.migrate_to_new_version(args.version)
    
    elif args.action == 'list':
        backups = backup_system.list_backups()
        if backups:
            print(f"\n📦 BACKUPS DISPONÍVEIS ({len(backups)}):")
            for backup in backups:
                print(f"   • {backup['filename']}")
                print(f"     Criado: {backup['created']}")
                print(f"     Tipo: {backup['type']}")
                print(f"     Tamanho: {backup['size_mb']:.2f} MB")
                print(f"     Questões: {backup['questions']}")
                print()
        else:
            print("ℹ️ Nenhum backup encontrado")
    
    elif args.action == 'cleanup':
        backup_system.cleanup_old_backups(args.keep)

if __name__ == "__main__":
    main()
