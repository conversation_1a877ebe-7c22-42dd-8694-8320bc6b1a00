"""
Módulo de processamento de áudio para o Python Question Helper.

Este módulo fornece funcionalidades para:
- Captura de áudio do sistema
- Conversão de fala para texto (Speech-to-Text)
- Processamento e melhoria de áudio
- Integração com múltiplos provedores de STT

Componentes principais:
- AudioProcessor: Captura e processa áudio
- SpeechToTextConverter: Converte fala para texto
"""

from .audio_processor import AudioProcessor
from .speech_to_text import SpeechToTextConverter

__all__ = [
    'AudioProcessor',
    'SpeechToTextConverter'
]

# Versão do módulo de áudio
__version__ = "1.0.0"
