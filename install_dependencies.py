#!/usr/bin/env python3
"""
Script para instalar dependências opcionais do sistema de logging.
"""

import subprocess
import sys
import importlib

def check_and_install_package(package_name, import_name=None):
    """
    Verifica se um pacote está instalado e instala se necessário.
    
    Args:
        package_name: Nome do pacote para pip install
        import_name: Nome para import (se diferente do package_name)
    """
    if import_name is None:
        import_name = package_name
    
    try:
        importlib.import_module(import_name)
        print(f"✅ {package_name} já está instalado")
        return True
    except ImportError:
        print(f"⚠️ {package_name} não encontrado. Instalando...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
            print(f"✅ {package_name} instalado com sucesso")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ Erro ao instalar {package_name}: {e}")
            return False

def main():
    """Instala todas as dependências opcionais."""
    print("🔧 INSTALANDO DEPENDÊNCIAS OPCIONAIS DO SISTEMA DE LOGGING")
    print("="*60)
    
    # Dependências opcionais para análise avançada
    optional_packages = [
        ("pandas", "pandas"),
        ("matplotlib", "matplotlib.pyplot"),
        ("seaborn", "seaborn"),
        ("numpy", "numpy"),  # Já deve estar instalado, mas verifica
    ]
    
    # Dependências para interface gráfica
    gui_packages = [
        ("PyQt5", "PyQt5.QtWidgets"),
    ]
    
    print("\n📊 INSTALANDO PACOTES PARA ANÁLISE DE DADOS:")
    success_count = 0
    for package, import_name in optional_packages:
        if check_and_install_package(package, import_name):
            success_count += 1
    
    print(f"\n🖥️ VERIFICANDO PACOTES PARA INTERFACE GRÁFICA:")
    gui_success = 0
    for package, import_name in gui_packages:
        if check_and_install_package(package, import_name):
            gui_success += 1
    
    print("\n" + "="*60)
    print("📋 RELATÓRIO DE INSTALAÇÃO:")
    print(f"✅ Pacotes de análise: {success_count}/{len(optional_packages)}")
    print(f"✅ Pacotes de interface: {gui_success}/{len(gui_packages)}")
    
    if success_count == len(optional_packages) and gui_success == len(gui_packages):
        print("\n🎉 TODAS AS DEPENDÊNCIAS INSTALADAS COM SUCESSO!")
        print("🔥 Funcionalidades disponíveis:")
        print("   • Análise avançada de dados (pandas)")
        print("   • Relatórios visuais (matplotlib/seaborn)")
        print("   • Interface de revisão (PyQt5)")
        print("\n✨ Execute 'python test_logging_system.py' para testar!")
    else:
        print("\n⚠️ ALGUMAS DEPENDÊNCIAS FALHARAM")
        print("📝 O sistema funcionará com funcionalidade limitada:")
        print("   • Logging básico: ✅ Sempre disponível")
        print("   • Estatísticas: ✅ Sempre disponível")
        print("   • Análise avançada: ❓ Depende do pandas")
        print("   • Relatórios visuais: ❓ Depende do matplotlib")
        print("   • Interface de revisão: ❓ Depende do PyQt5")
        
        print("\n🔧 SOLUÇÕES ALTERNATIVAS:")
        print("1. Instale manualmente: pip install pandas matplotlib seaborn PyQt5")
        print("2. Use apenas funcionalidades básicas (logging + estatísticas)")
        print("3. Execute em ambiente com dependências já instaladas")

if __name__ == "__main__":
    main()
