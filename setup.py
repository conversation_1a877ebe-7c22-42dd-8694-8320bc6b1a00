#!/usr/bin/env python3
"""
Setup script for Python Question Helper.

This script handles the installation and packaging of the Python Question Helper
application for commercial distribution.

Author: BoZolinO
Version: 1.0.0
License: Commercial
"""

import os
import sys
from pathlib import Path
from setuptools import setup, find_packages

# Ensure Python 3.8+
if sys.version_info < (3, 8):
    print("Python Question Helper requires Python 3.8 or higher.")
    sys.exit(1)

# Read version from config
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))
from src.core.config import VERSION, APP_NAME, APP_DESCRIPTION, APP_AUTHOR, APP_URL

# Read long description from README
def read_file(filename):
    """Read content from a file."""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            return f.read()
    except FileNotFoundError:
        return ""

long_description = read_file('README.md')
if not long_description:
    long_description = APP_DESCRIPTION

# Read requirements
def read_requirements(filename):
    """Read requirements from file."""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            return [line.strip() for line in f if line.strip() and not line.startswith('#')]
    except FileNotFoundError:
        return []

# Core requirements (always needed)
install_requires = [
    'pillow>=9.5.0',
    'pytesseract>=0.3.10',
    'pyautogui>=0.9.54',
    'opencv-python>=********',
    'numpy>=1.24.3',
    'requests>=2.31.0',
    'pynput>=1.7.6',
    'PyQt5>=5.15.9',
    'python-dotenv>=1.0.0',
    'openai>=1.12.0',
    'mss>=9.0.1',
    'google-generativeai>=0.3.2',
    'huggingface-hub>=0.20.3',
]

# Optional requirements for enhanced features
extras_require = {
    'analytics': [
        'pandas>=1.5.0',
        'matplotlib>=3.6.0',
        'seaborn>=0.12.0',
    ],
    'web': [
        'PyQtWebEngine>=5.15.6',
    ],
    'dev': [
        'pytest>=7.0.0',
        'pytest-cov>=4.0.0',
        'black>=22.0.0',
        'flake8>=5.0.0',
        'mypy>=1.0.0',
    ],
    'build': [
        'pyinstaller>=5.0.0',
        'cx-freeze>=6.0.0',
    ]
}

# All optional dependencies
extras_require['all'] = [
    dep for deps in extras_require.values() for dep in deps
]

# Platform-specific requirements
if sys.platform == "win32":
    install_requires.append('pywin32>=306')

# Entry points for command-line usage
entry_points = {
    'console_scripts': [
        'question-helper=src.core.main:main',
        'question-helper-review=src.ui.review_interface:main',
        'question-helper-analytics=src.analytics.analytics_module:main',
        'question-helper-health=system_health_check:main',
    ],
    'gui_scripts': [
        'question-helper-gui=src.core.main:main',
    ]
}

# Package data to include
package_data = {
    'src.monetization': ['ads/*.png', 'ads/*.md'],
    'src.core': ['*.md'],
    'src': ['*.md'],
}

# Data files to include in distribution
data_files = [
    ('docs', ['README.md', 'LICENSE', 'CHANGELOG.md']),
    ('docs/logging', ['LOGGING_DOCUMENTATION.md']),
    ('docs/tests', ['TEST_RESULTS_SUMMARY.md']),
]

# Classifiers for PyPI
classifiers = [
    'Development Status :: 5 - Production/Stable',
    'Intended Audience :: Education',
    'Intended Audience :: End Users/Desktop',
    'Topic :: Education',
    'Topic :: Scientific/Engineering :: Artificial Intelligence',
    'Topic :: Scientific/Engineering :: Image Recognition',
    'License :: Other/Proprietary License',
    'Operating System :: Microsoft :: Windows',
    'Operating System :: POSIX :: Linux',
    'Operating System :: MacOS',
    'Programming Language :: Python :: 3',
    'Programming Language :: Python :: 3.8',
    'Programming Language :: Python :: 3.9',
    'Programming Language :: Python :: 3.10',
    'Programming Language :: Python :: 3.11',
    'Programming Language :: Python :: 3.12',
    'Environment :: X11 Applications :: Qt',
    'Natural Language :: Portuguese (Brazilian)',
    'Natural Language :: English',
]

# Keywords for discovery
keywords = [
    'ocr', 'ai', 'education', 'questions', 'assistant', 'tesseract',
    'llm', 'vision', 'screen-capture', 'automation', 'learning',
    'portuguese', 'gemini', 'openai', 'huggingface'
]

setup(
    # Basic package information
    name=APP_NAME.lower().replace(' ', '-'),
    version=VERSION,
    author=APP_AUTHOR,
    author_email='<EMAIL>',
    description=APP_DESCRIPTION,
    long_description=long_description,
    long_description_content_type='text/markdown',
    url=APP_URL,
    
    # Package discovery
    packages=find_packages(),
    package_dir={'': '.'},
    package_data=package_data,
    data_files=data_files,
    include_package_data=True,
    
    # Dependencies
    install_requires=install_requires,
    extras_require=extras_require,
    python_requires='>=3.8',
    
    # Entry points
    entry_points=entry_points,
    
    # Metadata
    classifiers=classifiers,
    keywords=keywords,
    license='Commercial',
    platforms=['Windows', 'Linux', 'macOS'],
    
    # Additional metadata
    project_urls={
        'Documentation': f'{APP_URL}/wiki',
        'Source': APP_URL,
        'Tracker': f'{APP_URL}/issues',
        'Funding': 'https://github.com/sponsors/hiagodrigo',
    },
    
    # Options
    zip_safe=False,
    
    # Custom commands can be added here
    cmdclass={},
)

# Post-installation message
if __name__ == '__main__':
    print(f"""
    ╔══════════════════════════════════════════════════════════════╗
    ║                 {APP_NAME} v{VERSION}                 ║
    ║                     by {APP_AUTHOR}                          ║
    ╠══════════════════════════════════════════════════════════════╣
    ║                                                              ║
    ║  🎉 Installation completed successfully!                     ║
    ║                                                              ║
    ║  📖 Next steps:                                              ║
    ║     1. Configure your API keys in .env file                 ║
    ║     2. Install Tesseract OCR if not already installed       ║
    ║     3. Run: question-helper-health to verify setup          ║
    ║     4. Run: question-helper to start the application        ║
    ║                                                              ║
    ║  📚 Documentation: {APP_URL}/wiki           ║
    ║  🐛 Issues: {APP_URL}/issues                ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """)
