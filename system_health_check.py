#!/usr/bin/env python3
"""
Script de verificação de saúde do sistema de logging do Python Question Helper.
Identifica e corrige problemas automaticamente.
"""

import os
import sys
import sqlite3
import importlib
import subprocess
from datetime import datetime

# Adiciona o diretório atual ao path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

class SystemHealthCheck:
    """Verificador de saúde do sistema."""
    
    def __init__(self):
        self.issues = []
        self.fixes_applied = []
        
    def check_dependencies(self):
        """Verifica dependências obrigatórias e opcionais."""
        print("🔍 VERIFICANDO DEPENDÊNCIAS...")
        
        # Dependências obrigatórias
        required_deps = [
            ("numpy", "numpy"),
            ("cv2", "opencv-python"),
            ("PyQt5", "PyQt5"),
        ]
        
        # Dependências opcionais
        optional_deps = [
            ("pandas", "pandas"),
            ("matplotlib", "matplotlib"),
            ("seaborn", "seaborn"),
        ]
        
        missing_required = []
        missing_optional = []
        
        for import_name, package_name in required_deps:
            try:
                importlib.import_module(import_name)
                print(f"✅ {package_name} - OK")
            except ImportError:
                print(f"❌ {package_name} - FALTANDO (OBRIGATÓRIO)")
                missing_required.append(package_name)
                self.issues.append(f"Dependência obrigatória faltando: {package_name}")
        
        for import_name, package_name in optional_deps:
            try:
                importlib.import_module(import_name)
                print(f"✅ {package_name} - OK")
            except ImportError:
                print(f"⚠️ {package_name} - FALTANDO (OPCIONAL)")
                missing_optional.append(package_name)
                self.issues.append(f"Dependência opcional faltando: {package_name}")
        
        return missing_required, missing_optional
    
    def check_database(self):
        """Verifica integridade do banco de dados."""
        print("\n🔍 VERIFICANDO BANCO DE DADOS...")
        
        db_path = "question_logs.db"
        
        if not os.path.exists(db_path):
            print(f"⚠️ Banco de dados não existe: {db_path}")
            self.issues.append("Banco de dados não existe")
            return False
        
        try:
            with sqlite3.connect(db_path) as conn:
                cursor = conn.cursor()
                
                # Verifica se as tabelas existem
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = [row[0] for row in cursor.fetchall()]
                
                expected_tables = ['questions', 'pattern_analysis', 'system_metrics']
                missing_tables = [table for table in expected_tables if table not in tables]
                
                if missing_tables:
                    print(f"❌ Tabelas faltando: {missing_tables}")
                    self.issues.append(f"Tabelas faltando no banco: {missing_tables}")
                    return False
                
                # Verifica estrutura da tabela principal
                cursor.execute("PRAGMA table_info(questions)")
                columns = [row[1] for row in cursor.fetchall()]
                
                required_columns = [
                    'id', 'session_id', 'timestamp', 'question_hash', 
                    'domain', 'question_type', 'ocr_text', 'llm_response',
                    'llm_provider', 'llm_model', 'llm_confidence'
                ]
                
                missing_columns = [col for col in required_columns if col not in columns]
                
                if missing_columns:
                    print(f"❌ Colunas faltando: {missing_columns}")
                    self.issues.append(f"Colunas faltando na tabela questions: {missing_columns}")
                    return False
                
                # Verifica se há dados
                cursor.execute("SELECT COUNT(*) FROM questions")
                count = cursor.fetchone()[0]
                
                print(f"✅ Banco de dados OK - {count} questões registradas")
                return True
                
        except Exception as e:
            print(f"❌ Erro ao verificar banco: {e}")
            self.issues.append(f"Erro no banco de dados: {e}")
            return False
    
    def check_modules(self):
        """Verifica se os módulos do sistema podem ser importados."""
        print("\n🔍 VERIFICANDO MÓDULOS DO SISTEMA...")
        
        modules = [
            "question_logger",
            "analytics_module", 
            "review_interface",
            "main"
        ]
        
        all_ok = True
        
        for module_name in modules:
            try:
                importlib.import_module(module_name)
                print(f"✅ {module_name} - OK")
            except ImportError as e:
                print(f"❌ {module_name} - ERRO: {e}")
                self.issues.append(f"Erro ao importar {module_name}: {e}")
                all_ok = False
            except Exception as e:
                print(f"⚠️ {module_name} - AVISO: {e}")
                self.issues.append(f"Aviso em {module_name}: {e}")
        
        return all_ok
    
    def check_file_permissions(self):
        """Verifica permissões de arquivo."""
        print("\n🔍 VERIFICANDO PERMISSÕES...")
        
        files_to_check = [
            "question_logs.db",
            "logged_images/",
            "backups/"
        ]
        
        all_ok = True
        
        for file_path in files_to_check:
            if os.path.exists(file_path):
                if os.access(file_path, os.R_OK | os.W_OK):
                    print(f"✅ {file_path} - Permissões OK")
                else:
                    print(f"❌ {file_path} - Sem permissões de leitura/escrita")
                    self.issues.append(f"Permissões insuficientes: {file_path}")
                    all_ok = False
            else:
                print(f"ℹ️ {file_path} - Não existe (será criado quando necessário)")
        
        return all_ok
    
    def check_config(self):
        """Verifica configurações."""
        print("\n🔍 VERIFICANDO CONFIGURAÇÕES...")
        
        try:
            from config import DEBUG, TESSERACT_PATH
            print(f"✅ config.py importado - DEBUG: {DEBUG}")
            
            # Verifica Tesseract
            if os.path.exists(TESSERACT_PATH):
                print(f"✅ Tesseract encontrado: {TESSERACT_PATH}")
            else:
                print(f"⚠️ Tesseract não encontrado: {TESSERACT_PATH}")
                self.issues.append(f"Tesseract não encontrado em: {TESSERACT_PATH}")
            
            return True
            
        except ImportError as e:
            print(f"❌ Erro ao importar config.py: {e}")
            self.issues.append(f"Erro na configuração: {e}")
            return False
    
    def auto_fix_issues(self):
        """Tenta corrigir problemas automaticamente."""
        print("\n🔧 TENTANDO CORRIGIR PROBLEMAS...")
        
        # Cria diretórios necessários
        dirs_to_create = ["logged_images", "backups", "test_analytics_output"]
        
        for dir_name in dirs_to_create:
            if not os.path.exists(dir_name):
                try:
                    os.makedirs(dir_name)
                    print(f"✅ Diretório criado: {dir_name}")
                    self.fixes_applied.append(f"Criado diretório: {dir_name}")
                except Exception as e:
                    print(f"❌ Erro ao criar {dir_name}: {e}")
        
        # Inicializa banco de dados se não existir
        if not os.path.exists("question_logs.db"):
            try:
                from question_logger import QuestionLogger
                logger = QuestionLogger()
                print("✅ Banco de dados inicializado")
                self.fixes_applied.append("Banco de dados inicializado")
            except Exception as e:
                print(f"❌ Erro ao inicializar banco: {e}")
        
        # Remove arquivos temporários antigos
        temp_files = [
            "current_capture.png",
            "original_capture.png", 
            "cropped_question.png",
            "enhanced_for_ocr.png",
            "extracted_text.txt",
            "vision_capture.png",
            "test_question_image.png",
            "test_export.csv",
            "test_export.json"
        ]
        
        cleaned_files = 0
        for temp_file in temp_files:
            if os.path.exists(temp_file):
                try:
                    os.remove(temp_file)
                    cleaned_files += 1
                except:
                    pass
        
        if cleaned_files > 0:
            print(f"✅ {cleaned_files} arquivos temporários removidos")
            self.fixes_applied.append(f"Removidos {cleaned_files} arquivos temporários")
    
    def run_full_check(self):
        """Executa verificação completa do sistema."""
        print("🏥 VERIFICAÇÃO DE SAÚDE DO SISTEMA DE LOGGING")
        print("="*60)
        
        start_time = datetime.now()
        
        # Executa todas as verificações
        deps_ok = self.check_dependencies()
        db_ok = self.check_database()
        modules_ok = self.check_modules()
        perms_ok = self.check_file_permissions()
        config_ok = self.check_config()
        
        # Tenta corrigir problemas
        self.auto_fix_issues()
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        print("\n" + "="*60)
        print("📋 RELATÓRIO DE SAÚDE:")
        print(f"⏱️ Tempo de verificação: {duration:.2f}s")
        print(f"🔍 Problemas encontrados: {len(self.issues)}")
        print(f"🔧 Correções aplicadas: {len(self.fixes_applied)}")
        
        if self.issues:
            print(f"\n⚠️ PROBLEMAS IDENTIFICADOS:")
            for i, issue in enumerate(self.issues, 1):
                print(f"   {i}. {issue}")
        
        if self.fixes_applied:
            print(f"\n✅ CORREÇÕES APLICADAS:")
            for i, fix in enumerate(self.fixes_applied, 1):
                print(f"   {i}. {fix}")
        
        # Avaliação geral
        critical_issues = [issue for issue in self.issues if "obrigatório" in issue.lower() or "erro" in issue.lower()]
        
        if not critical_issues:
            print(f"\n🎉 SISTEMA SAUDÁVEL!")
            print("✅ Todos os componentes críticos estão funcionando")
            print("✅ Sistema pronto para uso em produção")
            return True
        else:
            print(f"\n⚠️ ATENÇÃO NECESSÁRIA!")
            print(f"❌ {len(critical_issues)} problema(s) crítico(s) encontrado(s)")
            print("🔧 Execute as correções sugeridas antes de usar o sistema")
            return False
    
    def suggest_fixes(self):
        """Sugere correções para problemas encontrados."""
        if not self.issues:
            return
        
        print(f"\n💡 SUGESTÕES DE CORREÇÃO:")
        
        for issue in self.issues:
            if "faltando" in issue and "pandas" in issue:
                print("   • Execute: pip install pandas matplotlib seaborn")
            elif "faltando" in issue and "PyQt5" in issue:
                print("   • Execute: pip install PyQt5")
            elif "faltando" in issue and "opencv" in issue:
                print("   • Execute: pip install opencv-python")
            elif "Tesseract" in issue:
                print("   • Instale o Tesseract OCR e atualize o caminho em config.py")
            elif "banco" in issue.lower():
                print("   • Execute: python test_logging_system.py para recriar o banco")
            elif "permissões" in issue.lower():
                print("   • Verifique permissões de escrita no diretório")

def main():
    """Função principal."""
    checker = SystemHealthCheck()
    
    # Executa verificação completa
    system_healthy = checker.run_full_check()
    
    # Sugere correções se necessário
    if not system_healthy:
        checker.suggest_fixes()
        
        print(f"\n🔧 PRÓXIMOS PASSOS:")
        print("1. Execute: python install_dependencies.py")
        print("2. Corrija os problemas listados acima")
        print("3. Execute este script novamente para verificar")
        print("4. Execute: python test_logging_system.py para testar")
    else:
        print(f"\n🚀 SISTEMA PRONTO!")
        print("• Execute: python main.py para usar o programa")
        print("• Execute: python review_interface.py para revisar questões")
        print("• Execute: python test_logging_system.py para testar funcionalidades")

if __name__ == "__main__":
    main()
