"""
Configurações para o sistema de monitoramento de mudanças.
"""

# =============================================================================
# CONFIGURAÇÕES DE DETECÇÃO DE MUDANÇAS
# =============================================================================

class MonitorConfig:
    """
    Configurações para o sistema de monitoramento de mudanças na tela.
    """
    
    # Configurações gerais
    CHECK_INTERVAL = 3.0  # Intervalo entre verificações (segundos)
    DETECT_ANY_CHANGE = True  # Detecta qualquer mudança visual por padrão
    
    # Thresholds de sensibilidade para diferentes elementos
    PROGRESS_BAR_THRESHOLD = 0.05  # 5% de mudança na barra de progresso
    TIMER_THRESHOLD = 5  # 5 segundos de diferença no timer
    TEXT_THRESHOLD = 0.3  # 30% de mudança no texto da questão
    ALTERNATIVES_THRESHOLD = 0.2  # 20% de mudança nas alternativas
    VISUAL_THRESHOLD = 0.15  # 15% de mudança visual geral
    
    # Configurações de regiões da tela (percentuais)
    TIMER_REGION = {
        'top': 0.0,     # 0% do topo
        'bottom': 0.2,  # 20% do topo
        'left': 0.7,    # 70% da esquerda
        'right': 1.0    # 100% da direita
    }
    
    QUESTION_REGION = {
        'top': 0.2,     # 20% do topo
        'bottom': 0.7,  # 70% do topo
        'left': 0.1,    # 10% da esquerda
        'right': 0.9    # 90% da direita
    }
    
    ALTERNATIVES_REGION = {
        'top': 0.4,     # 40% do topo
        'bottom': 0.9,  # 90% do topo
        'left': 0.1,    # 10% da esquerda
        'right': 0.9    # 90% da direita
    }

    # Regiões específicas para questões de relacionar colunas
    LEFT_COLUMN_REGION = {
        'top': 0.3,     # 30% do topo
        'bottom': 0.8,  # 80% do topo
        'left': 0.1,    # 10% da esquerda
        'right': 0.45   # 45% da direita
    }

    RIGHT_COLUMN_REGION = {
        'top': 0.3,     # 30% do topo
        'bottom': 0.8,  # 80% do topo
        'left': 0.55,   # 55% da esquerda
        'right': 0.9    # 90% da direita
    }

    # Região central para conexões/linhas
    CONNECTIONS_REGION = {
        'top': 0.3,     # 30% do topo
        'bottom': 0.8,  # 80% do topo
        'left': 0.4,    # 40% da esquerda
        'right': 0.6    # 60% da direita
    }

    # Região para itens arrastáveis
    DRAG_ITEMS_REGION = {
        'top': 0.3,     # 30% do topo
        'bottom': 0.6,  # 60% do topo
        'left': 0.1,    # 10% da esquerda
        'right': 0.9    # 90% da direita
    }

    # Região para zonas de soltar
    DROP_ZONES_REGION = {
        'top': 0.6,     # 60% do topo
        'bottom': 0.9,  # 90% do topo
        'left': 0.1,    # 10% da esquerda
        'right': 0.9    # 90% da direita
    }

    # Região para campos de entrada
    INPUT_FIELDS_REGION = {
        'top': 0.3,     # 30% do topo
        'bottom': 0.8,  # 80% do topo
        'left': 0.1,    # 10% da esquerda
        'right': 0.9    # 90% da direita
    }

    # Região para área de texto livre
    TEXT_AREA_REGION = {
        'top': 0.4,     # 40% do topo
        'bottom': 0.9,  # 90% do topo
        'left': 0.1,    # 10% da esquerda
        'right': 0.9    # 90% da direita
    }

    # Região para tela completa (auto-detect)
    FULL_SCREEN_REGION = {
        'top': 0.0,     # 0% do topo
        'bottom': 1.0,  # 100% do topo
        'left': 0.0,    # 0% da esquerda
        'right': 1.0    # 100% da direita
    }
    
    # Configurações de cores para detecção de barra de progresso (HSV)
    PROGRESS_BAR_COLORS = {
        'blue': {
            'lower': [100, 50, 50],
            'upper': [130, 255, 255]
        },
        'green': {
            'lower': [40, 50, 50],
            'upper': [80, 255, 255]
        },
        'orange': {
            'lower': [10, 50, 50],
            'upper': [30, 255, 255]
        }
    }
    
    # Filtros para barras de progresso
    MIN_PROGRESS_BAR_WIDTH = 50
    MIN_PROGRESS_BAR_HEIGHT = 5
    
    # Configurações de OCR
    OCR_CONFIG = {
        'timer': '--psm 7 -c tessedit_char_whitelist=0123456789:',
        'question': '--psm 6',
        'alternatives': '--psm 6'
    }
    
    # Padrões de tempo para detecção de timer
    TIME_PATTERNS = [
        r'(\d{1,2}):(\d{2}):(\d{2})',  # HH:MM:SS
        r'(\d{1,2}):(\d{2})',          # MM:SS
        r'(\d{2,4})'                   # Apenas segundos
    ]
    
    # Padrões para detecção de diferentes tipos de questão
    MULTIPLE_CHOICE_PATTERN = r'[A-E]\s*[\)\.]?\s*[^\n\r]+'
    MATCHING_PATTERN = r'(\d+[\.\)]\s*[^\n\r]+)|([IVX]+[\.\)]\s*[^\n\r]+)'
    FILL_BLANK_PATTERN = r'_{3,}|__+|\[.*?\]|\(\s*\)'
    DRAG_DROP_INDICATORS = ['drag', 'drop', 'arrastar', 'soltar', 'mover']
    ORDERING_INDICATORS = ['order', 'sequence', 'ordenar', 'sequência', 'primeiro', 'segundo']

    MIN_ALTERNATIVES = 2  # Mínimo de alternativas para considerar válido
    MIN_MATCHING_ITEMS = 3  # Mínimo de itens para questão de relacionar
    
    # Configurações de debug
    SAVE_DEBUG_IMAGES = True
    DEBUG_IMAGE_PREFIX = "monitor_debug_"
    
    @classmethod
    def get_optimized_config(cls, scenario="general"):
        """
        Retorna configurações otimizadas para diferentes cenários.
        
        Args:
            scenario: Tipo de cenário ("general", "sensitive", "fast", "conservative")
            
        Returns:
            Dict com configurações otimizadas
        """
        configs = {
            "general": {
                "check_interval": 3.0,
                "progress_threshold": 0.05,
                "timer_threshold": 5,
                "text_threshold": 0.3,
                "alternatives_threshold": 0.2,
                "visual_threshold": 0.15
            },
            "sensitive": {
                "check_interval": 2.0,
                "progress_threshold": 0.02,
                "timer_threshold": 2,
                "text_threshold": 0.1,
                "alternatives_threshold": 0.1,
                "visual_threshold": 0.05
            },
            "fast": {
                "check_interval": 1.5,
                "progress_threshold": 0.1,
                "timer_threshold": 10,
                "text_threshold": 0.5,
                "alternatives_threshold": 0.3,
                "visual_threshold": 0.25
            },
            "conservative": {
                "check_interval": 5.0,
                "progress_threshold": 0.1,
                "timer_threshold": 10,
                "text_threshold": 0.5,
                "alternatives_threshold": 0.4,
                "visual_threshold": 0.3
            }
        }
        
        return configs.get(scenario, configs["general"])
    
    @classmethod
    def get_region_coordinates(cls, image_shape, region_name):
        """
        Calcula as coordenadas reais de uma região baseada no tamanho da imagem.

        Args:
            image_shape: Tupla (height, width) da imagem
            region_name: Nome da região

        Returns:
            Tupla (y1, y2, x1, x2) com as coordenadas da região
        """
        height, width = image_shape[:2]

        regions = {
            "timer": cls.TIMER_REGION,
            "question": cls.QUESTION_REGION,
            "alternatives": cls.ALTERNATIVES_REGION,
            "left_column": cls.LEFT_COLUMN_REGION,
            "right_column": cls.RIGHT_COLUMN_REGION,
            "connections": cls.CONNECTIONS_REGION,
            "drag_items": cls.DRAG_ITEMS_REGION,
            "drop_zones": cls.DROP_ZONES_REGION,
            "input_fields": cls.INPUT_FIELDS_REGION,
            "text_area": cls.TEXT_AREA_REGION,
            "items": cls.ALTERNATIVES_REGION,  # Alias para itens de ordenação
            "sequence_area": cls.ALTERNATIVES_REGION,  # Alias para área de sequência
            "full_screen": cls.FULL_SCREEN_REGION
        }

        if region_name not in regions:
            raise ValueError(f"Região '{region_name}' não encontrada. Regiões disponíveis: {list(regions.keys())}")

        region = regions[region_name]

        y1 = int(height * region['top'])
        y2 = int(height * region['bottom'])
        x1 = int(width * region['left'])
        x2 = int(width * region['right'])

        return (y1, y2, x1, x2)

# Configuração padrão global
DEFAULT_CONFIG = MonitorConfig.get_optimized_config("general")

# Configurações específicas para diferentes tipos de questões
QUESTION_TYPE_CONFIGS = {
    "multiple_choice": {
        "focus_alternatives": True,
        "alternatives_threshold": 0.15,
        "text_threshold": 0.25,
        "visual_threshold": 0.15,
        "interactive_threshold": 0.1,
        "detection_regions": ["question", "alternatives"]
    },
    "true_false": {
        "focus_alternatives": True,
        "alternatives_threshold": 0.3,
        "text_threshold": 0.2,
        "visual_threshold": 0.15,
        "interactive_threshold": 0.1,
        "detection_regions": ["question", "alternatives"]
    },
    "matching": {
        "focus_alternatives": False,
        "text_threshold": 0.1,
        "visual_threshold": 0.08,
        "interactive_threshold": 0.05,
        "connection_threshold": 0.1,
        "detection_regions": ["question", "left_column", "right_column", "connections"]
    },
    "drag_drop": {
        "focus_alternatives": False,
        "text_threshold": 0.12,
        "visual_threshold": 0.1,
        "interactive_threshold": 0.05,
        "position_threshold": 0.08,
        "detection_regions": ["question", "drag_items", "drop_zones"]
    },
    "ordering": {
        "focus_alternatives": False,
        "text_threshold": 0.15,
        "visual_threshold": 0.1,
        "interactive_threshold": 0.06,
        "sequence_threshold": 0.1,
        "detection_regions": ["question", "items", "sequence_area"]
    },
    "fill_blank": {
        "focus_alternatives": False,
        "text_threshold": 0.08,
        "visual_threshold": 0.06,
        "interactive_threshold": 0.05,
        "input_threshold": 0.1,
        "detection_regions": ["question", "input_fields"]
    },
    "essay": {
        "focus_alternatives": False,
        "text_threshold": 0.05,
        "visual_threshold": 0.03,
        "interactive_threshold": 0.02,
        "content_threshold": 0.1,
        "detection_regions": ["question", "text_area"]
    },
    "auto_detect": {
        "focus_alternatives": True,
        "text_threshold": 0.15,
        "visual_threshold": 0.1,
        "interactive_threshold": 0.08,
        "detection_regions": ["full_screen"]
    }
}

def get_config_for_question_type(question_type="multiple_choice"):
    """
    Retorna configurações otimizadas para um tipo específico de questão.
    
    Args:
        question_type: Tipo da questão
        
    Returns:
        Dict com configurações específicas
    """
    base_config = DEFAULT_CONFIG.copy()
    specific_config = QUESTION_TYPE_CONFIGS.get(question_type, {})
    
    # Mescla as configurações
    base_config.update(specific_config)
    
    return base_config
