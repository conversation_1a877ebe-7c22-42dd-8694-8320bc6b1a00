"""
Configuration settings for the Python Question Helper application.

This module contains all configuration constants and environment variable handling
for the Python Question Helper system.

Author: BoZolinO
Version: 1.0.0
License: Commercial
"""

import os
from pathlib import Path
from typing import List, Optional
from dotenv import load_dotenv

# Application metadata
VERSION = "1.0.0"
APP_NAME = "Python Question Helper"
APP_AUTHOR = "BoZolinO"
APP_DESCRIPTION = "Sistema Inteligente de Assistência para Questões"
APP_URL = "https://github.com/hiagodrigo/Python_Question_Helper"

# Load environment variables from .env file
load_dotenv()

# =============================================================================
# TESSERACT OCR CONFIGURATION
# =============================================================================

def _find_tesseract_path() -> Optional[str]:
    """
    Automatically finds Tesseract installation path.

    Returns:
        Path to tesseract executable or None if not found
    """
    common_paths = [
        r"C:\Program Files\Tesseract-OCR\tesseract.exe",
        r"C:\Program Files (x86)\Tesseract-OCR\tesseract.exe",
        r"C:\Users\<USER>\Tesseract-OCR\tesseract.exe",
        r"C:\Users\<USER>\AppData\Local\Programs\Tesseract-OCR\tesseract.exe",
        "/usr/bin/tesseract",  # Linux
        "/usr/local/bin/tesseract",  # macOS
        "tesseract"  # System PATH
    ]

    for path in common_paths:
        if Path(path).exists() or path == "tesseract":
            return path

    return None

TESSERACT_PATH = os.getenv("TESSERACT_PATH") or _find_tesseract_path()

# =============================================================================
# LLM CONFIGURATION
# =============================================================================

# Primary LLM settings
LLM_API_KEY = os.getenv("LLM_API_KEY")
LLM_MODEL = os.getenv("LLM_MODEL", "google/gemini-2.0-flash-exp:free")
LLM_BASE_URL = os.getenv("LLM_BASE_URL", "https://openrouter.ai/api/v1")
LLM_SITE_URL = os.getenv("LLM_SITE_URL", "http://localhost")
LLM_SITE_NAME = os.getenv("LLM_SITE_NAME", APP_NAME)

# Provider configuration
LLM_PROVIDER = os.getenv("LLM_PROVIDER", "huggingface")
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")
HUGGINGFACE_API_KEY = os.getenv("HUGGINGFACE_API_KEY")
OLLAMA_BASE_URL = os.getenv("OLLAMA_BASE_URL", "http://localhost:11434/api")

# =============================================================================
# UI CONFIGURATION
# =============================================================================

# Overlay settings
OVERLAY_OPACITY = float(os.getenv("OVERLAY_OPACITY", "0.85"))
OVERLAY_WIDTH = int(os.getenv("OVERLAY_WIDTH", "350"))
OVERLAY_HEIGHT = int(os.getenv("OVERLAY_HEIGHT", "400"))
OVERLAY_POSITION_X = int(os.getenv("OVERLAY_POSITION_X", "10"))
OVERLAY_POSITION_Y = int(os.getenv("OVERLAY_POSITION_Y", "10"))

# =============================================================================
# PROCESSING CONFIGURATION
# =============================================================================

# Screen capture settings
CAPTURE_INTERVAL = float(os.getenv("CAPTURE_INTERVAL", "2.0"))
BROWSER_TITLE_CONTAINS = os.getenv("BROWSER_TITLE_CONTAINS", "Question")

# OCR settings
OCR_LANGUAGES = os.getenv("OCR_LANGUAGES", "por+eng").split("+")
OCR_CONFIDENCE_THRESHOLD = int(os.getenv("OCR_CONFIDENCE_THRESHOLD", "60"))

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================

# Database settings
DB_PATH = os.getenv("DB_PATH", "question_logs.db")
IMAGES_DIR = os.getenv("IMAGES_DIR", "logged_images")
BACKUPS_DIR = os.getenv("BACKUPS_DIR", "backups")

# Logging levels
DEBUG = os.getenv("DEBUG", "False").lower() == "true"
LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO" if not DEBUG else "DEBUG")

# =============================================================================
# MONETIZATION CONFIGURATION
# =============================================================================

# Advertisement settings
ADS_ENABLED = os.getenv("ADS_ENABLED", "False").lower() == "true"
ADS_CLIENT_ID = os.getenv("ADS_CLIENT_ID", "ca-pub-6285721618252255")
ADS_BANNER_SLOT = os.getenv("ADS_BANNER_SLOT", "**********")
ADS_RECTANGLE_SLOT = os.getenv("ADS_RECTANGLE_SLOT", "**********")
ADS_MULTIPLEX_SLOT = os.getenv("ADS_MULTIPLEX_SLOT", "**********")

# Licensing
LICENSE_TYPE = os.getenv("LICENSE_TYPE", "commercial")
LICENSE_KEY = os.getenv("LICENSE_KEY")

# =============================================================================
# PERFORMANCE CONFIGURATION
# =============================================================================

# Processing timeouts
LLM_TIMEOUT = int(os.getenv("LLM_TIMEOUT", "30"))
OCR_TIMEOUT = int(os.getenv("OCR_TIMEOUT", "10"))
CAPTURE_TIMEOUT = int(os.getenv("CAPTURE_TIMEOUT", "5"))

# Memory management
MAX_CACHED_IMAGES = int(os.getenv("MAX_CACHED_IMAGES", "10"))
MAX_LOG_ENTRIES = int(os.getenv("MAX_LOG_ENTRIES", "1000"))

# =============================================================================
# VALIDATION AND STARTUP CHECKS
# =============================================================================

def validate_configuration() -> List[str]:
    """
    Validates the current configuration and returns any issues found.

    Returns:
        List of configuration issues (empty if all valid)
    """
    issues = []

    # Check Tesseract
    if not TESSERACT_PATH:
        issues.append("Tesseract OCR not found. Please install Tesseract.")

    # Check LLM configuration
    if not any([LLM_API_KEY, GEMINI_API_KEY, HUGGINGFACE_API_KEY]):
        issues.append("No LLM API keys configured. At least one is required.")

    # Check directories
    for directory in [IMAGES_DIR, BACKUPS_DIR]:
        try:
            Path(directory).mkdir(parents=True, exist_ok=True)
        except Exception as e:
            issues.append(f"Cannot create directory {directory}: {e}")

    return issues

# Validate configuration on import
if __name__ != "__main__":
    _config_issues = validate_configuration()
    if _config_issues and DEBUG:
        print("Configuration issues found:")
        for issue in _config_issues:
            print(f"  - {issue}")

# =============================================================================
# EXPORTS
# =============================================================================

__all__ = [
    # Application metadata
    'VERSION', 'APP_NAME', 'APP_AUTHOR', 'APP_DESCRIPTION', 'APP_URL',

    # Tesseract
    'TESSERACT_PATH',

    # LLM
    'LLM_API_KEY', 'LLM_MODEL', 'LLM_BASE_URL', 'LLM_SITE_URL', 'LLM_SITE_NAME',
    'LLM_PROVIDER', 'GEMINI_API_KEY', 'HUGGINGFACE_API_KEY', 'OLLAMA_BASE_URL',

    # UI
    'OVERLAY_OPACITY', 'OVERLAY_WIDTH', 'OVERLAY_HEIGHT',
    'OVERLAY_POSITION_X', 'OVERLAY_POSITION_Y',

    # Processing
    'CAPTURE_INTERVAL', 'BROWSER_TITLE_CONTAINS', 'OCR_LANGUAGES', 'OCR_CONFIDENCE_THRESHOLD',

    # Logging
    'DB_PATH', 'IMAGES_DIR', 'BACKUPS_DIR', 'DEBUG', 'LOG_LEVEL',

    # Monetization
    'ADS_ENABLED', 'ADS_CLIENT_ID', 'ADS_BANNER_SLOT', 'ADS_RECTANGLE_SLOT', 'ADS_MULTIPLEX_SLOT',
    'LICENSE_TYPE', 'LICENSE_KEY',

    # Performance
    'LLM_TIMEOUT', 'OCR_TIMEOUT', 'CAPTURE_TIMEOUT', 'MAX_CACHED_IMAGES', 'MAX_LOG_ENTRIES',

    # Validation
    'validate_configuration'
]
